HOST_NAME=0.0.0.0
HOST_PORT=8001
QUARKUS_DATASOURCE_USERNAME=dev_ap2t
QUARKUS_DATASOURCE_PASSWORD=welcome1
QUARKUS_DATASOURCE_JDBC_URL=******************************************
QUARKUS_HIBERNATE_ORM_DATABASE_GENERATION=none
DB_REACTIVE_URL=vertx-reactive:postgresql://***********:5432/ap2t_db
CONDUCTOR_API=http://***********:8081
CONDUCTOR_URL=http://***********:5000
COMMUNITY_API=http://***********:8081
BILLING_API=http://***********:8003
SECURITY_API=http://***********:8006
CRM_API=http://***********:30082
PRINTING_API=http://***********:8005
FSM_API=http://***********:8004
MASTER_API=http://***********:8011
WORKFLOW_API=http://***********:8012
REDIS_HOST=redis://***********:6379
REDIS_PASSWORD=
READ_TIMEOUT=270000
CONNECT_TIMEOUT=160000
KAFKA_SERVER=***********:9092,***********:9093,***********:9094
KAFKA_SERVER2=***********:9093
KAFKA_SERVER3=***********:9094
GROUP_ID1=billing-runner-16
GROUP_ID2=billing-mutasi-16
RECALCULATE_ROW=60
;UNMESHED_TOKEN=eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDIyMkFBQSIsImtpZCI6Imluc18yZWpmbTE0UkVNeVVQMXl5dUpWVTE4bEFSTXEiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.sstb5hUqKmZm3TeOHT9kzDvJwEsaBhoGYasA3cU3MYON1HQ-aFJ2-2uMBYWyR7_VjiMFJnMqddJxkCUwff7CD65lBKBvenDi1BCwHWTXcNQy2jJF4mMDZnZFlxJiBsl_w3QtiPqbPOYV6aaWQp32iMv4aY3iZck6NpE7jISN_O-FhPc5dWc6KM1qIgwnoZ5hjipNd4KNBiiJpBfhF3tUX2A5lfUqwh5XR1wm2JnBo-WsgpIm1xd4GfEoAihOR2Xgrg3SKYK2dh88c7yx16vj6_-A1G4CtUbcI-4BCDmWPFGyDmC3MFU0tnYkvmaQuH_B450g3pR5F9ec4hnKERGU1Q
;UNMESHED_API=http://**********:30080
GRPC_SERVER=0.0.0.0
GRPC_PORT=9001
BATCH_SIZE=20
TOPIC_ID=job-hitung-billing
GROUP_ID=job-hitung-billing-03
EMAIL_FROM=<EMAIL>
EMAIL_HOST=***********
EMAIL_PORT=465
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=icon+123@
EMAIL_START_TLS=REQUIRED
EMAIL_METHOD=DIGEST-MD5 CRAM-SHA256 CRAM-SHA1 CRAM-MD5 PLAIN LOGIN
EMAIL_ALERT=<EMAIL>
