package com.iconplus.ap2t.fso.legacy.tasrip;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/1/2025
 */
@Embeddable
public class TasripId implements Serializable {

    @Size(max = 5)
    @NotEmpty
    @Column(length = 5, name = "TARIF", nullable = false)
    public String tarif;

    @Size(max = 5)
    @NotEmpty
    @Column(length = 5, name = "KDPT", nullable = false)
    public String kdPt;

    @Size(max = 20)
    @NotEmpty
    @Column(length = 20, name = "PEMBTRF", nullable = false)
    public String pembTarif;

    @Size(max = 10)
    @NotEmpty
    @Column(length = 10, name = "UNIT", nullable = false)
    public String unit;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        TasripId tasripId = (TasripId) o;
        return Objects.equals(tarif, tasripId.tarif) && Objects.equals(kdPt, tasripId.kdPt)
                && Objects.equals(pembTarif, tasripId.pembTarif) && Objects.equals(unit, tasripId.unit);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tarif, kdPt, pembTarif, unit);
    }
}
