package com.iconplus.ap2t.fso.legacy.wo;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/4/2025
 */
public record WorkOrderDto(String noAgenda,
                           Date tanggalAgenda,
                           Date tanggalLunas,
                           Date tanggalIsiMaterial,
                           String status,
                           String statusName,
                           String unitUp,
                           String namaUnitUp,
                           String unitAp,
                           String namaUnitAp,
                           String unitUpi,
                           String namaUnitUpi,
                           String idPelanggan,
                           String namaPelanggan,
                           String alamatPelanggan,
                           String noTelpPelanggan,
                           String noHpPelanggan,
                           String tarif,
                           String tarifLama,
                           String daya,
                           String dayaLama,
                           String noSuratPerintahKerja,
                           String noBeritaAcara,
                           String jenisTransaksi,
                           String kodePaket,
                           String namaPaket,
                           String noWorkOrder,
                           Date tanggalWorkOrder,
                           String kodeVendor,
                           String namaVendor,
                           Date tanggalCatat,
                           String userId,
                           String namaUser,
                           String noMeterKwh,
                           String kodePembMeterKwh,
                           String merekKwh,
                           String noPabrikKwh,
                           String typeKwh,
                           String tarifIndex,
                           String tahunBuatKwh,
                           String tahunTeraKwh,
                           String tinggiMeterKwh,
                           String trafoArusPrimer1,
                           String trafiArusPrimer2,
                           String trafoArusSekunder1,
                           String trafoArusSekunder2,
                           String trafoTeganganPrimer,
                           String trafoTeganganSekunder,
                           String merekPembatas,
                           String noPembatas,
                           String typePembatas,
                           String jenisPembatas,
                           String ukuranSettingPembatas,
                           String phasaPembatas,
                           String tegangPembatas,
                           Long arusPembatas,
                           String tahunTeraPembatas,
                           String tahunBuatPembatas,
                           String kodeKabel,
                           String merekKabel,
                           String jenisKabel,
                           String typeKabel,
                           String penampangKabel,
                           Integer jumlahKabel,
                           String jumlahCcoa,
                           String kabelNyy,
                           String kabelNya,
                           String jumlahKableNyy,
                           String jumlahKabelNya,
                           String oka,
                           String clamp,
                           String noSegelOk1,
                           String noSegelOk2,
                           String noSegelKwh1,
                           String noSegelKwh2,
                           String noSegelMcb1,
                           String noPabrikKabel,
                           String noKabel,
                           String kodePembMeterKvarh,
                           String merekKvarh,
                           String noPabrikKvarh,
                           String noMeterKvarh,
                           String typeKvarh,
                           String tahunTeraKvarh,
                           String tahunBuatKvarh,
                           String merekTrafoArusKvarh,
                           String tipeTrafoArusKvarh,
                           String tahunBuatTrafoArusKvarh,
                           String tahunTeraTrafoArusKvarh,
                           String trafoArusKvarhPrimer1,
                           String trafoArusKvarhPrimer2,
                           String trafoArusKvarhSekunder1,
                           String trafoArusKvarhSekunder2,
                           String merekTrafoTeganganKvarh,
                           String tipeTrafoTeganganKvarh,
                           String trafoTeganganKvarhPrimer,
                           String trafoTeganganKvarhSekunder,
                           String tahunBuatTrafoTeganganKvarh,
                           String tahunTeraTrafoTeganganKvarh,
                           String kodePembMeterKvamaks,
                           String merekKvamaks,
                           String noPabrikKvmaks,
                           String noRegisterKvamaks,
                           String typeKvamaks,
                           String tahunTeraKvamaks,
                           String tahunBuatKvamaks,
                           String merekTrafoArusKvamaks,
                           String tipeTrafoArusKvamaks,
                           String tahunBuatTrafoArusKvamaks,
                           String tahunTeraTrafoArusKvamaks,
                           String trafoArusKvamaksPrimer1,
                           String trafoArusKvamaksPrimer2,
                           String trafoArusKvamaksSekunder1,
                           String trafoArusKvamaksSekunder2,
                           String merekTrafoTeganganKvamaks,
                           String tipeTrafoTeganganKvamaks,
                           String trafoTeganganKvamaksPrimer,
                           String trafoTeganganKvamaksSekunder,
                           String tahunBuatTrafoTeganganKvamaks,
                           String tahunTeraTrafoTeganganKvamaks,
                           String merekSaklarWaktu,
                           String noSaklarWaktu,
                           String typeSaklarWaktu,
                           String jenisSaklarWaktu,
                           String ukuranSettingSaklarWaktu,
                           String fasaBatasSaklarWaktu,
                           String teganganSaklarWaktu,
                           String arusSaklarWaktu,
                           String tahunTeraSaklarWaktu,
                           String tahunBuatSaklarWaktu,
                           String kodeTrafoArus,
                           String noTrafoArus,
                           String merekTrafoArus,
                           String typeTrafoArus,
                           String tahunTeraTrafoArus,
                           String tahunBuatTrafoArus,
                           String kodeTrafoTegangan,
                           String noTrafoTegangan,
                           String merekTrafoTegangan,
                           String typeTrafoTegangan,
                           String tahunTeraTrafoTegangan,
                           String tahunBuatTrafoTegangan,
                           Integer jumlahTrafoTegangan,
                           String kodeModem,
                           String noModem,
                           String merekModem,
                           String typeModem,
                           String speed,
                           String operatorSeluler,
                           String kartuSim,
                           Integer jumlahModem,
                           Date tanggalPerintahKerja,
                           Integer messagingStatus) {
}
