package com.iconplus.ap2t.fso.legacy.harilibur;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 7/30/2025
 */
@Entity
@Table(schema = "FSO", name = "MASTER_HARI_LIBUR")
public class HariLibur extends PanacheEntityBase {

    @Id
    @Column(name = "TGLLIBUR")
    public Date tanggalLibur;

    @Size(max = 100)
    @Column(length = 100, name = "KETHARILIB<PERSON>")
    public String keteranganHariLibur;
}
