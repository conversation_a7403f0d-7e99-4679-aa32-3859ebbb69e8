package com.iconplus.ap2t.fso.legacy.info.pelanggan;

import com.iconplus.ap2t.fso.legacy.wo.WorkOrderId;
import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 7/30/2025
 */
@Entity
@Table(schema = "FSO", name = "TRANS_DATA_TEKNIK")
public class InformasiPelanggan extends PanacheEntityBase {

    @EmbeddedId
    public WorkOrderId workOrderId;

    @Size(max = 20)
    @Column(length = 20, name = "IDPEL")
    public String idPelanggan;

    @Size(max = 100)
    @Column(length = 100, name = "NAMA_PELANGGAN")
    public String namaPelanggan;

    @Size(max = 1000)
    @Column(length = 1000, name = "ALAMAT_PELANGGAN")
    public String alamatPelanggan;

    @Size(max = 30)
    @Column(length = 30, name = "NOTELP_PELANGGAN")
    public String noTelpPelanggan;

    @Size(max = 30)
    @Column(length = 30, name = "NOHP_PELANGGAN")
    public String noHpPelanggan;
}
