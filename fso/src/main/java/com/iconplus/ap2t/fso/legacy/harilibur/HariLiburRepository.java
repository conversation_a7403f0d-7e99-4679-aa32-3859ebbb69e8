package com.iconplus.ap2t.fso.legacy.harilibur;

import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 7/31/2025
 */
@ApplicationScoped
public class HariLiburRepository implements PanacheRepositoryBase<HariLibur, Long> {

    public List<HariLibur> findByTanggalLiburBetween(Date tanggalAwal, Date tanggalAkhir) {
        return find("tanggalLibur between ?1 and ?2", tanggalAwal, tanggalAkhir).list();
    }

    public List<HariLibur> findByTanggalLibur(Date tanggal) {
        return find("tanggalLibur", tanggal).list();
    }
}
