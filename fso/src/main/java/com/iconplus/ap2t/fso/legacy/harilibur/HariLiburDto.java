package com.iconplus.ap2t.fso.legacy.harilibur;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/4/2025
 */
public record HariLiburDto(Date tanggalLibur,
                           String keteranganHariLibur) {

    public static HariLiburDto fromEntity(HariLibur hariLibur) {
        return new HariLiburDto(hariLibur.tanggalLibur, hariLibur.keteranganHariLibur);
    }

    public HariLibur toEntity(HariLiburDto hariLiburDto) {
        HariLibur hariLibur = new HariLibur();
        hariLibur.tanggalLibur = hariLiburDto.tanggalLibur;
        hariLibur.keteranganHariLibur = hariLiburDto.keteranganHariLibur;
        return hariLibur;
    }
}
