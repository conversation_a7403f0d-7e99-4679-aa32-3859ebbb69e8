package com.iconplus.ap2t.fso.legacy.dayafasa;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/4/2025
 */
@Entity
@Table(schema = "FSO", name = "REF_DAYA_FASA")
public class DayaFasa extends PanacheEntityBase {

    @Id
    @Column(name = "DAYA")
    public Integer daya;

    @Column(name = "FASA")
    public Integer fasa;
}
