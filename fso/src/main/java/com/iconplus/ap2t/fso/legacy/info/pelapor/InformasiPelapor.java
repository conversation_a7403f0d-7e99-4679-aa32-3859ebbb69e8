package com.iconplus.ap2t.fso.legacy.info.pelapor;

import com.iconplus.ap2t.fso.legacy.wo.WorkOrderId;
import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 7/30/2025
 */
@Entity
@Table(schema = "FSO", name = "TRANS_DATA_TEKNIK")
public class InformasiPelapor extends PanacheEntityBase {

    @EmbeddedId
    public WorkOrderId workOrderId;

    @Size(max = 100)
    @Column(length = 100, name = "NAMA_PEMOHON")
    public String namaPemohon;

    @Size(max = 1000)
    @Column(length = 1000, name = "ALAMAT_PEMOHON")
    public String alamatPemohon;

    @Size(max = 30)
    @Column(length = 30, name = "NOTELP_PEMOHON")
    public String noTelpPemohon;

    @Size(max = 30)
    @Column(length = 30, name = "NOHP_PEMOHON")
    public String noHpPemohon;
}
