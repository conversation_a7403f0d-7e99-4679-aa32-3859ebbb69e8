package com.iconplus.ap2t.fso.legacy.dayafasa;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 8/4/2025
 */
public record DayaFasaDto(Integer daya,
                          Integer fasa) {

    public static DayaFasaDto fromEntity(DayaFasa dayaFasa) {
        return new DayaFasaDto(dayaFasa.daya, dayaFasa.fasa);
    }

    public DayaFasa toEntity(DayaFasaDto dayaFasaDto) {
        DayaFasa dayaFasa = new DayaFasa();
        dayaFasa.daya = dayaFasaDto.daya;
        dayaFasa.fasa = dayaFasaDto.fasa;
        return dayaFasa;
    }
}
