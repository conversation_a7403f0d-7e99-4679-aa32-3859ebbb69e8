package com.iconplus.ap2t.fso.legacy.wo;

import com.iconplus.ap2t.fso.legacy.dayafasa.DayaFasa;
import com.iconplus.ap2t.fso.legacy.harilibur.HariLibur;
import com.iconplus.ap2t.fso.legacy.dayafasa.DayaFasaRepository;
import com.iconplus.ap2t.fso.legacy.harilibur.HariLiburRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 7/30/2025
 */
@ApplicationScoped
public class WorkOrderService {

    @Inject
    HariLiburRepository hariLiburRepo;

    @Inject
    DayaFasaRepository dayaFasaRepo;

    @Inject
    WorkOrderRepository workOrderRepo;

    /**
     * Migrate from function FSO.GET_STATUS
     *
     * @param kdStatus Kode status W/O
     * @return Nama status W/O
     */
    private String getStatus(String kdStatus) {
        return switch (kdStatus) {
            case "00" -> "PK DARI AP2T";
            case "01" -> "ISIAN MATERIAL";
            case "02" -> "PENUGASAN W/O KE VENDOR";
            case "10" -> "PELAKSANAAN/RESPONSE W/O";
            case "20" -> "W/O DITERIMA PETUGAS";
            case "30" -> "PESAN MATERIAL";
            case "40" -> "MATERIAL DITERIMA";
            case "41" -> "RESET MATERIAL";
            case "50" -> "DALAM PERJALANAN";
            case "60" -> "SAMPAI-PASANG";
            case "61" -> "SAMPAI-TUNDA";
            case "70" -> "NYALA";
            case "71" -> "ISIAN DATA PEMASANGAN";
            case "72" -> "RETUR MATERIAL";
            case "73" -> "W/O SELESAI";
            case "75" -> "TUNDA-PENORMALAN-PESTA";
            case "76" -> "SAMPAI BONGKAR";
            case "77" -> "ISIAN DATA BONGKAR PESTA";
            case "79" -> "ENTRI SISA TOKEN PESTA";
            case "80" -> "Update AP2T";
            case "81" -> "PDL Manual Non FSO";
            case "88" -> "SAMPAI-PASANG";
            case "90" -> "PHOTO DIULANG";
            case "91" -> "PHOTO SUDAH DIULANG";
            case "98" -> "W/O DITOLAK (MIGRASI UNIT)";
            case "99" -> "W/O DITOLAK";
            default -> "";
        };
    }

    public String getHariSla(Integer hariSla, String jenisTransaksi) {
        if (getFlagSla(jenisTransaksi)) {
            return hariSla.toString();
        } else {
            return "-";
        }
    }

    /**
     * Migrate from view FSO.V_SLA_WO
     *
     * @param jenisTransaksi Jenis transaksi pelanggan
     * @return <code>true</code> jika transaksi termasuk jenis yang memiliki SLA
     * <code>false</code> jika tidak
     */
    private boolean getFlagSla(String jenisTransaksi) {
        return JenisTransaksi.FLAG_SLA_ENABLED.contains(jenisTransaksi);
    }

    public String getDurasiHariKerja(String jenisTransaksi, Integer hariSla, Date tglRemaja, Date tglLunas, Date tglRestitusi) {
        if (getFlagSla(jenisTransaksi)) {
            if (hitungDurasiHariKerja(jenisTransaksi, tglRemaja, tglLunas, tglRestitusi) > hariSla) {
                return hariSla.toString();
            } else {
                return String.valueOf(hitungDurasiHariKerja(jenisTransaksi, tglRemaja, tglLunas, tglRestitusi) - 1);
            }
        } else {
            return "-";
        }
    }

    public String getSisaHariSla(String jenisTransaksi, Integer hariSla, Date tglRemaja, Date tglLunas, Date tglRestitusi) {
        if (getFlagSla(jenisTransaksi)) {
            if (hitungDurasiHariKerja(jenisTransaksi, tglRemaja, tglLunas, tglRestitusi) > hariSla) {
                return String.valueOf(hariSla - hitungDurasiHariKerja(jenisTransaksi, tglRemaja, tglLunas, tglRestitusi));
            } else {
                return String.valueOf(hariSla - hitungDurasiHariKerja(jenisTransaksi, tglRemaja, tglLunas, tglRestitusi) + 1);
            }
        } else {
            return "-";
        }
    }

    public Integer getOrderHariSla(String jenisTransaksi, Integer hariSla, Date tglRemaja, Date tglLunas, Date tglRestitusi) {
        if (getFlagSla(jenisTransaksi)) {
            if (hitungDurasiHariKerja(jenisTransaksi, tglRemaja, tglLunas, tglRestitusi) > hariSla) {
                return hariSla - hitungDurasiHariKerja(jenisTransaksi, tglRemaja, tglLunas, tglRestitusi).intValue();
            } else {
                return hariSla - hitungDurasiHariKerja(jenisTransaksi, tglRemaja, tglLunas, tglRestitusi).intValue() + 1;
            }
        } else {
            return 0;
        }
    }

    private Long hitungDurasiHariKerja(String jenisTransaksi, Date tglRemaja, Date tglLunas, Date tglRestitusi) {
        if (getFlagSla(jenisTransaksi)) {
            if (tglRemaja != null) {
                return hitungDurasiHariKerja(tglLunas, tglRemaja);
            } else if (tglRestitusi != null) {
                return hitungDurasiHariKerja(tglLunas, tglRestitusi);
            } else {
                Date tomorrow = Date.from(LocalDate.now().plusDays(1L)
                        .atStartOfDay(ZoneId.systemDefault()).toInstant());
                return hitungDurasiHariKerja(tglLunas, tomorrow);
            }
        } else {
            return 0L;
        }
    }

    /**
     * Migrate from function FSO.f$HitungHariNew
     *
     * @param firstDate  Tanggal pertama
     * @param secondDate Tanggal kedua
     * @return Durasi hari kerja antara tanggal pertama dan kedua
     */
    private Long hitungDurasiHariKerja(Date firstDate, Date secondDate) {
        if (firstDate == null || secondDate == null)
            return null;
        LocalDate firstLocalDate = LocalDate.ofInstant(firstDate.toInstant(), ZoneId.systemDefault());
        LocalDate secondLocalDate = LocalDate.ofInstant(secondDate.toInstant(), ZoneId.systemDefault());
        Date dateAwal;
        Date dateAkhir;
        LocalDate tglAwal;
        LocalDate tglAkhir;
        if (firstLocalDate.isBefore(secondLocalDate)) {
            tglAwal = firstLocalDate;
            tglAkhir = secondLocalDate;
            dateAwal = firstDate;
            dateAkhir = secondDate;
        } else {
            tglAwal = secondLocalDate;
            tglAkhir = firstLocalDate;
            dateAwal = secondDate;
            dateAkhir = firstDate;
        }
        LocalDate firstMonday = tglAwal.minusDays(subtractCodeDay(tglAwal.getDayOfWeek()));
        LocalDate endMonday = tglAkhir.minusDays(subtractCodeDay(tglAkhir.getDayOfWeek()));
        double substractMonday = (double) ChronoUnit.DAYS.between(firstMonday, endMonday) / 7 * 2;
        long workday = (long) (ChronoUnit.DAYS.between(tglAwal, tglAkhir) - substractMonday);
        if (DayOfWeek.SUNDAY.equals(tglAwal.getDayOfWeek()) && ChronoUnit.DAYS.between(tglAwal, tglAkhir) >= 1) {
            workday++;
        }
        if (DayOfWeek.SATURDAY.equals(tglAwal.getDayOfWeek()) && ChronoUnit.DAYS.between(tglAwal, tglAkhir) > 1) {
            workday++;
        }
        if (DayOfWeek.SUNDAY.equals(tglAwal.getDayOfWeek()) && ChronoUnit.DAYS.between(tglAwal, tglAkhir) > 0) {
            workday++;
        }
        if (DayOfWeek.SATURDAY.equals(tglAwal.getDayOfWeek()) && ChronoUnit.DAYS.between(tglAwal, tglAkhir) > 0) {
            workday--;
        }
        if (DayOfWeek.SUNDAY.equals(tglAwal.getDayOfWeek()) && ChronoUnit.DAYS.between(tglAwal, tglAkhir) > 0) {
            if (ChronoUnit.DAYS.between(tglAwal, tglAkhir) == 1) {
                workday--;
            } else {
                workday -= 2;
            }
        }
        List<HariLibur> byTanggalLiburBetween = hariLiburRepo.findByTanggalLiburBetween(dateAwal, dateAkhir);
        long jumlahHariLibur = byTanggalLiburBetween.stream()
                .filter(hariLibur -> !DayOfWeek.SATURDAY.equals(LocalDate.ofInstant(hariLibur.tanggalLibur.toInstant(), ZoneId.systemDefault()).getDayOfWeek()))
                .filter(hariLibur -> !DayOfWeek.SUNDAY.equals(LocalDate.ofInstant(hariLibur.tanggalLibur.toInstant(), ZoneId.systemDefault()).getDayOfWeek()))
                .count();
        workday = workday - jumlahHariLibur;
        List<HariLibur> byTanggalLibur = hariLiburRepo.findByTanggalLibur(dateAwal);
        long jumlahHariLiburAwal = byTanggalLibur.stream()
                .filter(hariLibur -> !DayOfWeek.SATURDAY.equals(LocalDate.ofInstant(hariLibur.tanggalLibur.toInstant(), ZoneId.systemDefault()).getDayOfWeek()))
                .filter(hariLibur -> !DayOfWeek.SUNDAY.equals(LocalDate.ofInstant(hariLibur.tanggalLibur.toInstant(), ZoneId.systemDefault()).getDayOfWeek()))
                .filter(hariLibur -> dateAwal.equals(hariLibur.tanggalLibur))
                .count();
        if (jumlahHariLiburAwal > 0 && tglAkhir.isAfter(tglAwal)) {
            workday++;
        }
        return Math.max(workday, 0L);
    }

    private int subtractCodeDay(DayOfWeek dayOfWeek) {
        return switch (dayOfWeek) {
            case MONDAY -> 0;
            case TUESDAY -> 1;
            case WEDNESDAY -> 2;
            case THURSDAY -> 3;
            case FRIDAY -> 4;
            case SATURDAY -> 5;
            case SUNDAY -> 6;
        };
    }

    private int getFasa(Integer daya) {
        if (daya > 22000) return 3;
        DayaFasa byId = dayaFasaRepo.findById(daya);
        return byId != null ? byId.fasa : 1;
    }
}
