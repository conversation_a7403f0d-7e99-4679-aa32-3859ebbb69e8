package com.iconplus.ap2t.fso.legacy;

import com.iconplus.ap2t.fso.legacy.wo.WorkOrderService;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 7/31/2025
 */
@QuarkusTest
public class WorkOrderServiceTest {

    @Inject
    WorkOrderService workOrderService;

    @Test
    void testHitungDurasiKerja() {
        Date tglAwal = new Date(1754326800000L);
        Date tglAkhir = new Date(1753894800000L);
//        String durasiHariKerja = workOrderService.getDurasiHariKerja(tglAwal, tglAkhir);
//        Assertions.assertEquals("3", durasiHariKerja);
    }
}
