package com.iconplus.ap2t.fso.legacy;

import com.iconplus.ap2t.fso.legacy.material.pembatas.MaterialPembatas;
import com.iconplus.ap2t.fso.legacy.wo.WorkOrderId;
import com.iconplus.ap2t.fso.legacy.material.pembatas.MaterialPembatasRepository;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 7/29/2025
 */
@QuarkusTest
public class IsianMaterialTest {

    @Inject
    MaterialPembatasRepository materialPembatasRepo;

    @Test
    void getOneMaterialPembatas() {
        WorkOrderId woId = new WorkOrderId();
        woId.noAgenda = "538710562501070989";
        woId.unitUp = "53871";
        MaterialPembatas byId = materialPembatasRepo.findById(woId);
        Assertions.assertEquals("PEMBATAS", byId.kodeArusPembatas);
        Assertions.assertEquals("ACTAR", byId.merekPembatas);
        Assertions.assertEquals("2010", byId.tahunBuatPembatas);
    }
}

