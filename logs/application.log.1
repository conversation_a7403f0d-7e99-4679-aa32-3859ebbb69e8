2025-07-24 15:00:13,265 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] FINE  [io.grp.NameResolverRegistry] (Quarkus Main Thread) Service loader found io.grpc.internal.DnsNameResolverProvider@4f65a80b
2025-07-24 15:00:13,265 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] FINE  [io.grp.NameResolverRegistry] (Quarkus Main Thread) Service loader found io.grpc.netty.UdsNameResolverProvider@7ab64b2c
2025-07-24 15:00:13,280 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] FINE  [io.grp.LoadBalancerRegistry] (Quarkus Main Thread) Service loader found Provider{policy=round_robin, priority=5, available=true}
2025-07-24 15:00:13,280 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] FINE  [io.grp.LoadBalancerRegistry] (Quarkus Main Thread) Service loader found OutlierDetectionLoadBalancerProvider{policy=outlier_detection_experimental, priority=5, available=true}
2025-07-24 15:00:13,280 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] FINE  [io.grp.LoadBalancerRegistry] (Quarkus Main Thread) Service loader found PickFirstLoadBalancerProvider{policy=pick_first, priority=5, available=true}
2025-07-24 15:00:15,345 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.caffeine.expire-after-write" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-24 15:00:15,345 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.enabled" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-24 15:00:15,346 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.redis.key-type" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-24 15:00:15,346 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.redis.expensiveResourceCache.expire-after-write" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-24 15:00:15,346 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.redis.expensiveResourceCache.key-type" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-24 15:00:15,346 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.thread-pool.name" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-24 15:00:15,346 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.caffeine.metrics-enabled" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-24 15:00:15,346 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.redis.expire-after-write" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-24 15:00:15,346 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.http.worker-threads" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-24 15:00:15,346 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.health.datasource.enabled" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-24 15:00:15,346 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.caffeine.expire-after-access" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-24 15:00:16,694 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [io.qua.hib.orm.run.ser.QuarkusRuntimeInitDialectFactory] (JPA Startup Thread) Persistence unit default-reactive: Could not retrieve the database version to check it is at least 12.0.0
2025-07-24 15:00:16,903 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] INFO  [io.qua.grp.run.GrpcServerRecorder] (Quarkus Main Thread) Starting new Quarkus gRPC server (using Vert.x transport)...
2025-07-24 15:00:17,010 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] INFO  [io.sma.rea.mes.kafka] (Quarkus Main Thread) SRMSG18229: Configured topics for channel 'job-hitung-billing': [job-hitung-billing]
2025-07-24 15:00:17,019 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] INFO  [io.sma.rea.mes.kafka] (Quarkus Main Thread) SRMSG18214: Key deserializer omitted, using String as default
2025-07-24 15:00:17,195 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] INFO  [io.sma.rea.mes.kafka] (smallrye-kafka-consumer-thread-0) SRMSG18257: Kafka consumer kafka-consumer-job-hitung-billing, connected to Kafka brokers '10.1.51.71:9092,10.1.51.71:9093,10.1.51.71:9094', belongs to the 'job-hitung-billing-02' consumer group and is configured to poll records from [job-hitung-billing]
2025-07-24 15:00:17,284 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] INFO  [io.quarkus] (Quarkus Main Thread) engine 1.0-SNAPSHOT on JVM (powered by Quarkus 3.23.0) started in 13.969s. Listening on: http://0.0.0.0:8001
2025-07-24 15:00:17,285 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] INFO  [io.quarkus] (Quarkus Main Thread) Profile dev activated. Live Coding activated.
2025-07-24 15:00:17,286 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] INFO  [io.quarkus] (Quarkus Main Thread) Installed features: [awt, cdi, compose, grpc-server, hibernate-orm, hibernate-reactive, hibernate-reactive-panache, hibernate-validator, kafka-client, logging-sentry, mailer, messaging, messaging-kafka, micrometer, poi, qute, qute-web, reactive-pg-client, redis-client, rest, rest-client, rest-client-jackson, rest-jackson, rest-links, rest-qute, security, smallrye-context-propagation, smallrye-jwt, smallrye-openapi, swagger-ui, vertx]
2025-07-24 15:00:26,694 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:00:36,667 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:00:45,000 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:01:03,645 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:01:23,660 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:01:44,363 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:02:05,517 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:02:05,518 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:02:22,704 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] INFO  [com.ico.ap2.eng.con.RootController] (vert.x-eventloop-thread-3) Welcome to Billing Engine
2025-07-24 15:02:26,673 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:02:26,674 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:02:47,842 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:02:47,843 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:02:57,159 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] ERROR [io.qua.ver.htt.run.QuarkusErrorHandler] (vert.x-eventloop-thread-3) HTTP Request to /api/report/daily/test?_=1753344147047 failed, error id: 73ef2062-ad9a-41ad-9d59-2e65fed63574-1: io.vertx.core.impl.NoStackTraceThrowable: Timeout

2025-07-24 15:02:57,159 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] ERROR [io.qua.ver.htt.run.QuarkusErrorHandler] (vert.x-eventloop-thread-6) HTTP Request to /api/report/daily/transaction?_=1753344147046 failed, error id: 73ef2062-ad9a-41ad-9d59-2e65fed63574-2: io.vertx.core.impl.NoStackTraceThrowable: Timeout

2025-07-24 15:03:08,902 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:03:08,903 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:03:30,048 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:03:30,048 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:03:51,174 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:03:51,174 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:04:12,321 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:04:12,322 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:04:33,454 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:04:33,454 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:04:54,519 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:04:54,520 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:05:15,679 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:05:15,680 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:05:36,814 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:05:36,815 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:05:57,966 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:05:57,967 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:06:19,112 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:06:19,112 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:06:40,244 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:06:40,245 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:07:01,301 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:07:01,301 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:07:22,446 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:07:22,447 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:07:43,624 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:07:43,624 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:08:04,801 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:08:04,802 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:08:25,941 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:08:25,942 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:08:39,975 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] ERROR [io.qua.ver.htt.run.QuarkusErrorHandler] (vert.x-eventloop-thread-4) HTTP Request to /api/formula failed, error id: 73ef2062-ad9a-41ad-9d59-2e65fed63574-3: io.vertx.core.impl.NoStackTraceThrowable: Timeout

2025-07-24 15:08:47,061 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:08:47,061 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:09:08,215 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:09:08,216 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:09:29,370 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:09:29,371 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:09:50,491 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:09:50,492 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:10:11,650 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:10:11,650 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:10:32,787 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:10:32,788 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:10:53,973 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:10:53,974 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:11:15,107 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:11:15,107 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:11:36,267 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:11:36,268 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:11:57,442 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:11:57,443 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:12:18,587 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:12:18,587 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:12:39,749 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:12:39,749 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:13:00,902 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:13:00,903 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:13:22,034 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:13:22,035 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:13:43,183 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:13:43,184 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:14:04,307 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:14:04,307 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:14:25,358 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:14:25,359 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:14:46,505 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:14:46,507 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:15:07,641 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:15:07,642 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:15:28,777 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:15:28,777 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:15:49,894 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:15:49,896 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:16:11,037 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:16:11,039 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:16:32,125 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:16:32,125 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:16:53,245 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:16:53,246 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:17:14,383 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:17:14,384 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:17:35,509 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:17:35,510 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:17:56,653 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:17:56,654 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:18:17,759 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:18:17,760 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:18:38,908 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:18:38,909 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:58:34,658 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:58:34,705 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:58:55,764 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:58:55,765 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 15:59:16,900 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 15:59:16,901 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 15:59:38,047 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 15:59:38,049 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 15:59:59,167 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 15:59:59,167 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:00:20,313 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:00:20,313 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:00:41,441 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:00:41,441 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:01:02,593 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:01:02,593 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:01:23,713 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:01:23,714 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:01:44,892 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:01:44,893 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:02:06,029 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:02:06,030 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:02:27,209 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:02:27,209 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:02:48,325 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:02:48,326 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:03:09,481 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:03:09,482 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:03:30,627 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:03:30,628 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:03:51,782 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:03:51,783 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:04:12,930 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:04:12,930 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:04:34,088 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:04:34,088 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:04:55,253 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:04:55,254 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:05:16,393 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:05:16,394 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:05:37,515 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:05:37,515 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:05:58,660 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:05:58,661 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:06:19,808 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:06:19,808 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:06:40,969 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:06:40,969 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:07:02,097 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:07:02,097 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:07:23,261 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:07:23,261 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:07:44,417 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:07:44,418 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:08:05,554 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:08:05,555 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:08:26,713 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:08:26,713 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:08:47,859 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:08:47,860 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:09:09,009 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:09:09,009 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:09:30,156 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:09:30,157 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:09:51,309 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:09:51,309 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:10:12,475 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:10:12,475 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:10:33,630 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:10:33,630 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:10:54,768 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:10:54,774 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:11:15,905 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:11:15,906 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:11:37,068 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:11:37,069 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:11:58,223 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:11:58,223 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:12:19,367 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:12:19,367 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:12:40,511 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:12:40,511 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:13:01,670 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:13:01,671 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:13:22,817 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:13:22,818 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:13:43,964 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:13:43,965 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:14:05,108 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:14:05,109 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:14:26,245 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:14:26,245 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:14:47,363 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:14:47,364 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:15:08,530 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:15:08,531 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:15:29,652 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:15:29,653 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:15:50,791 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:15:50,792 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:16:11,953 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:16:11,954 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:16:33,087 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:16:33,088 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:16:54,261 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:16:54,261 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:17:15,413 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:17:15,414 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:17:36,483 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:17:36,483 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:22:06,251 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:22:06,275 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:22:27,319 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:22:27,319 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:22:48,480 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:22:48,480 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:23:09,645 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:23:09,646 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:23:30,774 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:23:30,776 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:23:51,929 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:23:51,929 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:24:13,078 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:24:13,079 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:24:34,211 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:24:34,211 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:24:55,374 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:24:55,375 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:25:16,450 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:25:16,451 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:25:37,618 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:25:37,618 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:25:58,785 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:25:58,786 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:26:19,899 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:26:19,900 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:26:41,030 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:26:41,030 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:27:02,160 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:27:02,161 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:27:23,306 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:27:23,306 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:27:44,439 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:27:44,440 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:28:05,572 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:28:05,572 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:28:26,735 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:28:26,735 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:28:47,865 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:28:47,870 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:29:09,026 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:29:09,027 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:29:30,156 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:29:30,157 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:29:51,215 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:29:51,216 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:30:12,376 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:30:12,377 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:30:33,558 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:30:33,558 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:30:54,728 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:30:54,729 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:31:15,879 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:31:15,879 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:31:37,002 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:31:37,002 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:31:58,170 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:31:58,170 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:32:19,326 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:32:19,327 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:32:40,470 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:32:40,471 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:33:01,602 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:33:01,603 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:33:22,742 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:33:22,742 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:33:43,832 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:33:43,833 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:34:04,995 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:34:04,995 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:34:26,084 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:34:26,086 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:34:47,228 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:34:47,228 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:35:08,359 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:35:08,360 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:35:29,510 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:35:29,511 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:35:50,660 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:35:50,661 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:36:11,781 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:36:11,781 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:36:32,927 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:36:32,928 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:36:54,038 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:36:54,040 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:37:15,174 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:37:15,174 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:37:36,317 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:37:36,317 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:37:57,486 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:37:57,486 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:38:18,644 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:38:18,645 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:38:39,805 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:38:39,805 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:39:00,980 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:39:00,980 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:39:22,114 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:39:22,114 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:39:43,209 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:39:43,210 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:40:04,360 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:40:04,361 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:40:25,480 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:40:25,481 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:40:46,633 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:40:46,634 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:41:07,787 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:41:07,788 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:41:28,836 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:41:28,837 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:41:49,981 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:41:49,982 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:42:11,132 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:42:11,132 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:42:32,273 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:42:32,274 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:42:53,410 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:42:53,410 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:43:14,528 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:43:14,528 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:43:35,667 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:43:35,669 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:43:56,809 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:43:56,810 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:44:17,980 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:44:17,981 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:44:39,162 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:44:39,163 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:45:00,291 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:45:00,292 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:45:21,445 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:45:21,445 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:45:42,604 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:45:42,604 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:46:03,665 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:46:03,665 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:46:24,811 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:46:24,812 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:46:45,974 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:46:45,974 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:47:07,123 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:47:07,123 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:47:28,293 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:47:28,294 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:47:49,451 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:47:49,452 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:48:10,600 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:48:10,601 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:48:31,745 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:48:31,745 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:48:52,907 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:48:52,908 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:49:14,039 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:49:14,040 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:49:35,206 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:49:35,207 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:49:56,354 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:49:56,355 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:50:17,508 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:50:17,508 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:50:38,633 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:50:38,633 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:50:59,752 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:50:59,753 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:51:20,883 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:51:20,884 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:51:42,000 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:51:42,001 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:52:03,134 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:52:03,135 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:52:24,275 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:52:24,275 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:52:45,410 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:52:45,411 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:53:06,543 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:53:06,544 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:53:27,605 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:53:27,605 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:53:48,729 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:53:48,730 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:54:09,845 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:54:09,846 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:54:30,962 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:54:30,963 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:54:52,127 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:54:52,128 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:55:13,279 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:55:13,280 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:55:34,350 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:55:34,351 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:55:55,531 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:55:55,532 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:56:16,692 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:56:16,692 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:56:37,864 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -3 (/10.1.51.71:9094) could not be established. Node may not be available.
2025-07-24 16:56:37,864 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9094 (id: -3 rack: null) disconnected
2025-07-24 16:56:59,062 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -1 (/10.1.51.71:9092) could not be established. Node may not be available.
2025-07-24 16:56:59,062 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9092 (id: -1 rack: null) disconnected
2025-07-24 16:57:20,224 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Connection to node -2 (/10.1.51.71:9093) could not be established. Node may not be available.
2025-07-24 16:57:20,225 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] Bootstrap broker 10.1.51.71:9093 (id: -2 rack: null) disconnected
2025-07-24 16:57:31,085 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] WARNING [org.aes.rea.ter.imp.AbstractWindowsTerminal] (Quarkus Main Thread) Failed to write out.
2025-07-24 16:57:31,083 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[25088] INFO  [io.quarkus] (Quarkus Main Thread) engine stopped in 0.070s
