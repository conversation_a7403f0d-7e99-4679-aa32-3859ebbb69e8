2025-07-28 14:11:12,753 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] FINE  [io.grp.NameResolverRegistry] (Quarkus Main Thread) Service loader found io.grpc.internal.DnsNameResolverProvider@38efa539
2025-07-28 14:11:12,753 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] FINE  [io.grp.NameResolverRegistry] (Quarkus Main Thread) Service loader found io.grpc.netty.UdsNameResolverProvider@18a8debb
2025-07-28 14:11:12,768 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] FINE  [io.grp.LoadBalancerRegistry] (Quarkus Main Thread) Service loader found Provider{policy=round_robin, priority=5, available=true}
2025-07-28 14:11:12,768 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] FINE  [io.grp.LoadBalancerRegistry] (Quarkus Main Thread) Service loader found OutlierDetectionLoadBalancerProvider{policy=outlier_detection_experimental, priority=5, available=true}
2025-07-28 14:11:12,768 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] FINE  [io.grp.LoadBalancerRegistry] (Quarkus Main Thread) Service loader found PickFirstLoadBalancerProvider{policy=pick_first, priority=5, available=true}
2025-07-28 14:11:15,204 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.caffeine.expire-after-write" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-28 14:11:15,204 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.enabled" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-28 14:11:15,204 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.redis.key-type" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-28 14:11:15,204 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.redis.expensiveResourceCache.expire-after-write" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-28 14:11:15,204 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.redis.expensiveResourceCache.key-type" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-28 14:11:15,204 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.thread-pool.name" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-28 14:11:15,204 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.caffeine.metrics-enabled" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-28 14:11:15,204 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.redis.expire-after-write" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-28 14:11:15,204 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.http.worker-threads" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-28 14:11:15,205 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.health.datasource.enabled" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-28 14:11:15,205 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] WARN  [io.qua.config] (Quarkus Main Thread) Unrecognized configuration key "quarkus.cache.caffeine.expire-after-access" was provided; it will be ignored; verify that the dependency extension for this configuration is set or that you did not make a typo
2025-07-28 14:11:16,701 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] WARN  [io.qua.hib.orm.run.ser.QuarkusRuntimeInitDialectFactory] (JPA Startup Thread) Persistence unit default-reactive: Could not retrieve the database version to check it is at least 12.0.0
2025-07-28 14:11:16,852 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] INFO  [io.qua.grp.run.GrpcServerRecorder] (Quarkus Main Thread) Starting new Quarkus gRPC server (using Vert.x transport)...
2025-07-28 14:11:16,986 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] INFO  [io.sma.rea.mes.kafka] (Quarkus Main Thread) SRMSG18229: Configured topics for channel 'job-hitung-billing': [job-hitung-billing]
2025-07-28 14:11:16,998 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] INFO  [io.sma.rea.mes.kafka] (Quarkus Main Thread) SRMSG18214: Key deserializer omitted, using String as default
2025-07-28 14:11:17,247 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] INFO  [io.sma.rea.mes.kafka] (smallrye-kafka-consumer-thread-0) SRMSG18257: Kafka consumer kafka-consumer-job-hitung-billing, connected to Kafka brokers '10.1.51.71:9092,10.1.51.71:9093,10.1.51.71:9094', belongs to the 'job-hitung-billing-02' consumer group and is configured to poll records from [job-hitung-billing]
2025-07-28 14:11:17,360 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] INFO  [io.quarkus] (Quarkus Main Thread) engine 1.0-SNAPSHOT on JVM (powered by Quarkus 3.23.0) started in 14.067s. Listening on: http://0.0.0.0:8001
2025-07-28 14:11:17,360 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] INFO  [io.quarkus] (Quarkus Main Thread) Profile dev activated. Live Coding activated.
2025-07-28 14:11:17,361 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] INFO  [io.quarkus] (Quarkus Main Thread) Installed features: [awt, cdi, compose, grpc-server, hibernate-orm, hibernate-reactive, hibernate-reactive-panache, hibernate-validator, kafka-client, logging-sentry, mailer, messaging, messaging-kafka, micrometer, poi, qute, qute-web, reactive-pg-client, redis-client, rest, rest-client, rest-client-jackson, rest-jackson, rest-links, rest-qute, security, smallrye-context-propagation, smallrye-jwt, smallrye-openapi, swagger-ui, vertx]
2025-07-28 14:11:17,644 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] WARN  [org.apa.kaf.cli.NetworkClient] (smallrye-kafka-consumer-thread-0) [Consumer clientId=kafka-consumer-job-hitung-billing, groupId=job-hitung-billing-02] The metadata response from the cluster reported a recoverable issue with correlation id 3 : {job-hitung-billing=LEADER_NOT_AVAILABLE}
2025-07-28 14:12:26,180 salmanoe C:\Program Files\Java\jdk-21\bin\java.exe[9184] INFO  [com.ico.ap2.eng.con.RootController] (vert.x-eventloop-thread-4) Welcome to Billing Engine
