package com.iconplus.ap2t.data.repository.billing;

import com.iconplus.ap2t.common.param.CustomerParam;
import com.iconplus.ap2t.common.param.billing.VerifikasiDlpdParam;
import com.iconplus.ap2t.common.param.master.UnitParam;
import com.iconplus.ap2t.common.response.billing.DataBillJamNyalaDto;
import com.iconplus.ap2t.common.response.billing.KwhPakaiRata2BulanDTO;
import com.iconplus.ap2t.data.entity.billing.Billing;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.quarkus.hibernate.reactive.panache.PanacheQuery;
import io.quarkus.hibernate.reactive.panache.PanacheRepositoryBase;
import io.quarkus.panache.common.Page;
import io.quarkus.panache.common.Parameters;
import io.quarkus.panache.common.Sort;
import io.smallrye.mutiny.Uni;
import io.vertx.mutiny.pgclient.PgPool;
import io.vertx.mutiny.sqlclient.Row;
import io.vertx.mutiny.sqlclient.Tuple;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@ApplicationScoped
public class BillingRepository implements PanacheRepositoryBase<Billing, String> {
    static Logger LOGGER = LoggerFactory.getLogger(BillingRepository.class.getName());
    @Inject
    PgPool client;
    public Uni<Billing> findByIdpelThblrek(String idpel, String thblrek){
        return Billing.find("idpel=?1 and thblrek=?2",idpel,thblrek).firstResult();
    }
    public Uni<List<CustomerParam>> getPostingBilling(String status, String thblrek, int page, int size){
        return find("SELECT idpel,thblrek FROM Billing WHERE postingbilling=?1 AND thblrek=?2 " +
                        "AND idpel not in (select idpel from WorkflowExecution where thblrek=?3)",
                Sort.by("idpel", Sort.Direction.Ascending),status,thblrek,thblrek)
                .project(CustomerParam.class)
                .page(page,size).list();
    }
    public Uni<List<CustomerParam>> getPostingBilling2(String status,String thblrek, int page, int size){
        return find("SELECT idpel,thblrek FROM Billing WHERE postingbilling=?1 AND thblrek=?2 " +
                        "AND idpel in (select idpel from WorkflowExecution where thblrek=?3 AND jsonBilling is not null)",
                Sort.by("idpel", Sort.Direction.Ascending),status,thblrek,thblrek)
                .project(CustomerParam.class)
                .page(page,size).list();
    }
    public Uni<List<CustomerParam>> getBillingUp(String status,String kode, int page, int size){
        return find("SELECT idpel,thblrek FROM Billing WHERE postingbilling=?1 AND unitup=?2 AND tarif in (SELECT DISTINCT tarif FROM MasterTarif)",
                Sort.by("idpel", Sort.Direction.Ascending),status,kode)
                .project(CustomerParam.class)
                .page(page,size).list();
    }
    public Uni<List<CustomerParam>> getBillingTarif(String status,String thblrek,String tarif,Integer daya, int page, int size){
        return find("SELECT idpel,thblrek FROM Billing WHERE postingbilling=?1 AND thblrek=?2 AND tarif=?3 and daya=?4",
                Sort.by("idpel", Sort.Direction.Ascending),status,thblrek,tarif,daya)
                .project(CustomerParam.class)
                .page(page,size).list();
    }
    public Uni<List<Billing>> getBillingStatus(String status,String thblrek, int page, int size){
        return find("postingbilling=?1 AND thblrek=?2 AND tglmulaihitung is null",
                Sort.by("idpel", Sort.Direction.Ascending),status,thblrek)
                .page(page,size).list();
    }
    public Uni<Integer> updateMsg(String idpel,String thblrek,String msg){
        return Panache.withTransaction(()->update("SET msg=?1 WHERE idpel=?2 AND thblrek=?3",msg,idpel,thblrek)
                .onItem().transform(results->{
                    LOGGER.info("Update MSG : {} success",msg);
                    return results;
                }));
    }
    private String getThblrek(String thblrek, int monthOffset) {
        LocalDate date = LocalDate.parse(thblrek + "01", DateTimeFormatter.ofPattern("yyyyMMdd"))
                .plusMonths(monthOffset);
        return date.format(DateTimeFormatter.ofPattern("yyyyMM"));
    }
    public Uni<Billing> findByIdForDlpd(String id) {
        return find("id=?1 and postingbilling='2' and tglVerifikasiDlpd is null", id).firstResult();
    }

    @Override
    public Uni<Billing> persist(Billing billing) {
        return Panache.withTransaction(billing::persist);
    }


    public Uni<KwhPakaiRata2BulanDTO> getRataRataKwhAndKvarh(String thblrek, String idpel) {
        // Menghitung bulan sebelumnya
        List<String> thblrekList = new ArrayList<>();
        thblrekList.add(getThblrek(thblrek, -1));
        thblrekList.add(getThblrek(thblrek, -2));
        thblrekList.add(getThblrek(thblrek, -3));

        // Menghitung rata-rata dengan query
        return find("SELECT " +
                "avg(COALESCE(kwhlwbp, 0)) * 0.5 AS lwbpAvg, " +
                "avg(COALESCE(kwhwbp, 0)) * 0.5 AS wbpAvg, " +
                "avg(COALESCE(pemkvarh, 0)) * 0.5 AS pemkvarhAvg " +
                "FROM Billing " +
                "WHERE thblrek IN ?1 " +
                "AND idpel = ?2", thblrekList, idpel)
                .project(KwhPakaiRata2BulanDTO.class)
                .firstResult();
    }

    public PanacheQuery<Billing> findForMonitoringVerifikasi(UnitParam unit, String kdProsesKlp, String thblrek, String dlpd, Page page) {
        if (dlpd != null && !dlpd.equals("SEMUA")) {
            return Billing.find(
                    "unitupi=?1 and unitap=?2 and unitup=?3 and kdprosesklp=?4 and thblrek=?5 and dlpd=?6 and postingbilling='2' and tglVerifikasiDlpd is null",
                    unit.unitupi, unit.unitap, unit.unitup, kdProsesKlp, thblrek, dlpd
            ).page(page);
        } else {
            return Billing.find(
                    "unitupi=?1 and unitap=?2 and unitup=?3 and kdprosesklp=?4 and thblrek=?5 and postingbilling='2' and tglVerifikasiDlpd is null",
                    unit.unitupi, unit.unitap, unit.unitup, kdProsesKlp, thblrek
            ).page(page);
        }
    }

    public Uni<Integer> setFlagVerifikasiDlpd(VerifikasiDlpdParam request) {
        return update(
                "ptgVerifikasiDlpd = :petugas, flagVerifikasiDlpd = :flag, alasanVerifikasiDlpd = :alasan, tglVerifikasiDlpd = :tglVerifikasi, postingbilling = :posting " +
                        "where unitup= :unitup and kdprosesklp= :kdprosesklp and thblrek= :thblrek and dlpd= :dlpd and postingbilling='2' and tglVerifikasiDlpd is null",
                Parameters.with("petugas", request.userId)
                        .and("flag", request.isApprove ? 1 : 0)
                        .and("alasan", request.reason)
                        .and("tglVerifikasi", LocalDateTime.now())
                        .and("posting", request.isApprove ? "2" : "1")
                        .and("unitup", request.byKdProses.unitup)
                        .and("kdprosesklp", request.byKdProses.kdProsesKlp)
                        .and("dlpd", request.byKdProses.dlpd)
                        .and("thblrek", request.byKdProses.thblrek)
        );
    }
    public Uni<Integer> setFlagVerifikasiDlpd(
            String userId, Boolean approve, String reason, String unitup,
            String dlpd, String kdProsesKlp, String thblrek
    ) {
        return update(
                "ptgVerifikasiDlpd = :petugas, flagVerifikasiDlpd = :flag, alasanVerifikasiDlpd = :alasan, tglVerifikasiDlpd = :tglVerifikasi, postingbilling = :posting " +
                        "where unitup= :unitup and kdprosesklp= :kdprosesklp and thblrek= :thblrek and dlpd= :dlpd and postingbilling='2' and tglVerifikasiDlpd is null",
                Parameters.with("petugas", userId)
                        .and("flag", approve ? 1 : 0)
                        .and("alasan", reason)
                        .and("tglVerifikasi", LocalDateTime.now())
                        .and("posting", approve ? "2" : "1")
                        .and("unitup", unitup)
                        .and("kdprosesklp", kdProsesKlp)
                        .and("dlpd", dlpd)
                        .and("thblrek", thblrek)
        );
    }
//    public Uni<String> getPostingBillingStatus(String idpel,String thblrek){
//        return
//    }
public Uni<List<CustomerParam>> getParamThblrek(String thblrek,String posting,int page,int size){
    return find("SELECT idpel,thblrek FROM Billing a WHERE thblrek=?1 AND postingbilling=?2 " +
            "AND thblrek=?3 and not exists (SELECT idpel,thblrek FROM InitBilling b WHERE a.idpel=b.idpel)",thblrek,posting,thblrek)
            .project(CustomerParam.class).page(page,size).list();
}
    public Uni<List<CustomerParam>> getPelanggan(String thblrek,String posting,int page,int size){
        return find("SELECT idpel,thblrek FROM Billing WHERE thblrek=?1 AND postingbilling=?2 ",thblrek,posting)
                .project(CustomerParam.class).page(page,size).list();
    }
    public Uni<List<CustomerParam>> getCustomerNoLog(int rows,String thblrek){
        return find("SELECT idpel,thblrek FROM Billing a WHERE a.thblrek=?1 and not exists " +
                        "(select idpel,thblrek from LogJobHitungBilling b where a.idpel=b.idpel)",
                thblrek)
                .project(CustomerParam.class).page(0,rows).list();
    }
    public Uni<List<Map<String, Object>>> findForMonitoringVerifikasiDlpdKolektif(UnitParam unit, String kdProsesKlp, String thblrek, String dlpd, int pageIndex, int pageSize) {
        String sql;
        if (dlpd != null && !dlpd.equals("SEMUA")) {
            sql = """
                    select *
                    from (
                        select * from otc.vw_data_bill_dlpd_belum_verifikasi
                        where thblrek=$1 and unitup=$2 and kdprosesklp=$3 and dlpd=$4
                        )
                        OFFSET $5 LIMIT $6
                    """;
            return client.preparedQuery(sql)
                    .execute(Tuple.of(
                            thblrek,
                            unit.unitup,
                            kdProsesKlp,
                            new BigDecimal(dlpd),
                            pageIndex * pageSize,
                            pageSize
                    ))
                    .onItem().transform(rows -> {
                        List<Map<String, Object>> resultList = new ArrayList<>();
                        for (Row row : rows) {
                            // Menggunakan toJson() agar dapat langsung dikonversi ke Map
                            Map<String, Object> map = row.toJson().getMap();
                            resultList.add(map);
                        }
                        return resultList;
                    });
        }else {
            sql = """
                    select *
                    from (
                        select * from otc.vw_data_bill_dlpd_belum_verifikasi
                        where thblrek=$1 and unitup=$2 and kdprosesklp=$3
                        )
                        OFFSET $4 LIMIT $5
                    """;
            return client.preparedQuery(sql)
                    .execute(Tuple.of(
                            thblrek,
                            unit.unitup,
                            kdProsesKlp,
                            pageIndex * pageSize,
                            pageSize
                    ))
                    .onItem().transform(rows -> {
                        List<Map<String, Object>> resultList = new ArrayList<>();
                        for (Row row : rows) {
                            // Menggunakan toJson() agar dapat langsung dikonversi ke Map
                            Map<String, Object> map = row.toJson().getMap();
                            resultList.add(map);
                        }
                        return resultList;
                    });
        }
    }

    public Uni<Long> getCountMonitoringBillingDlpdDetil(String unitupi, String unitap, String unitup, String kdProsesKlp, String thblrek, String dlpd){
        String sql ="";
        if (dlpd != null && !dlpd.equals("SEMUA")) {
            sql = """
                    select count(0) total from otc.vw_monitoring_dlpd_detil
                        where thblrek=$1 and unitup=$2 and kdprosesklp=$3 and dlpd=$4
                    """;
            return client.preparedQuery(sql)
                    .execute(Tuple.of(
                            thblrek, unitup, kdProsesKlp, new BigDecimal(dlpd)
                    ))
                    .onItem().transform(rows -> {
                        Row row = rows.iterator().next();
                        return row.getLong("total");
                    });
        }else{
            sql = """
                    select count(0) total from otc.vw_monitoring_dlpd_detil
                        where thblrek=$1 and unitup=$2 and kdprosesklp=$3
                    """;
            return client.preparedQuery(sql)
                    .execute(Tuple.of(
                            thblrek, unitup, kdProsesKlp
                    ))
                    .onItem().transform(rows -> {
                        Row row = rows.iterator().next();
                        return row.getLong("total");
                    });
        }


    }

    public Uni<List<Map<String, Object>>> getMonitoringBillingDlpdDetil(String unitupi, String unitap, String unitup, String kdProsesKlp, String thblrek, String dlpd, int pageIndex, int pageSize) {
        String sql;
        if (dlpd != null && !dlpd.equals("SEMUA")) {
            sql = """
                    select *
                    from (
                        select * from otc.vw_monitoring_dlpd_detil
                        where thblrek=$1 and unitup=$2 and kdprosesklp=$3 and dlpd=$4
                        )
                        OFFSET $5 LIMIT $6
                    """;
            return client.preparedQuery(sql)
                    .execute(Tuple.of(
                            thblrek,
                            unitup,
                            kdProsesKlp,
                            new BigDecimal(dlpd),
                            pageIndex * pageSize,
                            pageSize
                    ))
                    .onItem().transform(rows -> {
                        List<Map<String, Object>> resultList = new ArrayList<>();
                        for (Row row : rows) {
                            // Menggunakan toJson() agar dapat langsung dikonversi ke Map
                            Map<String, Object> map = row.toJson().getMap();
                            resultList.add(map);
                        }
                        return resultList;
                    });
        }else {
            sql = """
                    select *
                    from (
                        select * from otc.vw_monitoring_dlpd_detil
                        where thblrek=$1 and unitup=$2 and kdprosesklp=$3
                        )
                        OFFSET $4 LIMIT $5
                    """;
            return client.preparedQuery(sql)
                    .execute(Tuple.of(
                            thblrek,
                            unitup,
                            kdProsesKlp,
                            pageIndex * pageSize,
                            pageSize
                    ))
                    .onItem().transform(rows -> {
                        List<Map<String, Object>> resultList = new ArrayList<>();
                        for (Row row : rows) {
                            // Menggunakan toJson() agar dapat langsung dikonversi ke Map
                            Map<String, Object> map = row.toJson().getMap();
                            resultList.add(map);
                        }
                        return resultList;
                    });
        }
    }

    public Uni<Long> getCountMonitoringBillingDlpdRekap(String unitupi, String unitap, String unitup, String kdProsesKlp, String thblrek, String dlpd){
        String sql ="";
        if (dlpd != null && !dlpd.equals("SEMUA")) {
            sql = """
                    select count(0) total from otc.vw_monitoring_dlpd_rekap
                        where thblrek=$1 and unitup=$2 and kdprosesklp=$3 and dlpd=$4
                    """;
            return client.preparedQuery(sql)
                    .execute(Tuple.of(
                            thblrek, unitup, kdProsesKlp, new BigDecimal(dlpd)
                    ))
                    .onItem().transform(rows -> {
                        Row row = rows.iterator().next();
                        return row.getLong("total");
                    });
        }else{
            sql = """
                    select count(0) total from otc.vw_monitoring_dlpd_rekap
                        where thblrek=$1 and unitup=$2 and kdprosesklp=$3
                    """;
            return client.preparedQuery(sql)
                    .execute(Tuple.of(
                            thblrek, unitup, kdProsesKlp
                    ))
                    .onItem().transform(rows -> {
                        Row row = rows.iterator().next();
                        return row.getLong("total");
                    });
        }


    }

    public Uni<List<Map<String, Object>>> getMonitoringBillingDlpdRekap(String unitupi, String unitap, String unitup, String kdProsesKlp, String thblrek, String dlpd, int pageIndex, int pageSize) {
        String sql;
        if (dlpd != null && !dlpd.equals("SEMUA")) {
            sql = """
                    select *
                    from (
                        select * from otc.vw_monitoring_dlpd_rekap
                        where thblrek=$1 and unitup=$2 and kdprosesklp=$3 and dlpd=$4
                        )
                        OFFSET $5 LIMIT $6
                    """;
            return client.preparedQuery(sql)
                    .execute(Tuple.of(
                            thblrek,
                            unitup,
                            kdProsesKlp,
                            new BigDecimal(dlpd),
                            pageIndex * pageSize,
                            pageSize
                    ))
                    .onItem().transform(rows -> {
                        List<Map<String, Object>> resultList = new ArrayList<>();
                        for (Row row : rows) {
                            // Menggunakan toJson() agar dapat langsung dikonversi ke Map
                            Map<String, Object> map = row.toJson().getMap();
                            resultList.add(map);
                        }
                        return resultList;
                    });
        }else {
            sql = """
                    select *
                    from (
                        select * from otc.vw_monitoring_dlpd_rekap
                        where thblrek=$1 and unitup=$2 and kdprosesklp=$3
                        )
                        OFFSET $4 LIMIT $5
                    """;
            return client.preparedQuery(sql)
                    .execute(Tuple.of(
                            thblrek,
                            unitup,
                            kdProsesKlp,
                            pageIndex * pageSize,
                            pageSize
                    ))
                    .onItem().transform(rows -> {
                        List<Map<String, Object>> resultList = new ArrayList<>();
                        for (Row row : rows) {
                            // Menggunakan toJson() agar dapat langsung dikonversi ke Map
                            Map<String, Object> map = row.toJson().getMap();
                            resultList.add(map);
                        }
                        return resultList;
                    });
        }
    }

    public Uni<List<Billing>> findCheckForFlagDpp(String thblrek, String unitap, String unitup, String kdProsesKlp) {
        return find(
                "postingbilling in ('0', '1') and unitap = :unitap and unitup = :unitup and thblrek = :thblrek and " +
                        "kdprosesklp = :kdprosesklp and trim(lower(jnsmut)) not like '%n%' and trim(lower(jnsmut)) not like '%o%' ",
                Parameters.with("thblrek", thblrek).and("unitap", unitap)
                        .and("unitup", unitup).and("kdprosesklp", kdProsesKlp)
        ).list();
    }

    public Uni<Long> checkIfAnyDataHasNotVerificationDlpd(String thblrek, String unitap, String unitup, String kdprosesklp) {
        return count(
                "thblrek= :thblrek and unitap= :unitap and (unitup= :unitup or 'SEMUA' = :unitup) and kdprosesklp= :kdprosesklp " +
                        "and trim(lower(jnsmut)) not like '%n%' and trim(lower(jnsmut)) not like '%o%' and postingbilling='2' and " +
                        "tglVerifikasiDlpd is null",
                Parameters.with("thblrek", thblrek)
                        .and("unitap", unitap).and("unitup", unitup).and("kdprosesklp", kdprosesklp)
        );
    }

    public Uni<List<Billing>> findByThblAndUnitAndKdProsesKlp(String thblrek, String unitap, String unitup, String kdprosesklp) {
        return find(
                "thblrek= :thblrek and unitap= :unitap and (unitup= :unitup or 'SEMUA' = :unitup) and kdprosesklp= :kdprosesklp " +
                        "and trim(lower(jnsmut)) not like '%n%' and trim(lower(jnsmut)) not like '%o%' and postingbilling='2' and " +
                        "tglVerifikasiDlpd is not null",
                Parameters.with("thblrek", thblrek)
                        .and("unitap", unitap).and("unitup", unitup).and("kdprosesklp", kdprosesklp)
        ).list();
    }

    public Uni<Integer> setUpdateFlagDpp(String idpel, String thblrek, String userId, String batch, String kdgerak) {
        return update(
                "tglsah= :tglsah, sahby= :sahby, batch= :batch, kdgerak= :kdgerak, postingbilling='3' "+
                        "where idpel= :idpel and thblrek= :thblrek and postingbilling='2'",
                Parameters.with("idpel", idpel).and("thblrek", thblrek)
                        .and("tglsah", LocalDateTime.now()).and("sahby", userId).and("batch", batch)
                        .and("kdgerak", kdgerak)
        );
    }

    public Uni<DataBillJamNyalaDto> findRekapJamNyala(String thblrek, String unitap, String unitup, String kdprosesklp) {
        return find(
                """
                       SELECT COUNT(*) AS lbrTotal,
                            SUM(CASE WHEN b.jamnyala > 720 THEN 1 ELSE 0 END) AS lbrJnMax,
                            SUM(CASE WHEN b.jamnyala > 2000 THEN 1 ELSE 0 END) AS lbrJn2000,
                            SUM(CASE WHEN b.jamnyala > 720 AND b.jamnyala < 2000 THEN 1 ELSE 0 END) AS lbrJn720,
                            SUM(CASE WHEN b.pemkvarh >= b.pemkwh AND b.pemkvarh > 0 THEN 1 ELSE 0 END) AS lbrKvarhLebih,
                            SUM(CASE WHEN b.pemkvarh > 2 * b.pemkwh THEN 1 ELSE 0 END) AS lbrKvarhLebih2,
                            SUM(CASE WHEN TO_DATE(b.tglbacaakhir, 'YYYYMMDD') > TO_DATE(b.thblrek || '01', 'YYYYMMDD')
                                     AND (b.tarif <> 'P3' AND b.kdpt_2 NOT IN ('2', '3')) THEN 1 ELSE 0 END) AS lbrTgl1,
                            SUM(CASE WHEN b.slalwbp = b.sahlwbp AND b.slawbp = b.sahwbp THEN 1 ELSE 0 END) AS lbrStSama
                        FROM Billing b
                        WHERE b.thblrek = :thblrek
                          AND b.unitap = :unitap
                          AND b.unitup = :unitup
                          AND b.kdprosesklp = :kdprosesklp
                          AND TRIM(LOWER(b.jnsmut)) NOT LIKE '%n%'
                          AND TRIM(LOWER(b.jnsmut)) NOT LIKE '%o%'
                          AND b.postingbilling = '3'
                        GROUP BY b.thblrek, b.unitap, b.unitup, b.kdprosesklp
                   """, Parameters.with("thblrek", thblrek)
                        .and("unitap", unitap)
                        .and("unitup", unitup)
                        .and("kdprosesklp", kdprosesklp))
                .project(DataBillJamNyalaDto.class)
                .firstResult();
    }
    public Uni<Long> getCountForMonitoringVerifikasiDlpdKolektif(UnitParam unit, String kdProsesKlp, String thblrek, String dlpd){
        String sql ="";
        if (dlpd != null && !dlpd.equals("SEMUA")) {
            sql = """
                    select count(0) total from otc.vw_data_bill_dlpd_belum_verifikasi
                        where thblrek=$1 and unitup=$2 and kdprosesklp=$3 and dlpd=$4
                    """;
            return client.preparedQuery(sql)
                    .execute(Tuple.of(
                            thblrek, unit.unitup, kdProsesKlp, new BigDecimal(dlpd)
                    ))
                    .onItem().transform(rows -> {
                        Row row = rows.iterator().next();
                        return row.getLong("total");
                    });
        }else{
            sql = """
                    select count(0) total from otc.vw_data_bill_dlpd_belum_verifikasi
                        where thblrek=$1 and unitup=$2 and kdprosesklp=$3
                    """;
            return client.preparedQuery(sql)
                    .execute(Tuple.of(
                            thblrek, unit.unitup, kdProsesKlp
                    ))
                    .onItem().transform(rows -> {
                        Row row = rows.iterator().next();
                        return row.getLong("total");
                    });
        }


    }
    public Uni<Billing> findByIdpelThblrekPostingBilling(String idpel, String thblrek) {
        return find("idpel=?1 and thblrek=?2 and postingbilling in('1','2')", idpel, thblrek).firstResult();
    }
    public Uni<Billing> find720ByIdpelThblrek(String idpel, String thblrek){
        return find("idpel=?1 and thblrek=?2 and dlpd=10", idpel, thblrek).firstResult();
    }
    public Uni<Billing> findByIdpelThblrekPostingBilling720(String idpel, String thblrek) {
        return find("idpel=?1 and thblrek=?2 and postingbilling in('1','2') and dlpd=10", idpel, thblrek).firstResult();
    }
    public Uni<List<Map<String, Object>>> getMonitoringBillingRekap(String unitupi, String unitap, String unitup, String thblrek, int pageIndex, int pageSize) {
        String sql;
        if (unitup != null && !unitup.isEmpty()) {
            sql = """
                select *
                from (
                    select * from otc.vw_monitoring_billing_rekap
                    where thblrek=$1 and unitup=$2
                )
                OFFSET $3 LIMIT $4
            """;
        } else {
            sql = """
                select *
                from (
                    select * from otc.vw_monitoring_billing_rekap
                    where thblrek=$1 and unitap=$2
                )
                OFFSET $3 LIMIT $4
            """;
        }

        return client.preparedQuery(sql)
                .execute(Tuple.of(
                        thblrek,
                        (unitup != null && !unitup.isEmpty()) ? unitup : unitap,
                        pageIndex * pageSize,
                        pageSize
                ))
                .onItem().transform(rows -> {
                    List<Map<String, Object>> resultList = new ArrayList<>();
                    for (Row row : rows) {
                        // Menggunakan toJson() agar dapat langsung dikonversi ke Map
                        Map<String, Object> map = row.toJson().getMap();
                        resultList.add(map);
                    }
                    return resultList;
                });
    }

    public Uni<Long> getCountMonitoringBillingDetil(String unitupi, String unitap, String unitup, String thblrek){
        String sql ="";
        if (unitup != null && !unitup.isEmpty()) {
            sql = """
                        select count(0) total
                        from otc.vw_monitoring_billing_detil
                        where thblrek=$1 and unitup=$2
                    """;
        }else{
            sql = """
                        select count(0) total
                        from otc.vw_monitoring_billing_detil
                        where thblrek=$1 and unitap=$2
                    """;
        }
        return client.preparedQuery(sql)
                .execute(Tuple.of(
                        thblrek, (unitup != null && !unitup.isEmpty()) ? unitup : unitap
                ))
                .onItem().transform(rows -> {
                    Row row = rows.iterator().next();
                    return row.getLong("total");
                });

    }
    public Uni<Long> getCountMonitoringBillingRekap(String unitupi, String unitap, String unitup, String thblrek){
        String sql ="";
        if (unitup != null && !unitup.isEmpty()) {
            sql = """
                        select count(0) total
                        from otc.vw_monitoring_billing_rekap
                        where thblrek=$1 and unitup=$2
                    """;
        }else{
            sql = """
                        select count(0) total
                        from otc.vw_monitoring_billing_rekap
                        where thblrek=$1 and unitap=$2
                    """;
        }
        return client.preparedQuery(sql)
                .execute(Tuple.of(
                        thblrek, (unitup != null && !unitup.isEmpty()) ? unitup : unitap
                ))
                .onItem().transform(rows -> {
                    Row row = rows.iterator().next();
                    return row.getLong("total");
                });

    }
    public Uni<List<Map<String, Object>>> getMonitoringBillingDetil(String unitupi, String unitap, String unitup, String thblrek, int pageIndex, int pageSize) {
        String sql;
        if (unitup != null && !unitup.isEmpty()) {
            sql = """
                select *
                from (
                    select * from otc.vw_monitoring_billing_detil
                    where thblrek=$1 and unitup=$2
                )
                OFFSET $3 LIMIT $4
            """;
        } else {
            sql = """
                select *
                from (
                    select * from otc.vw_monitoring_billing_detil
                    where thblrek=$1 and unitap=$2
                )
                OFFSET $3 LIMIT $4
            """;
        }

        return client.preparedQuery(sql)
                .execute(Tuple.of(
                        thblrek,
                        (unitup != null && !unitup.isEmpty()) ? unitup : unitap,
                        pageIndex * pageSize,
                        pageSize
                ))
                .onItem().transform(rows -> {
                    List<Map<String, Object>> resultList = new ArrayList<>();
                    for (Row row : rows) {
                        // Menggunakan toJson() agar dapat langsung dikonversi ke Map
                        Map<String, Object> map = row.toJson().getMap();
                        resultList.add(map);
                    }
                    return resultList;
                });
    }

}
