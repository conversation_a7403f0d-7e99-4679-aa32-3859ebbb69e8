package com.iconplus.ap2t.data.repository.billing;

import com.iconplus.ap2t.common.param.CustomerParam;
import com.iconplus.ap2t.common.response.engine.ProcessItem;
import com.iconplus.ap2t.common.response.engine.TransactionItem;
import com.iconplus.ap2t.data.entity.billing.WorkflowExecution;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.panache.common.Sort;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

@ApplicationScoped
public class WExecRepository implements PanacheRepository<WorkflowExecution> {
    @Override
    public Uni<WorkflowExecution> persist(WorkflowExecution workflowExecution) {
        return Panache.withTransaction(workflowExecution::persist);
    }

    @Override
    public Uni<WorkflowExecution> persistAndFlush(WorkflowExecution workflowExecution) {
        return Panache.withTransaction(workflowExecution::persistAndFlush);
    }

    public Uni<List<TransactionItem>> getTransactions(int page, int size){
        return find("select idpel,thblrek,processId,workflowName,statusCode,executionStart," +
                "executionEnd,resultTime,message from WorkflowExecution", Sort.by("executionStart", Sort.Direction.Descending))
                .project(TransactionItem.class).page(page,size).list();
    }

    public Uni<List<WorkflowExecution>> getAll(int page,int size) {
        return findAll(Sort.by("executionStart", Sort.Direction.Descending)).page(page,size).list();
    }
    public Uni<List<WorkflowExecution>> search(CustomerParam param){
        return find("idpel=?1 AND thblrek=?2",param.idpel,param.thblrek).list();
    }
    public Uni<List<ProcessItem>> getParentProcess(String flag, String thblrek, int page, int size){
        return find("SELECT parentProcessId,count(*) as jumlah FROM WorkflowExecution WHERE flagProcess=?1 AND thblrek=?2 " +
                "AND idpel not in (SELECT idpel FROM Billing WHERE postingbilling='2') " +
                "GROUP BY parentProcessId",flag,thblrek)
                .project(ProcessItem.class).page(page,size).list();
    }
    public Uni<List<WorkflowExecution>> getProcess(String parentProcessId){
        return find("parentProcessId=?1",Sort.by("executionStart", Sort.Direction.Ascending),parentProcessId
        ).list();
    }
    public Uni<Integer> updateFlags(String flag,String idpel,String thblrek){
        return update("SET flagProcess=?1 WHERE idpel=?2 AND thblrek=?3",flag,idpel,thblrek);
    }
    public Uni<Integer> updateFlagsByProcess(String flag,String processId){
        return update("SET flagProcess=?1 WHERE parentProcessId=?2",flag,processId);
    }
    public Uni<String> getOutputProcess(String idpel,String thblrek){
        return find("SELECT jsonBilling FROM WorkflowExecution WHERE idpel=?1 AND thblrek=?2 AND jsonBilling is not NULL",idpel,thblrek)
                .project(String.class).firstResult();
    }
}
