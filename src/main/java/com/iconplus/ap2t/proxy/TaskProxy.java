package com.iconplus.ap2t.proxy;

import com.iconplus.ap2t.common.definition.workflow.TaskDef;
import com.iconplus.ap2t.common.exception.NotFoundExceptionMapper;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import java.util.List;

@RegisterRestClient(configKey = "conductor.proxy")
@Path("/api/metadata/taskdefs")
@RegisterProvider(NotFoundExceptionMapper.class)
public interface TaskProxy {
    @GET
    @Path("/")
    List<TaskDef> getTaskList();
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> createNewTask(List<TaskDef> content);
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> updateTask(TaskDef taskDef);
    @GET
    @Path("/{tasktype}")
    Uni<Response> getTaskDetail(@PathParam("tasktype") String taskName);
    @DELETE
    @Path("/{tasktype}")
    Uni<Response> deleteTask(@PathParam("tasktype") String taskName);
}
