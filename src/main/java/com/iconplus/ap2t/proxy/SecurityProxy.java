package com.iconplus.ap2t.proxy;

import com.iconplus.ap2t.common.param.TwoFactorParam;
import com.iconplus.ap2t.proxy.param.JWTResponse;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.parameters.RequestBody;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;


@RegisterRestClient(configKey = "security.proxy")
public interface SecurityProxy {
    @POST
    @Path("/auth/login")
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> authLogin(@FormParam("email") String email,@FormParam("password") String password);

    @POST
    @Path("/jwt/check")
    @Consumes(MediaType.TEXT_PLAIN)
    @Produces(MediaType.APPLICATION_JSON)
    Uni<JWTResponse> checkJwt(String token);

    @POST
    @Path("/auth/twofactor")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> twoFactor(@RequestBody TwoFactorParam param);
}
