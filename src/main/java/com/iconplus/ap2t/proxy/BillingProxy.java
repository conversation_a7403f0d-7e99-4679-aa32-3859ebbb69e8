package com.iconplus.ap2t.proxy;

import com.iconplus.ap2t.common.param.CustomerParam;
import com.iconplus.ap2t.common.param.output.OutputRunnerMutasi;
import com.iconplus.ap2t.common.param.output.OutputRunnerNormal;
import com.iconplus.ap2t.common.response.APIResponse;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.parameters.RequestBody;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

@RegisterRestClient(configKey = "billing.proxy")
@Path("/api")
public interface BillingProxy {
    @GET
    @Path("/pelanggan/search")
    @Produces(MediaType.APPLICATION_JSON)
    Response searchPelanggan(@QueryParam("q")String keyword);
    @GET
    @Path("/billing/pelanggan")
    @Produces(MediaType.APPLICATION_JSON)
    Response searchDataBilling(@QueryParam("idpel")String idpel,@QueryParam("thblrek") String thblrek);
    @POST
    @Path("/inisialiasi-billing")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> inisialisasi(@RequestBody CustomerParam body);
    @POST
    @Path("/save-billing")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> saveBilling(@RequestBody OutputRunnerNormal body);
    @POST
    @Path("/save-billing-mutasi")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    Uni<APIResponse> saveBillingMutasi(@RequestBody OutputRunnerMutasi body);
    @GET
    @Path("/billing/init")
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> getInit(@QueryParam("idpel") String idpel,@QueryParam("thblrek") String thblrek);
}
