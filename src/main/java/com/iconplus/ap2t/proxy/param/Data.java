package com.iconplus.ap2t.proxy.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.iconplus.ap2t.common.param.secman.Jabatan;
import io.quarkus.runtime.annotations.RegisterForReflection;

@RegisterForReflection
public class Data {

	@JsonProperty("status_login")
	private String statusLogin;

	@JsonProperty("ponsel")
	private String ponsel;

	@JsonProperty("jenis_pp")
	private String jenisPp;

	@JsonProperty("jabatan")
	private Jabatan jabatan;

	@JsonProperty("level_id")
	private int levelId;

	@JsonProperty("kode_unit")
	private String kodeUnit;

	@JsonProperty("avatar")
	private String avatar;

	@JsonProperty("tfa_code")
	private String tfaCode;

	@JsonProperty("status_approval")
	private String statusApproval;

	@JsonProperty("nip")
	private String nip;

	@JsonProperty("name")
	private String name;

	@JsonProperty("id")
	private String id;

	@JsonProperty("email")
	private String email;

	public String getStatusLogin(){
		return statusLogin;
	}

	public String getPonsel(){
		return ponsel;
	}

	public String getJenisPp(){
		return jenisPp;
	}

	public Jabatan getJabatan(){
		return jabatan;
	}

	public int getLevelId(){
		return levelId;
	}

	public String getKodeUnit(){
		return kodeUnit;
	}

	public String getAvatar(){
		return avatar;
	}

	public String getTfaCode(){
		return tfaCode;
	}

	public String getStatusApproval(){
		return statusApproval;
	}

	public String getNip(){
		return nip;
	}

	public String getName(){
		return name;
	}

	public String getId(){
		return id;
	}

	public String getEmail(){
		return email;
	}
}