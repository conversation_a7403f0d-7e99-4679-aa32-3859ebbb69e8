package com.iconplus.ap2t.proxy.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

@RegisterForReflection
public class JWTResponse {

	@JsonProperty("data")
	public Data data;

	@JsonProperty("message")
	public String message;

	@JsonProperty("status")
	public Boolean status;
	public JWTResponse(){}
	@Override
	public String toString() {
		return "JWTResponse [data=" + data + ", message=" + message + ", status=" + status + "]";
	}
	
	
}