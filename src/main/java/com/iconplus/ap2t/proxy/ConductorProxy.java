package com.iconplus.ap2t.proxy;

import com.iconplus.ap2t.common.definition.workflow.Workflow;
import com.iconplus.ap2t.common.exception.NotFoundExceptionMapper;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.parameters.RequestBody;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import java.util.List;

@RegisterRestClient(configKey = "conductor.proxy")
@Path("/api")
@RegisterProvider(NotFoundExceptionMapper.class)
public interface ConductorProxy {
    @GET
    @Path("/metadata/workflow")
    @Produces(MediaType.APPLICATION_JSON)
    Uni<List<Workflow>> getWorkFlowList();
    @POST
    @Path("/metadata/workflow")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    Response createWorkflow(String content);
    @PUT
    @Path("/metadata/workflow")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> updateWorkflow(String content);
    @GET
    @Path("/metadata/workflow/{name}")
    Uni<Workflow> getWorkflowDetail(@PathParam("name") String name);
    @POST
    @Path("/workflow/{name}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.TEXT_PLAIN)
    Uni<String> startWorkflow(@PathParam("name") String name,
            @QueryParam("version") String version,
            @QueryParam("correlationId") String correlationId,
            @QueryParam("priority") Integer priority,
            @RequestBody String request);
    @GET
    @Path("/workflow/{workflowId}")
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> getWorkflow(
            @PathParam("workflowId") String workflowId,
            @QueryParam("includeTasks") Boolean includeTask);
    @GET
    @Path("/metadata/workflow/names-and-versions")
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> getMetadataWorkflowNamesAndVersions();
    @POST
    @Path("/metadata/workflow/validate")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    Uni<Response> validateWorkflow(@RequestBody String body);
    @DELETE
    @Path("/metadata/workflow/{name}/{version}")
    @Consumes(MediaType.APPLICATION_JSON)
    Uni<Response> deleteWorkflow(@PathParam("name") String name,
                              @PathParam("version") String version);
}
