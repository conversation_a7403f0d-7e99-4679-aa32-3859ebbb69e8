package com.iconplus.ap2t.proxy;

import com.iconplus.ap2t.common.exception.NotFoundExceptionMapper;
import com.iconplus.ap2t.common.response.customer.Customer;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import java.util.List;

@RegisterRestClient(configKey = "crm.proxy")
@Path("/api/customer")
@RegisterProvider(NotFoundExceptionMapper.class)
public interface CrmProxy {
    @GET
    @Path("/list/{page}/{size}")
    List<Customer> getCustomers(@PathParam("page") int page, @PathParam("size") int size);

    @GET
    @Path("/search")
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> search(@QueryParam("keyword") String keyword);

}
