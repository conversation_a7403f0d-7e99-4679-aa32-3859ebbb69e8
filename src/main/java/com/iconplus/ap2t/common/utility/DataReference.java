package com.iconplus.ap2t.common.utility;


public class DataReference {
    private String message = "0";
    private int recordSuccess = 0;
    private int recordFailed = 0;
    private int totalData = 0;
    private int allRecordSuccess = 0;
    private int allTotalData = 0;

    public void reset() {
        setMessage("0");
        setRecordSuccess(0);
        setRecordFailed(0);
        setTotalData(0);
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getRecordSuccess() {
        return recordSuccess;
    }

    public void setRecordSuccess(int recordSuccess) {
        this.recordSuccess = recordSuccess;
    }

    public int getRecordFailed() {
        return recordFailed;
    }

    public void setRecordFailed(int recordFailed) {
        this.recordFailed = recordFailed;
    }

    public int getTotalData() {
        return totalData;
    }

    public void setTotalData(int totalData) {
        this.totalData = totalData;
    }

    public int getAllRecordSuccess() {
        return allRecordSuccess;
    }

    public void setAllRecordSuccess(int allRecordSuccess) {
        this.allRecordSuccess = allRecordSuccess;
    }

    public int getAllTotalData() {
        return allTotalData;
    }

    public void setAllTotalData(int allTotalData) {
        this.allTotalData = allTotalData;
    }

    @Override
    public String toString() {
        return "DataReference{" +
            "message='" + message + '\'' +
            ", recordSuccess=" + recordSuccess +
            ", recordFailed=" + recordFailed +
            ", totalData=" + totalData +
            ", allRecordSuccess=" + allRecordSuccess +
            ", allTotalData=" + allTotalData +
            '}';
    }
}
