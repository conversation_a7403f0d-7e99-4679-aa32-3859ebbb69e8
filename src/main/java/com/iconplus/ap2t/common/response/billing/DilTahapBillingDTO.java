package com.iconplus.ap2t.common.response.billing;

import java.math.BigDecimal;

public class DilTahapBillingDTO {

    private String tarif;
    private BigDecimal daya;
    private Long jmlRealisasi;

    public DilTahapBillingDTO(String tarif, BigDecimal daya, Long jmlRealisasi) {
        this.tarif = tarif;
        this.daya = daya;
        this.jmlRealisasi = jmlRealisasi;
    }

    public BigDecimal getDaya() {
        return daya;
    }

    public void setDaya(BigDecimal daya) {
        this.daya = daya;
    }

    public String getTarif() {
        return tarif;
    }

    public void setTarif(String tarif) {
        this.tarif = tarif;
    }

    public Long getJmlRealisasi() {
        return jmlRealisasi;
    }

    public void setJmlRealisasi(Long jmlRealisasi) {
        this.jmlRealisasi = jmlRealisasi;
    }
}
