package com.iconplus.ap2t.common.response.engine;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

@RegisterForReflection
public class ProcessItem {
    @JsonProperty("parent_process_id")
    public String parentProcessId;
    @JsonProperty("jumlah")
    public Long jumlah;
    public ProcessItem(){}
    public ProcessItem(String parentProcessId, Long jumlah) {
        this.parentProcessId = parentProcessId;
        this.jumlah = jumlah;
    }
}
