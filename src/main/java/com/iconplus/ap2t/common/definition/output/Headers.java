package com.iconplus.ap2t.common.definition.output;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class Headers{

	@JsonProperty("Accept")
	private String accept;

	@JsonProperty("Content-Type")
	private String contentType;

	@JsonProperty("content-length")
	private List<String> contentLength;

	public void setAccept(String accept){
		this.accept = accept;
	}

	public String getAccept(){
		return accept;
	}

	public void setContentType(String contentType){
		this.contentType = contentType;
	}

	public String getContentType(){
		return contentType;
	}

	public void setContentLength(List<String> contentLength){
		this.contentLength = contentLength;
	}

	public List<String> getContentLength(){
		return contentLength;
	}
}