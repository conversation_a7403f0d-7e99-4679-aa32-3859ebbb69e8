package com.iconplus.ap2t.common.definition.diagram;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

@RegisterForReflection
public class NodesItem extends BaseNode{

	@JsonProperty("idFormula")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	public String idFormula;

	@JsonProperty("name")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	public String name;

	@JsonProperty("style")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	public Style style;

	@JsonProperty("id")
	public String id;

	@JsonProperty("label")
	public String label;

	@JsonProperty("position")
	public Position position;

	@JsonProperty("type")
	public String type;

	@JsonProperty("slug")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	public String slug;
}