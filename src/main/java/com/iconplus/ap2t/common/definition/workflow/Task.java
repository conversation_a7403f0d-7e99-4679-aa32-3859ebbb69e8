
package com.iconplus.ap2t.common.definition.workflow;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


@RegisterForReflection
public class Task {

    @JsonProperty("asyncComplete")
    public Boolean asyncComplete;

    @JsonProperty("joinOn")
    public List<Object> joinOn;

    @JsonProperty("optional")
    public Boolean optional;

    @JsonProperty("type")
    public String type;

    @JsonIgnore
    public Object inputParameters;

    @JsonProperty("decisionCases")
    public Object decisionCases;

    @JsonProperty("loopOver")
    public List<Object> loopOver;

    @JsonProperty("name")
    public String name;

    @JsonProperty("description")
    public String description;

    @JsonProperty("startDelay")
    public Integer startDelay;

    @JsonProperty("defaultExclusiveJoinTask")
    public List<Object> defaultExclusiveJoinTask;

    @JsonProperty("permissive")
    public Boolean permissive;

    @JsonProperty("taskReferenceName")
    public String taskReferenceName;

    @JsonProperty("onStateChange")
    public OnStateChange onStateChange;

    @JsonProperty("defaultCase")
    public List<Object> defaultCase;
    @JsonProperty("expression")
    public String expression;
    @JsonProperty("evaluatorType")
    public String evaluatorType;
    @JsonProperty("forkTasks")
    public List<Object> forkTasks;
    @JsonIgnore
    public Map<String, Object> additionalProperties = new LinkedHashMap<String, Object>();

    

}
