package com.iconplus.ap2t.common.definition.output;

import com.fasterxml.jackson.annotation.JsonProperty;

public class HttpRequest{

	@JsonProperty("headers")
	private Headers headers;

	@JsonProperty("method")
	private String method;

	@JsonProperty("body")
	private Body body;

	@JsonProperty("uri")
	private String uri;

	public void setHeaders(Headers headers){
		this.headers = headers;
	}

	public Headers getHeaders(){
		return headers;
	}

	public void setMethod(String method){
		this.method = method;
	}

	public String getMethod(){
		return method;
	}

	public void setBody(Body body){
		this.body = body;
	}

	public Body getBody(){
		return body;
	}

	public void setUri(String uri){
		this.uri = uri;
	}

	public String getUri(){
		return uri;
	}
}