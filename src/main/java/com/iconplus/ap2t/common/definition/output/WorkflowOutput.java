package com.iconplus.ap2t.common.definition.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

import java.util.List;
import java.util.Map;

@RegisterForReflection
public class WorkflowOutput{

	@JsonProperty("variables")
	private Variables variables;

	@JsonProperty("workflowDefinition")
	private WorkflowDefinition workflowDefinition;

	@JsonProperty("lastRetriedTime")
	private int lastRetriedTime;

	@JsonProperty("updateTime")
	private long updateTime;

	@JsonProperty("taskToDomain")
	private TaskToDomain taskToDomain;

	@JsonProperty("workflowName")
	private String workflowName;

	@JsonProperty("priority")
	private int priority;

	@JsonProperty("failedReferenceTaskNames")
	private List<Object> failedReferenceTaskNames;

	@JsonProperty("ownerApp")
	private String ownerApp;

	@JsonProperty("output")
	private Map<String,Object> output;

	@JsonProperty("input")
	private Map<String,Object> input;

	@JsonProperty("failedTaskNames")
	private List<Object> failedTaskNames;

	@JsonProperty("createTime")
	private long createTime;

	@JsonProperty("correlationId")
	private String correlationId;

	@JsonProperty("startTime")
	private long startTime;

	@JsonProperty("endTime")
	private long endTime;

	@JsonProperty("workflowVersion")
	private int workflowVersion;

	@JsonProperty("workflowId")
	private String workflowId;

	@JsonProperty("tasks")
	private List<TasksItem> tasks;

	@JsonProperty("status")
	private String status;

	public void setVariables(Variables variables){
		this.variables = variables;
	}

	public Variables getVariables(){
		return variables;
	}

	public void setWorkflowDefinition(WorkflowDefinition workflowDefinition){
		this.workflowDefinition = workflowDefinition;
	}

	public WorkflowDefinition getWorkflowDefinition(){
		return workflowDefinition;
	}

	public void setLastRetriedTime(int lastRetriedTime){
		this.lastRetriedTime = lastRetriedTime;
	}

	public int getLastRetriedTime(){
		return lastRetriedTime;
	}

	public void setUpdateTime(long updateTime){
		this.updateTime = updateTime;
	}

	public long getUpdateTime(){
		return updateTime;
	}

	public void setTaskToDomain(TaskToDomain taskToDomain){
		this.taskToDomain = taskToDomain;
	}

	public TaskToDomain getTaskToDomain(){
		return taskToDomain;
	}

	public void setWorkflowName(String workflowName){
		this.workflowName = workflowName;
	}

	public String getWorkflowName(){
		return workflowName;
	}

	public void setPriority(int priority){
		this.priority = priority;
	}

	public int getPriority(){
		return priority;
	}

	public void setFailedReferenceTaskNames(List<Object> failedReferenceTaskNames){
		this.failedReferenceTaskNames = failedReferenceTaskNames;
	}

	public List<Object> getFailedReferenceTaskNames(){
		return failedReferenceTaskNames;
	}

	public void setOwnerApp(String ownerApp){
		this.ownerApp = ownerApp;
	}

	public String getOwnerApp(){
		return ownerApp;
	}

	public Map<String, Object> getOutput() {
		return output;
	}

	public void setOutput(Map<String, Object> output) {
		this.output = output;
	}

	public Map<String, Object> getInput() {
		return input;
	}

	public void setInput(Map<String, Object> input) {
		this.input = input;
	}

	public void setFailedTaskNames(List<Object> failedTaskNames){
		this.failedTaskNames = failedTaskNames;
	}

	public List<Object> getFailedTaskNames(){
		return failedTaskNames;
	}

	public void setCreateTime(long createTime){
		this.createTime = createTime;
	}

	public long getCreateTime(){
		return createTime;
	}

	public void setCorrelationId(String correlationId){
		this.correlationId = correlationId;
	}

	public String getCorrelationId(){
		return correlationId;
	}

	public void setStartTime(long startTime){
		this.startTime = startTime;
	}

	public long getStartTime(){
		return startTime;
	}

	public void setEndTime(long endTime){
		this.endTime = endTime;
	}

	public long getEndTime(){
		return endTime;
	}

	public void setWorkflowVersion(int workflowVersion){
		this.workflowVersion = workflowVersion;
	}

	public int getWorkflowVersion(){
		return workflowVersion;
	}

	public void setWorkflowId(String workflowId){
		this.workflowId = workflowId;
	}

	public String getWorkflowId(){
		return workflowId;
	}

	public void setTasks(List<TasksItem> tasks){
		this.tasks = tasks;
	}

	public List<TasksItem> getTasks(){
		return tasks;
	}

	public void setStatus(String status){
		this.status = status;
	}

	public String getStatus(){
		return status;
	}
}