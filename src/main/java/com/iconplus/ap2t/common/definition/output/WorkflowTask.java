package com.iconplus.ap2t.common.definition.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

@RegisterForReflection
public class WorkflowTask{

	@JsonProperty("dynamicForkTasksInputParamName")
	private Object dynamicForkTasksInputParamName;

	@JsonProperty("expression")
	private Object expression;

	@JsonProperty("loopCondition")
	private Object loopCondition;

	@JsonProperty("asyncComplete")
	private boolean asyncComplete;

	@JsonProperty("sink")
	private Object sink;

	@JsonProperty("rateLimited")
	private Object rateLimited;

	@JsonProperty("retryCount")
	private Object retryCount;

	@JsonProperty("subWorkflowParam")
	private Object subWorkflowParam;

	@JsonProperty("description")
	private String description;

	@JsonProperty("optional")
	private boolean optional;

	@JsonProperty("scriptExpression")
	private Object scriptExpression;

	@JsonProperty("type")
	private String type;

	@JsonProperty("inputParameters")
	private InputParameters inputParameters;

	@JsonProperty("dynamicForkJoinTasksParam")
	private Object dynamicForkJoinTasksParam;

	@JsonProperty("evaluatorType")
	private Object evaluatorType;

	@JsonProperty("dynamicTaskNameParam")
	private Object dynamicTaskNameParam;

	@JsonProperty("caseExpression")
	private Object caseExpression;

	@JsonProperty("name")
	private String name;

	@JsonProperty("startDelay")
	private int startDelay;

	@JsonProperty("taskReferenceName")
	private String taskReferenceName;

	@JsonProperty("taskDefinition")
	private TaskDefinition taskDefinition;

	@JsonProperty("caseValueParam")
	private Object caseValueParam;

	@JsonProperty("dynamicForkTasksParam")
	private Object dynamicForkTasksParam;

	public void setDynamicForkTasksInputParamName(Object dynamicForkTasksInputParamName){
		this.dynamicForkTasksInputParamName = dynamicForkTasksInputParamName;
	}

	public Object getDynamicForkTasksInputParamName(){
		return dynamicForkTasksInputParamName;
	}

	public void setExpression(Object expression){
		this.expression = expression;
	}

	public Object getExpression(){
		return expression;
	}

	public void setLoopCondition(Object loopCondition){
		this.loopCondition = loopCondition;
	}

	public Object getLoopCondition(){
		return loopCondition;
	}

	public void setAsyncComplete(boolean asyncComplete){
		this.asyncComplete = asyncComplete;
	}

	public boolean isAsyncComplete(){
		return asyncComplete;
	}

	public void setSink(Object sink){
		this.sink = sink;
	}

	public Object getSink(){
		return sink;
	}

	public void setRateLimited(Object rateLimited){
		this.rateLimited = rateLimited;
	}

	public Object getRateLimited(){
		return rateLimited;
	}

	public void setRetryCount(Object retryCount){
		this.retryCount = retryCount;
	}

	public Object getRetryCount(){
		return retryCount;
	}

	public void setSubWorkflowParam(Object subWorkflowParam){
		this.subWorkflowParam = subWorkflowParam;
	}

	public Object getSubWorkflowParam(){
		return subWorkflowParam;
	}

	public void setDescription(String description){
		this.description = description;
	}

	public String getDescription(){
		return description;
	}

	public void setOptional(boolean optional){
		this.optional = optional;
	}

	public boolean isOptional(){
		return optional;
	}

	public void setScriptExpression(Object scriptExpression){
		this.scriptExpression = scriptExpression;
	}

	public Object getScriptExpression(){
		return scriptExpression;
	}

	public void setType(String type){
		this.type = type;
	}

	public String getType(){
		return type;
	}

	public void setInputParameters(InputParameters inputParameters){
		this.inputParameters = inputParameters;
	}

	public InputParameters getInputParameters(){
		return inputParameters;
	}

	public void setDynamicForkJoinTasksParam(Object dynamicForkJoinTasksParam){
		this.dynamicForkJoinTasksParam = dynamicForkJoinTasksParam;
	}

	public Object getDynamicForkJoinTasksParam(){
		return dynamicForkJoinTasksParam;
	}

	public void setEvaluatorType(Object evaluatorType){
		this.evaluatorType = evaluatorType;
	}

	public Object getEvaluatorType(){
		return evaluatorType;
	}

	public void setDynamicTaskNameParam(Object dynamicTaskNameParam){
		this.dynamicTaskNameParam = dynamicTaskNameParam;
	}

	public Object getDynamicTaskNameParam(){
		return dynamicTaskNameParam;
	}

	public void setCaseExpression(Object caseExpression){
		this.caseExpression = caseExpression;
	}

	public Object getCaseExpression(){
		return caseExpression;
	}

	public void setName(String name){
		this.name = name;
	}

	public String getName(){
		return name;
	}

	public void setStartDelay(int startDelay){
		this.startDelay = startDelay;
	}

	public int getStartDelay(){
		return startDelay;
	}

	public void setTaskReferenceName(String taskReferenceName){
		this.taskReferenceName = taskReferenceName;
	}

	public String getTaskReferenceName(){
		return taskReferenceName;
	}

	public void setTaskDefinition(TaskDefinition taskDefinition){
		this.taskDefinition = taskDefinition;
	}

	public TaskDefinition getTaskDefinition(){
		return taskDefinition;
	}

	public void setCaseValueParam(Object caseValueParam){
		this.caseValueParam = caseValueParam;
	}

	public Object getCaseValueParam(){
		return caseValueParam;
	}

	public void setDynamicForkTasksParam(Object dynamicForkTasksParam){
		this.dynamicForkTasksParam = dynamicForkTasksParam;
	}

	public Object getDynamicForkTasksParam(){
		return dynamicForkTasksParam;
	}
}