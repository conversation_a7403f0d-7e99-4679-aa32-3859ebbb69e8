package com.iconplus.ap2t.common.definition.output;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Data{

	@JsonProperty("rpsewatrafoDil")
	private Object rpsewatrafoDil;

	@JsonProperty("slalwbp")
	private double slalwbp;

	@JsonProperty("angskec")
	private Object angskec;

	@JsonProperty("rpangsc")
	private Object rpangsc;

	@JsonProperty("rpangsb")
	private Object rpangsb;

	@JsonProperty("thblangs1c")
	private Object thblangs1c;

	@JsonProperty("rpangsa")
	private Object rpangsa;

	@JsonProperty("thblangs1b")
	private Object thblangs1b;

	@JsonProperty("kdinkaso")
	private String kdinkaso;

	@JsonProperty("fakmkvam")
	private int fakmkvam;

	@JsonProperty("faktor_fakm")
	private int faktorFakm;

	@JsonProperty("thblangs1a")
	private Object thblangs1a;

	@JsonProperty("slakvarh")
	private Object slakvarh;

	@JsonProperty("tglnyala")
	private String tglnyala;

	@JsonProperty("tarif")
	private String tarif;

	@JsonProperty("kdbacameter")
	private Object kdbacameter;

	@JsonProperty("biaya_beban")
	private int biayaBeban;

	@JsonProperty("id")
	private String id;

	@JsonProperty("angskea")
	private Object angskea;

	@JsonProperty("angskeb")
	private Object angskeb;

	@JsonProperty("status_emin")
	private Object statusEmin;

	@JsonProperty("faradkap")
	private Object faradkap;

	@JsonProperty("fakmkvarh")
	private int fakmkvarh;

	@JsonProperty("jenis_batas_emin")
	private Object jenisBatasEmin;

	@JsonProperty("kdklp")
	private String kdklp;

	@JsonProperty("tahunKe")
	private Object tahunKe;

	@JsonProperty("frt")
	private String frt;

	@JsonProperty("fjn")
	private String fjn;

	@JsonProperty("thblmut")
	private String thblmut;

	@JsonProperty("sahlwbp")
	private double sahlwbp;

	@JsonProperty("sahwbp")
	private int sahwbp;

	@JsonProperty("faktork_value")
	private int faktorkValue;

	@JsonProperty("kdpt")
	private Object kdpt;

	@JsonProperty("lokettgk")
	private Object lokettgk;

	@JsonProperty("faktorn_value")
	private int faktornValue;

	@JsonProperty("lamaangsa")
	private Object lamaangsa;

	@JsonProperty("kdpembmeter")
	private String kdpembmeter;

	@JsonProperty("biaya_kvar")
	private int biayaKvar;

	@JsonProperty("faktorq_value")
	private int faktorqValue;

	@JsonProperty("faktor_fjn")
	private int faktorFjn;

	@JsonProperty("faktor_frt")
	private int faktorFrt;

	@JsonProperty("kdbedajbst")
	private Object kdbedajbst;

	@JsonProperty("dayamaxWbp")
	private int dayamaxWbp;

	@JsonProperty("unitap")
	private String unitap;

	@JsonProperty("daya_max")
	private Object dayaMax;

	@JsonProperty("tglrubah")
	private String tglrubah;

	@JsonProperty("kdppj")
	private String kdppj;

	@JsonProperty("kdkvamaks")
	private Object kdkvamaks;

	@JsonProperty("met_kwh")
	private String metKwh;

	@JsonProperty("faknpremium")
	private Object faknpremium;

	@JsonProperty("dayajbst")
	private Object dayajbst;

	@JsonProperty("daya")
	private int daya;

	@JsonProperty("faktorp_value")
	private int faktorpValue;

	@JsonProperty("fakm")
	private int fakm;

	@JsonProperty("kdam")
	private String kdam;

	@JsonProperty("thblrek")
	private String thblrek;

	@JsonProperty("kdmeterai")
	private Object kdmeterai;

	@JsonProperty("slawbp")
	private Object slawbp;

	@JsonProperty("prosen_ppn")
	private int prosenPpn;

	@JsonProperty("sahkvarh")
	private double sahkvarh;

	@JsonProperty("prosen_ppj")
	private Object prosenPpj;

	@JsonProperty("kdangsa")
	private Object kdangsa;

	@JsonProperty("blok")
	private int blok;

	@JsonProperty("kdproses")
	private String kdproses;

	@JsonProperty("jnsmutAde")
	private Object jnsmutAde;

	@JsonProperty("kddk")
	private String kddk;

	@JsonProperty("jnsmut")
	private String jnsmut;

	@JsonProperty("kdangsc")
	private Object kdangsc;

	@JsonProperty("kdangsb")
	private Object kdangsb;

	@JsonProperty("flagppjangsb")
	private Object flagppjangsb;

	@JsonProperty("flagppjangsa")
	private Object flagppjangsa;

	@JsonProperty("flagppjangsc")
	private Object flagppjangsc;

	@JsonProperty("kdprosesklp")
	private String kdprosesklp;

	@JsonProperty("nama")
	private String nama;

	@JsonProperty("kogol")
	private String kogol;

	@JsonProperty("rpsewakap")
	private Object rpsewakap;

	@JsonProperty("kdmut")
	private String kdmut;

	@JsonProperty("batas_blok2")
	private int batasBlok2;

	@JsonProperty("unitupi")
	private String unitupi;

	@JsonProperty("batas_blok1")
	private int batasBlok1;

	@JsonProperty("lamaangsb")
	private Object lamaangsb;

	@JsonProperty("lamaangsc")
	private Object lamaangsc;

	@JsonProperty("dayabpt")
	private Object dayabpt;

	@JsonProperty("pemda")
	private String pemda;

	@JsonProperty("jenis_batas")
	private String jenisBatas;

	@JsonProperty("kdinvoice")
	private Object kdinvoice;

	@JsonProperty("subkogol")
	private Object subkogol;

	@JsonProperty("kddayabpt")
	private Object kddayabpt;

	@JsonProperty("kdpt_2")
	private Object kdpt2;

	@JsonProperty("emin")
	private int emin;

	@JsonProperty("maxDemand")
	private Object maxDemand;

	@JsonProperty("biaya_pakai1")
	private int biayaPakai1;

	@JsonProperty("biaya_pakai2")
	private int biayaPakai2;

	@JsonProperty("idpel")
	private String idpel;

	@JsonProperty("biaya_pakai3")
	private int biayaPakai3;

	@JsonProperty("flagsewakap")
	private Object flagsewakap;

	@JsonProperty("nopel")
	private String nopel;

	@JsonProperty("kdind")
	private Object kdind;

	@JsonProperty("unitup")
	private String unitup;

	@JsonProperty("kdbpt")
	private Object kdbpt;

	public void setRpsewatrafoDil(Object rpsewatrafoDil){
		this.rpsewatrafoDil = rpsewatrafoDil;
	}

	public Object getRpsewatrafoDil(){
		return rpsewatrafoDil;
	}

	public void setSlalwbp(double slalwbp){
		this.slalwbp = slalwbp;
	}

	public double getSlalwbp(){
		return slalwbp;
	}

	public void setAngskec(Object angskec){
		this.angskec = angskec;
	}

	public Object getAngskec(){
		return angskec;
	}

	public void setRpangsc(Object rpangsc){
		this.rpangsc = rpangsc;
	}

	public Object getRpangsc(){
		return rpangsc;
	}

	public void setRpangsb(Object rpangsb){
		this.rpangsb = rpangsb;
	}

	public Object getRpangsb(){
		return rpangsb;
	}

	public void setThblangs1c(Object thblangs1c){
		this.thblangs1c = thblangs1c;
	}

	public Object getThblangs1c(){
		return thblangs1c;
	}

	public void setRpangsa(Object rpangsa){
		this.rpangsa = rpangsa;
	}

	public Object getRpangsa(){
		return rpangsa;
	}

	public void setThblangs1b(Object thblangs1b){
		this.thblangs1b = thblangs1b;
	}

	public Object getThblangs1b(){
		return thblangs1b;
	}

	public void setKdinkaso(String kdinkaso){
		this.kdinkaso = kdinkaso;
	}

	public String getKdinkaso(){
		return kdinkaso;
	}

	public void setFakmkvam(int fakmkvam){
		this.fakmkvam = fakmkvam;
	}

	public int getFakmkvam(){
		return fakmkvam;
	}

	public void setFaktorFakm(int faktorFakm){
		this.faktorFakm = faktorFakm;
	}

	public int getFaktorFakm(){
		return faktorFakm;
	}

	public void setThblangs1a(Object thblangs1a){
		this.thblangs1a = thblangs1a;
	}

	public Object getThblangs1a(){
		return thblangs1a;
	}

	public void setSlakvarh(Object slakvarh){
		this.slakvarh = slakvarh;
	}

	public Object getSlakvarh(){
		return slakvarh;
	}

	public void setTglnyala(String tglnyala){
		this.tglnyala = tglnyala;
	}

	public String getTglnyala(){
		return tglnyala;
	}

	public void setTarif(String tarif){
		this.tarif = tarif;
	}

	public String getTarif(){
		return tarif;
	}

	public void setKdbacameter(Object kdbacameter){
		this.kdbacameter = kdbacameter;
	}

	public Object getKdbacameter(){
		return kdbacameter;
	}

	public void setBiayaBeban(int biayaBeban){
		this.biayaBeban = biayaBeban;
	}

	public int getBiayaBeban(){
		return biayaBeban;
	}

	public void setId(String id){
		this.id = id;
	}

	public String getId(){
		return id;
	}

	public void setAngskea(Object angskea){
		this.angskea = angskea;
	}

	public Object getAngskea(){
		return angskea;
	}

	public void setAngskeb(Object angskeb){
		this.angskeb = angskeb;
	}

	public Object getAngskeb(){
		return angskeb;
	}

	public void setStatusEmin(Object statusEmin){
		this.statusEmin = statusEmin;
	}

	public Object getStatusEmin(){
		return statusEmin;
	}

	public void setFaradkap(Object faradkap){
		this.faradkap = faradkap;
	}

	public Object getFaradkap(){
		return faradkap;
	}

	public void setFakmkvarh(int fakmkvarh){
		this.fakmkvarh = fakmkvarh;
	}

	public int getFakmkvarh(){
		return fakmkvarh;
	}

	public void setJenisBatasEmin(Object jenisBatasEmin){
		this.jenisBatasEmin = jenisBatasEmin;
	}

	public Object getJenisBatasEmin(){
		return jenisBatasEmin;
	}

	public void setKdklp(String kdklp){
		this.kdklp = kdklp;
	}

	public String getKdklp(){
		return kdklp;
	}

	public void setTahunKe(Object tahunKe){
		this.tahunKe = tahunKe;
	}

	public Object getTahunKe(){
		return tahunKe;
	}

	public void setFrt(String frt){
		this.frt = frt;
	}

	public String getFrt(){
		return frt;
	}

	public void setFjn(String fjn){
		this.fjn = fjn;
	}

	public String getFjn(){
		return fjn;
	}

	public void setThblmut(String thblmut){
		this.thblmut = thblmut;
	}

	public String getThblmut(){
		return thblmut;
	}

	public void setSahlwbp(double sahlwbp){
		this.sahlwbp = sahlwbp;
	}

	public double getSahlwbp(){
		return sahlwbp;
	}

	public void setSahwbp(int sahwbp){
		this.sahwbp = sahwbp;
	}

	public int getSahwbp(){
		return sahwbp;
	}

	public void setFaktorkValue(int faktorkValue){
		this.faktorkValue = faktorkValue;
	}

	public int getFaktorkValue(){
		return faktorkValue;
	}

	public void setKdpt(Object kdpt){
		this.kdpt = kdpt;
	}

	public Object getKdpt(){
		return kdpt;
	}

	public void setLokettgk(Object lokettgk){
		this.lokettgk = lokettgk;
	}

	public Object getLokettgk(){
		return lokettgk;
	}

	public void setFaktornValue(int faktornValue){
		this.faktornValue = faktornValue;
	}

	public int getFaktornValue(){
		return faktornValue;
	}

	public void setLamaangsa(Object lamaangsa){
		this.lamaangsa = lamaangsa;
	}

	public Object getLamaangsa(){
		return lamaangsa;
	}

	public void setKdpembmeter(String kdpembmeter){
		this.kdpembmeter = kdpembmeter;
	}

	public String getKdpembmeter(){
		return kdpembmeter;
	}

	public void setBiayaKvar(int biayaKvar){
		this.biayaKvar = biayaKvar;
	}

	public int getBiayaKvar(){
		return biayaKvar;
	}

	public void setFaktorqValue(int faktorqValue){
		this.faktorqValue = faktorqValue;
	}

	public int getFaktorqValue(){
		return faktorqValue;
	}

	public void setFaktorFjn(int faktorFjn){
		this.faktorFjn = faktorFjn;
	}

	public int getFaktorFjn(){
		return faktorFjn;
	}

	public void setFaktorFrt(int faktorFrt){
		this.faktorFrt = faktorFrt;
	}

	public int getFaktorFrt(){
		return faktorFrt;
	}

	public void setKdbedajbst(Object kdbedajbst){
		this.kdbedajbst = kdbedajbst;
	}

	public Object getKdbedajbst(){
		return kdbedajbst;
	}

	public void setDayamaxWbp(int dayamaxWbp){
		this.dayamaxWbp = dayamaxWbp;
	}

	public int getDayamaxWbp(){
		return dayamaxWbp;
	}

	public void setUnitap(String unitap){
		this.unitap = unitap;
	}

	public String getUnitap(){
		return unitap;
	}

	public void setDayaMax(Object dayaMax){
		this.dayaMax = dayaMax;
	}

	public Object getDayaMax(){
		return dayaMax;
	}

	public void setTglrubah(String tglrubah){
		this.tglrubah = tglrubah;
	}

	public String getTglrubah(){
		return tglrubah;
	}

	public void setKdppj(String kdppj){
		this.kdppj = kdppj;
	}

	public String getKdppj(){
		return kdppj;
	}

	public void setKdkvamaks(Object kdkvamaks){
		this.kdkvamaks = kdkvamaks;
	}

	public Object getKdkvamaks(){
		return kdkvamaks;
	}

	public void setMetKwh(String metKwh){
		this.metKwh = metKwh;
	}

	public String getMetKwh(){
		return metKwh;
	}

	public void setFaknpremium(Object faknpremium){
		this.faknpremium = faknpremium;
	}

	public Object getFaknpremium(){
		return faknpremium;
	}

	public void setDayajbst(Object dayajbst){
		this.dayajbst = dayajbst;
	}

	public Object getDayajbst(){
		return dayajbst;
	}

	public void setDaya(int daya){
		this.daya = daya;
	}

	public int getDaya(){
		return daya;
	}

	public void setFaktorpValue(int faktorpValue){
		this.faktorpValue = faktorpValue;
	}

	public int getFaktorpValue(){
		return faktorpValue;
	}

	public void setFakm(int fakm){
		this.fakm = fakm;
	}

	public int getFakm(){
		return fakm;
	}

	public void setKdam(String kdam){
		this.kdam = kdam;
	}

	public String getKdam(){
		return kdam;
	}

	public void setThblrek(String thblrek){
		this.thblrek = thblrek;
	}

	public String getThblrek(){
		return thblrek;
	}

	public void setKdmeterai(Object kdmeterai){
		this.kdmeterai = kdmeterai;
	}

	public Object getKdmeterai(){
		return kdmeterai;
	}

	public void setSlawbp(Object slawbp){
		this.slawbp = slawbp;
	}

	public Object getSlawbp(){
		return slawbp;
	}

	public void setProsenPpn(int prosenPpn){
		this.prosenPpn = prosenPpn;
	}

	public int getProsenPpn(){
		return prosenPpn;
	}

	public void setSahkvarh(double sahkvarh){
		this.sahkvarh = sahkvarh;
	}

	public double getSahkvarh(){
		return sahkvarh;
	}

	public void setProsenPpj(Object prosenPpj){
		this.prosenPpj = prosenPpj;
	}

	public Object getProsenPpj(){
		return prosenPpj;
	}

	public void setKdangsa(Object kdangsa){
		this.kdangsa = kdangsa;
	}

	public Object getKdangsa(){
		return kdangsa;
	}

	public void setBlok(int blok){
		this.blok = blok;
	}

	public int getBlok(){
		return blok;
	}

	public void setKdproses(String kdproses){
		this.kdproses = kdproses;
	}

	public String getKdproses(){
		return kdproses;
	}

	public void setJnsmutAde(Object jnsmutAde){
		this.jnsmutAde = jnsmutAde;
	}

	public Object getJnsmutAde(){
		return jnsmutAde;
	}

	public void setKddk(String kddk){
		this.kddk = kddk;
	}

	public String getKddk(){
		return kddk;
	}

	public void setJnsmut(String jnsmut){
		this.jnsmut = jnsmut;
	}

	public String getJnsmut(){
		return jnsmut;
	}

	public void setKdangsc(Object kdangsc){
		this.kdangsc = kdangsc;
	}

	public Object getKdangsc(){
		return kdangsc;
	}

	public void setKdangsb(Object kdangsb){
		this.kdangsb = kdangsb;
	}

	public Object getKdangsb(){
		return kdangsb;
	}

	public void setFlagppjangsb(Object flagppjangsb){
		this.flagppjangsb = flagppjangsb;
	}

	public Object getFlagppjangsb(){
		return flagppjangsb;
	}

	public void setFlagppjangsa(Object flagppjangsa){
		this.flagppjangsa = flagppjangsa;
	}

	public Object getFlagppjangsa(){
		return flagppjangsa;
	}

	public void setFlagppjangsc(Object flagppjangsc){
		this.flagppjangsc = flagppjangsc;
	}

	public Object getFlagppjangsc(){
		return flagppjangsc;
	}

	public void setKdprosesklp(String kdprosesklp){
		this.kdprosesklp = kdprosesklp;
	}

	public String getKdprosesklp(){
		return kdprosesklp;
	}

	public void setNama(String nama){
		this.nama = nama;
	}

	public String getNama(){
		return nama;
	}

	public void setKogol(String kogol){
		this.kogol = kogol;
	}

	public String getKogol(){
		return kogol;
	}

	public void setRpsewakap(Object rpsewakap){
		this.rpsewakap = rpsewakap;
	}

	public Object getRpsewakap(){
		return rpsewakap;
	}

	public void setKdmut(String kdmut){
		this.kdmut = kdmut;
	}

	public String getKdmut(){
		return kdmut;
	}

	public void setBatasBlok2(int batasBlok2){
		this.batasBlok2 = batasBlok2;
	}

	public int getBatasBlok2(){
		return batasBlok2;
	}

	public void setUnitupi(String unitupi){
		this.unitupi = unitupi;
	}

	public String getUnitupi(){
		return unitupi;
	}

	public void setBatasBlok1(int batasBlok1){
		this.batasBlok1 = batasBlok1;
	}

	public int getBatasBlok1(){
		return batasBlok1;
	}

	public void setLamaangsb(Object lamaangsb){
		this.lamaangsb = lamaangsb;
	}

	public Object getLamaangsb(){
		return lamaangsb;
	}

	public void setLamaangsc(Object lamaangsc){
		this.lamaangsc = lamaangsc;
	}

	public Object getLamaangsc(){
		return lamaangsc;
	}

	public void setDayabpt(Object dayabpt){
		this.dayabpt = dayabpt;
	}

	public Object getDayabpt(){
		return dayabpt;
	}

	public void setPemda(String pemda){
		this.pemda = pemda;
	}

	public String getPemda(){
		return pemda;
	}

	public void setJenisBatas(String jenisBatas){
		this.jenisBatas = jenisBatas;
	}

	public String getJenisBatas(){
		return jenisBatas;
	}

	public void setKdinvoice(Object kdinvoice){
		this.kdinvoice = kdinvoice;
	}

	public Object getKdinvoice(){
		return kdinvoice;
	}

	public void setSubkogol(Object subkogol){
		this.subkogol = subkogol;
	}

	public Object getSubkogol(){
		return subkogol;
	}

	public void setKddayabpt(Object kddayabpt){
		this.kddayabpt = kddayabpt;
	}

	public Object getKddayabpt(){
		return kddayabpt;
	}

	public void setKdpt2(Object kdpt2){
		this.kdpt2 = kdpt2;
	}

	public Object getKdpt2(){
		return kdpt2;
	}

	public void setEmin(int emin){
		this.emin = emin;
	}

	public int getEmin(){
		return emin;
	}

	public void setMaxDemand(Object maxDemand){
		this.maxDemand = maxDemand;
	}

	public Object getMaxDemand(){
		return maxDemand;
	}

	public void setBiayaPakai1(int biayaPakai1){
		this.biayaPakai1 = biayaPakai1;
	}

	public int getBiayaPakai1(){
		return biayaPakai1;
	}

	public void setBiayaPakai2(int biayaPakai2){
		this.biayaPakai2 = biayaPakai2;
	}

	public int getBiayaPakai2(){
		return biayaPakai2;
	}

	public void setIdpel(String idpel){
		this.idpel = idpel;
	}

	public String getIdpel(){
		return idpel;
	}

	public void setBiayaPakai3(int biayaPakai3){
		this.biayaPakai3 = biayaPakai3;
	}

	public int getBiayaPakai3(){
		return biayaPakai3;
	}

	public void setFlagsewakap(Object flagsewakap){
		this.flagsewakap = flagsewakap;
	}

	public Object getFlagsewakap(){
		return flagsewakap;
	}

	public void setNopel(String nopel){
		this.nopel = nopel;
	}

	public String getNopel(){
		return nopel;
	}

	public void setKdind(Object kdind){
		this.kdind = kdind;
	}

	public Object getKdind(){
		return kdind;
	}

	public void setUnitup(String unitup){
		this.unitup = unitup;
	}

	public String getUnitup(){
		return unitup;
	}

	public void setKdbpt(Object kdbpt){
		this.kdbpt = kdbpt;
	}

	public Object getKdbpt(){
		return kdbpt;
	}
}