package com.iconplus.ap2t.common.exception;


import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.ext.ExceptionMapper;

public class NotFoundExceptionMapper implements ExceptionMapper<WebApplicationException> {

    @Override
    public Response toResponse(WebApplicationException exception) {
        if (exception.getResponse().getStatus() == 404) {
            throw new ResourceNotFoundException("Resource not found");
        }
        return exception.getResponse();
    }
}

