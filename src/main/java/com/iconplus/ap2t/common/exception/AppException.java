package com.iconplus.ap2t.common.exception;

import jakarta.ws.rs.core.Response;

/**
 * <AUTHOR>
 */
public class AppException extends RuntimeException {
    private final Response.Status status;

    public AppException(String message) {
        super(message);
        this.status = Response.Status.INTERNAL_SERVER_ERROR; // Default to 500
    }

    public AppException(String message, Response.Status status) {
        super(message);
        this.status = status;
    }

    public AppException(String message, int statusCode) {
        super(message);
        this.status = Response.Status.fromStatusCode(statusCode);
    }

    public AppException(String errorMessage, Response.Status status, Throwable err) {
        super(errorMessage, err);
        this.status = status;
    }

    public AppException(String errorMessage, int statusCode, Throwable err) {
        super(errorMessage, err);
        this.status = Response.Status.fromStatusCode(statusCode);
    }

    public Response.Status getStatus() {
        return this.status;
    }

    public int getStatusCode() {
        return this.status.getStatusCode();
    }
}
