package com.iconplus.ap2t.common.param.input;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.iconplus.ap2t.common.param.EnergiParam;
import io.quarkus.runtime.annotations.RegisterForReflection;

import java.math.BigDecimal;
import java.util.List;

@RegisterForReflection
public class InputDiskon {
    @JsonProperty("id")
    public String id;
    @JsonProperty("idpel")
    public String idpel;
    @JsonProperty("thblrek")
    public String thblrek;
    @JsonProperty("kdprosesklp")
    public String kdprosesklp;
    @JsonProperty("unitupi")
    public String unitupi;
    @JsonProperty("unitap")
    public String unitap;
    @JsonProperty("unitup")
    public String unitup;
    @JsonProperty("jnsmut")
    public String jnsmut;
    @JsonProperty("kdmut")
    public String kdmut;
    @JsonProperty("thblmut")
    public String thblmut;
    @JsonProperty("tglnyala")
    public String tglnyala;
    @JsonProperty("tglrubah")
    public String tglrubah;
    @JsonProperty("kdproses")
    public String kdproses;
    @JsonProperty("nopel")
    public String nopel;
    @JsonProperty("nama")
    public String nama;
    @JsonProperty("tarif")
    public String tarif;
    @JsonProperty("kdpt")
    public String kdpt;
    @JsonProperty("kdpt_2")
    public String kdpt2;
    public BigDecimal daya;
    public String faknpremium;
    public BigDecimal dayajbst;
    public String kdbedajbst;
    public String kddk;
    public String kdbacameter;
    public String kdmeterai;
    public String lokettgk;
    public String kogol;
    public String subkogol;
    public String pemda;
    public String kdppj;
    public String kdinkaso;
    public String kdklp;
    public String kdind;
    public String kdam;
    public String kdkvamaks;
    public String maxDemand;
    public String frt;
    public String fjn;
    public String kdbpt;
    public BigDecimal dayabpt;
    public String kddayabpt;
    public String kdpembmeter;
    public BigDecimal fakm;
    public BigDecimal fakmkvarh;
    public BigDecimal fakmkvam;
    public BigDecimal rpsewatrafoDil;
    public BigDecimal rpsaldotmp;
    public String flagsewakap;
    public BigDecimal faradkap;
    public BigDecimal rpsewakap;
    public String kdangsa;
    public BigDecimal rpangsa;
    public BigDecimal lamaangsa;
    public String thblangs1a;
    public BigDecimal angskea;
    public String kdangsb;
    public BigDecimal rpangsb;
    public BigDecimal lamaangsb;
    public String thblangs1b;
    public BigDecimal angskeb;
    public String kdangsc;
    public BigDecimal rpangsc;
    public BigDecimal lamaangsc;
    public String thblangs1c;
    public BigDecimal angskec;
    public String kdinvoice;
    public String jnsmutAde;
    public Integer tahunKe;
    public Integer flagppjangsa;
    public Integer flagppjangsb;
    public Integer flagppjangsc;
    public BigDecimal sahlwbp;
    public BigDecimal sahwbp;
    public BigDecimal sahkvarh;
    public BigDecimal slalwbp;
    public BigDecimal slawbp;
    public BigDecimal slakvarh;
    public BigDecimal dayamaxWbp;
    public BigDecimal rpoperasiparalel;
    @JsonProperty("tglbacalalu")
    public String tglbacalalu;
    @JsonProperty("tglbacaakhir")
    public String tglbacaakhir;
    @JsonProperty("faktor_fjn")
    public BigDecimal faktorFjn;
    @JsonProperty("faktor_frt")
    public BigDecimal faktorFrt;
    @JsonProperty("daya_max")
    public BigDecimal dayaMax;
    @JsonProperty("prosen_ppj")
    public BigDecimal prosenPpj;
    @JsonProperty("prosen_ppn")
    public BigDecimal prosenPpn;
    @JsonProperty("jam_nyala_tanpa_meter")
    public BigDecimal jamNyalaTanpaMeter;
    @JsonProperty("faktor_fakm")
    public BigDecimal faktorFakm;
    @JsonProperty("biaya_beban")
    public BigDecimal biayaBeban;
    @JsonProperty("biaya_pakai1")
    public BigDecimal biayaPakai1;
    @JsonProperty("biaya_pakai2")
    public BigDecimal biayaPakai2;
    @JsonProperty("biaya_pakai3")
    public BigDecimal biayaPakai3;
    @JsonProperty("biaya_kvar")
    public BigDecimal biayaKvar;
    @JsonProperty("jenis_batas")
    public String jenisBatas;
    @JsonProperty("jenis_batas_emin")
    public String jenisBatasEmin;
    @JsonProperty("status_emin")
    public String statusEmin;
    @JsonProperty("emin")
    public Double emin;
    @JsonProperty("blok")
    public Integer blok;
    @JsonProperty("batas_blok1")
    public Long batasBlok1;
    @JsonProperty("batas_blok2")
    public Long batasBlok2;
    @JsonProperty("met_kwh")
    public String metKwh;
    @JsonProperty("met_kvarh")
    public String metKvarh;
    @JsonProperty("met_kvamaks")
    public String metKvamaks;
    @JsonProperty("faktork_value")
    public BigDecimal faktorKValue;
    @JsonProperty("faktorp_value")
    public BigDecimal faktorPValue;
    @JsonProperty("faktorq_value")
    public BigDecimal faktorQValue;
    @JsonProperty("faktorn_value")
    public BigDecimal faktorNValue;
    @JsonProperty("workflowName")
    public String workflowName;
    @JsonProperty("workflowVersion")
    public Integer workflowVersion;
    public BigDecimal rptarifbptrafo;
    public BigDecimal rptmp;
    public BigDecimal rpuap;
    @JsonProperty("prosentase_rec")
    public BigDecimal prosentaseRec;
    @JsonProperty("jmlunit_rec")
    public Integer jmlunitRec;
    @JsonProperty("harga_rec")
    public BigDecimal hargaRec;
    @JsonProperty("flag_type_pembelian_rec")
    public BigDecimal flagTypePembelianRec;
    public BigDecimal pemkvarh;
    public BigDecimal rpkwh;
    @JsonProperty("jamnyala_real")
    public BigDecimal jamNyalaReal;
    public BigDecimal rpkvarh;
    public BigDecimal rpptl;
    public BigDecimal rpbeban;
    @JsonProperty("kwhmin_emin_reg")
    public BigDecimal kwhEminReg;
    public BigDecimal blok3;
    public BigDecimal kelbkvarh;
    public BigDecimal kwhwbp;
    public BigDecimal kwhlwbp;
    @JsonProperty("kvarhpakai_real")
    public BigDecimal kvarhpakaiReal;
    @JsonProperty("kwhlwbp_real")
    public BigDecimal kwhlwbpReal;
    @JsonProperty("jam_nyala")
    public BigDecimal jamNyala;
    @JsonProperty("rpkvarh_param")
    public BigDecimal rpkvarh_param;
    @JsonProperty("pemkwh_real")
    public BigDecimal pemkwhReal;
    @JsonProperty("kwhwbp_real")
    public BigDecimal kwhwbpReal;
    @JsonProperty("rprekmin")
    public BigDecimal rprekmin;
    @JsonProperty("rplwbp")
    public BigDecimal rplwbp;
    @JsonProperty("kvarhlebih")
    public BigDecimal kvarhlebih;
    @JsonProperty("rplwbp_param")
    public BigDecimal rplwbpRaram;
    @JsonProperty("rpblok3")
    public BigDecimal rpblok3;
    @JsonProperty("rpwbp")
    public BigDecimal rpwbp;
    @JsonProperty("rpwbp_param")
    public BigDecimal rpwbpParam;

    @JsonProperty("data_energi")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public List<EnergiParam> dataEnergi;

}
