package com.iconplus.ap2t.common.param.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

import java.time.Instant;

@RegisterForReflection
public class UserRoleParam {

    @JsonProperty("user_id")
    public String userId;
    
    @JsonProperty("role_id")
    public String roleId;
    
    @JsonProperty("granted")
    public Boolean granted;
    
    @JsonProperty("granted_by")
    public String grantedById;
    
    @JsonProperty("granted_at")
    public Instant grantedAt;
    
    @JsonProperty("revoked_by")
    public String revokedById;
    
    @JsonProperty("revoked_at")
    public Instant revokedAt;
}
