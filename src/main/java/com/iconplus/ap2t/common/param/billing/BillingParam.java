package com.iconplus.ap2t.common.param.billing;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

public class BillingParam {
    public String idpel;
    public String nopel;
    public String nama;
    public String norek;
    public String unitup;
    public String unitupi;
    public String unitap;
    public String thblrek;
    public String frt;
    public String fjn;
    public String kdppj;
    public String pemda;
    public String tarif;
    public String kogol;
    public String kdinkaso;
    public String tgljttempo;
    public BigDecimal kwhlwbp;
    public BigDecimal kwhwbp;
    public BigDecimal rplwbp;
    public BigDecimal rpwbp;
    public BigDecimal rpblok3;
    public BigDecimal rpkvarh;
    public BigDecimal rpbeban;
    public BigDecimal rpptl;
    public BigDecimal rptb;
    public BigDecimal rpppn;
    public BigDecimal rpbpju;
    public BigDecimal rpbptrafo;
    public BigDecimal rpsewatrafo;
    public BigDecimal rpsewakap;
    public BigDecimal rpangsa;
    public BigDecimal rpangsb;
    public BigDecimal rpangsc;
    public BigDecimal rpmat;
    public BigDecimal rppln;
    public BigDecimal rptag;
    public BigDecimal rpproduksi;
    public BigDecimal rpsubsidi;
    public BigDecimal rptdllama;
    public BigDecimal rptdlbaru;
    public BigDecimal rpselisih;
    public BigDecimal rpreduksi;
    public BigDecimal rpbk1;
    public BigDecimal rpbk2;
    public BigDecimal rpbk3;
    public BigDecimal rprekBruto;
    public BigDecimal rprekNetto;
    public BigDecimal rptagBruto;
    public BigDecimal rptagNetto;
    public BigDecimal rpjbst;
    public BigDecimal rpkompensasi;
    public BigDecimal rpdiskonPremium;
    public BigDecimal rpdiskon;
    public BigDecimal rpinvoice;
    public BigDecimal rpptlplus;
    public BigDecimal rpptlminus;
    public BigDecimal rpppjptl;
    public BigDecimal rpppjangsa;
    public BigDecimal rpppjangsb;
    public BigDecimal rpppjangsc;
    public BigDecimal rpppnR3;
    public BigDecimal rpppnBptrafo;
    public BigDecimal rpppnSewatrafo;
    public BigDecimal rpppnOpspararel;
    public BigDecimal rpppnSewakap;
    public BigDecimal rpppnLain;
    public BigDecimal rptagMat;
    public BigDecimal rpppnRec;
    public BigDecimal rprec;
    public BigDecimal rpppnUap;
    public BigDecimal rpblok4;
    public BigDecimal rpopsel;
    public BigDecimal rpsewatrafoDil;
    public BigDecimal slalwbp;
    public BigDecimal sahlwbp;
    public BigDecimal blok4;
    public String kdpt;
    public String kdpt2;
    public BigDecimal daya;
    public String faknpremium;
    public BigDecimal dayar312;
    public String subkogol;
    public String kdklp;
    public String kdind;
    public String kdam;
    public String kdkvamaks;
    public String maxDemand;
    public String kdbpt;
    public BigDecimal dayabpt;
    public String kddayabpt;
    public String jnsmut;
    public String thblmut;
    public String kdmut;
    public LocalDate tglnyala;
    public LocalDate tglrubah;
    public String kdpembmeter;
    public BigDecimal fakm;
    public BigDecimal fakmkvarh;
    public BigDecimal fakmkvam;
    public String tglbacalalu;
    public String tglbacaakhir;
    public BigDecimal slawbp;
    public BigDecimal sahwbp;
    public BigDecimal slakvarh;
    public BigDecimal sahkvarh;
    public BigDecimal sahkvamaks;
    public BigDecimal dayamaks;
    public BigDecimal sahkvamaxWbp;
    public BigDecimal dayamaxWbp;
    public BigDecimal rasioDaya;
    public BigDecimal blok3;
    public BigDecimal pemkwh;
    public BigDecimal pemkvarh;
    public BigDecimal kelbkvarh;
    public BigDecimal jamnyala;
    public String kdangsa;
    public BigDecimal lamaangsa;
    public String thblangs1a;
    public BigDecimal angskea;
    public String kdangsb;
    public BigDecimal lamaangsb;
    public String thblangs1b;
    public BigDecimal angskeb;
    public String kdangsc;
    public BigDecimal lamaangsc;
    public String thblangs1c;
    public BigDecimal angskec;
    public String flagdiskon;
    public BigDecimal prosendiskon;
    public String kddiskon;
    public String jnsdiskon;
    public String kdinvoice;
    public String statusemin;
    public BigDecimal fraksibptrafo;
    public BigDecimal trfbptrafo;
    public BigDecimal trfsewakap;
    public BigDecimal dayajbst;
    public String kdbedajbst;
    public BigDecimal jumlahPadam;
    public String hitungby;
    public String msg;
    public String kdproses;
    public String kdprosesklp;
    public BigDecimal dlpd;
    public BigDecimal prosenppj;
    public BigDecimal dlpdLm;
    public String dlpdFkm;
    public String dlpdKvarh;
    public String dlpd3bln;
    public String dlpdJnsmutasi;
    public String dlpdTglbaca;
    public String alasanKoreksi;
    public String flagsewakap;
    public BigDecimal faradkap;
    public String kddk;
    public String kdbacameter;
    public String kdrekg;
    public String copyrek;
    public String kdmeterai;
    public String jnsmutAde;
    public Integer flagppjangsa;
    public Integer flagppjangsb;
    public Integer flagppjangsc;
    public List<BillingEnergiParam> billingEnergiParamList;
}