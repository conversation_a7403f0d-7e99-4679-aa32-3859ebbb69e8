package com.iconplus.ap2t.common.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

import java.math.BigDecimal;

@RegisterForReflection
public class KomporPegawaiParam {
    @JsonProperty("kwh_subsidi")
    public BigDecimal kwhSubsidi;
    @JsonProperty("kwh_nonsubsidi")
    public BigDecimal kwhNonSubsidi;
    @JsonProperty("rp_subsidi")
    public BigDecimal rpSubsidi;
    @JsonProperty("rp_nonsubsidi")
    public BigDecimal rpNonSubsidi;
}
