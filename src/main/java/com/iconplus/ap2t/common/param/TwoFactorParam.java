package com.iconplus.ap2t.common.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

@RegisterForReflection
public class TwoFactorParam {
    @JsonProperty("user_id")
    public String userId;
    @JsonProperty("code")
    public String code;
    public TwoFactorParam(String userId,String code){
        this.userId = userId;
        this.code = code;
    }
}
