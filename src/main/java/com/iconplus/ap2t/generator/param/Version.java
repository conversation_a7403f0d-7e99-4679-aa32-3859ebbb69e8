package com.iconplus.ap2t.generator.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

import java.time.LocalDateTime;

@RegisterForReflection
public class Version {
    @JsonProperty("name")
    public String name;
    @JsonProperty("version")
    public String version;
    @JsonProperty("last_update")
    public LocalDateTime lastUpdate;
    @JsonProperty("release_note")
    public String releaseNote;

}
