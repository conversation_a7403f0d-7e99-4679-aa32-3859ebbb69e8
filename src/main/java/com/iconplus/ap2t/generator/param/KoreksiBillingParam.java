package com.iconplus.ap2t.generator.param;


import com.iconplus.ap2t.generator.dto.KoreksiBillingDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @AUTHOR RR
 * @DATE 15/10/2024
 */
public class KoreksiBillingParam {
    public String userId;
    public LocalDateTime tgl_koreksi;
    public String idpel;
    public String bulanRekening;
    public List<KoreksiBillingDTO.DataBillEnergiDTO> dataBillEnergiList;

    public String getIdpel() {
        return idpel;
    }

    public void setIdpel(String idpel) {
        this.idpel = idpel;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public LocalDateTime getTgl_koreksi() {
        return tgl_koreksi;
    }

    public void setTgl_koreksi(LocalDateTime tgl_koreksi) {
        this.tgl_koreksi = tgl_koreksi;
    }

    public List<KoreksiBillingDTO.DataBillEnergiDTO> getDataBillEnergiList() {
        return dataBillEnergiList;
    }

    public void setDataBillEnergiList(List<KoreksiBillingDTO.DataBillEnergiDTO> dataBillEnergiList) {
        this.dataBillEnergiList = dataBillEnergiList;
    }

    public String getBulanRekening() {
        return bulanRekening;
    }

    public void setBulanRekening(String bulanRekening) {
        this.bulanRekening = bulanRekening;
    }


}