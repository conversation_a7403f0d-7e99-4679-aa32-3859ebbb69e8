package com.iconplus.ap2t.generator.param;


import com.iconplus.ap2t.generator.dto.KoreksiBillingPltsDTO;

import java.time.LocalDateTime;
import java.util.List;

public class KoreksiBillingPltsParam {
    public String userId;
    public LocalDateTime tgl_koreksi;
    public String idpel;
    public String bulanRekening;
    public List<KoreksiBillingPltsDTO.DataBillEnergiPltsDTO> dataBillEnergiPltsList;

    public String getIdpel() {
        return idpel;
    }

    public void setIdpel(String idpel) {
        this.idpel = idpel;
    }
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public LocalDateTime getTgl_koreksi() {
        return tgl_koreksi;
    }

    public void setTgl_koreksi(LocalDateTime tgl_koreksi) {
        this.tgl_koreksi = tgl_koreksi;
    }

    public List<KoreksiBillingPltsDTO.DataBillEnergiPltsDTO> getDataBillEnergiPltsList() {
        return dataBillEnergiPltsList;
    }

    public void setDataBillEnergiPltsList(List<KoreksiBillingPltsDTO.DataBillEnergiPltsDTO> dataBillEnergiPltsList) {
        this.dataBillEnergiPltsList = dataBillEnergiPltsList;
    }

    public String getBulanRekening() {
        return bulanRekening;
    }

    public void setBulanRekening(String bulanRekening) {
        this.bulanRekening = bulanRekening;
    }
}
