package com.iconplus.ap2t.generator.proxy;

import com.iconplus.ap2t.common.param.billing.BacaUlangParam;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

/**
 * @AUTHOR RR
 * @DATE 17/10/2024
 */
@RegisterRestClient(configKey = "acmt.proxy")
@Path("/api/acmt/secure")
public interface AcmtProxy {
    @POST
    @Path("/setBacaulang")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    Uni<Response> bacaUlangMeter(BacaUlangParam param);
}
