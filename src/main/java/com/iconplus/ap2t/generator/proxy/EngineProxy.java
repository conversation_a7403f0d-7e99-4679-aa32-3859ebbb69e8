package com.iconplus.ap2t.generator.proxy;

import com.iconplus.ap2t.common.param.InisialisasiBillingDTO;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.parameters.RequestBody;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

@RegisterRestClient(configKey = "engine.proxy")
@Path("/api/engine")
public interface EngineProxy {
    @POST
    @Path("/run-billing")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> runWorkflow(@RequestBody InisialisasiBillingDTO data);

    @POST
    @Path("/run")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> runWorkflowV2(@RequestBody InisialisasiBillingDTO data);
    @POST
    @Path("/run-queue")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> runWorkQueue(@RequestBody InisialisasiBillingDTO data);
}
