package com.iconplus.ap2t.generator.proxy;

import com.iconplus.ap2t.common.param.billing.ClosingMutasiParam;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.rest.client.annotation.ClientHeaderParam;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

/**
 * <AUTHOR>
 * Date: 20/06/2025
 * Time: 11:18
 */
@RegisterRestClient(configKey = "ap2t_old.proxy")
@Path("/dil")
@ClientHeaderParam(name = "x-api-key", value = "${ap2t-old-proxy.api-key}")
public interface Ap2tOldProxy {

    @POST
    @Path("/proses-suplai-dil")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    Uni<Response> closingMutasi(ClosingMutasiParam param);
}
