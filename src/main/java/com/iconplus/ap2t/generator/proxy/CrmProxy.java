package com.iconplus.ap2t.generator.proxy;

import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

@RegisterRestClient(configKey = "crm.proxy")
@Path("/dil")
public interface CrmProxy {

    @GET
    @Path("/suplai-dil-idpel")
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> getSuplaiDilByIdpel(@QueryParam("idpel") String idpel, @QueryParam("thblrek") String thblrek);
    @GET
    @Path("/stand-meter-by-idpel")
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Response> getStandMeterByIdpel(@QueryParam("idpel") String idpel);
}
