package com.iconplus.ap2t.generator.mapper;


import com.iconplus.ap2t.common.response.billing.DataBillJamNyalaDto;
import com.iconplus.ap2t.data.entity.billing.BillLogFlagDpp;
import com.iconplus.ap2t.data.entity.billing.BillLogProses;
import com.iconplus.ap2t.data.entity.master.MasterKodeProses;

import java.time.LocalDateTime;

public class BillLogMapper {

    public static BillLogProses mapToEntityStart(BillLogProses logProses, MasterKodeProses tabProsesUp, String batch, String userId) {
        logProses.thblrek = tabProsesUp.thblrek;
        logProses.kdProsesKlp = tabProsesUp.kdProsesKlp;
        logProses.unitap = tabProsesUp.unitap;
        logProses.unitup = tabProsesUp.unitup;
        logProses.batch = batch;
        logProses.proses = "SAH";
        logProses.startProses = LocalDateTime.now();
        logProses.byProses = userId;

        return logProses;
    }

    public static BillLogProses mapToEntityFinish(BillLogProses logProses, String message, int totalData, int recordSuccess, int recordFailed) {
        logProses.endProses = LocalDateTime.now();
        logProses.jmlRecProses = totalData;
        logProses.jmlRecGagal = recordFailed;
        logProses.jmlRecBerhasil = recordSuccess;
        logProses.message = message;

        return logProses;
    }

    public static BillLogProses mapToEntitySetting(
        BillLogProses logProses, String thblrek, String kdProsesKlp, String unitap,
        String unitup, String batch, String userId, int processedData
    ) {
        logProses.thblrek = thblrek;
        logProses.kdProsesKlp = kdProsesKlp;
        logProses.unitap = unitap;
        logProses.unitup = unitup;
        logProses.batch = batch;
        logProses.proses = "SETTING_TAB_PROSES";
        logProses.startProses = LocalDateTime.now();
        logProses.byProses = userId;
        logProses.jmlRecGagal = 0;
        logProses.jmlRecProses =  processedData;
        logProses.jmlRecBerhasil = processedData;
        logProses.message = "BERHASIL";

        return logProses;
    }

    public static BillLogFlagDpp mapToEntity(
        BillLogFlagDpp billLog, String thblrek, String unitap, String unitup,
        String kdProsesKlp, String alasan, String userId, DataBillJamNyalaDto jamNyala
    ) {
        billLog.thblrek=thblrek;
        billLog.unitupi=unitup.substring(0, 2);
        billLog.unitap=unitap;
        billLog.unitup=unitup;
        billLog.kdProsesKlp=kdProsesKlp;
        billLog.tglLog=LocalDateTime.now();
        billLog.alasan=alasan;
        billLog.petugasCatat=userId;
        billLog.tglCatat=LocalDateTime.now();
        billLog.lbrTotal=jamNyala.getLbrTotal().intValue();
        billLog.lbrJnMax=jamNyala.getLbrJnMax().intValue();
        billLog.levelSah=null;
        billLog.lbrJn2000=jamNyala.getLbrJn2000().intValue();
        billLog.lbrJn720=jamNyala.getLbrJn720().intValue();
        billLog.lbrKvarhLebih=jamNyala.getLbrKvarhLebih().intValue();
        billLog.lbrKvarhLebih2=jamNyala.getLbrKvarhLebih2().intValue();
        billLog.lbrTgl1=jamNyala.getLbrTgl1().intValue();
        billLog.lbrStSama=jamNyala.getLbrStSama().intValue();
        return billLog;
    }

    public static BillLogProses mapEntityBillLogProsesSemua(BillLogProses logProses, String thblrek, String unitap, String userId, String batch) {
        logProses.thblrek = thblrek;
        logProses.kdProsesKlp = "SEMUA";
        logProses.unitap = unitap;
        logProses.unitup = "SEMUA";
        logProses.batch = batch;
        logProses.proses = "SUPLAI_TAB_PROSES";
        logProses.startProses = LocalDateTime.now();
        logProses.byProses = userId;

        return logProses;
    }

    public static BillLogProses mapToRekapEntity(BillLogProses logProses, MasterKodeProses tabProsesUp, String batch, String userId) {
        logProses.thblrek = tabProsesUp.thblrek;
        logProses.kdProsesKlp = tabProsesUp.kdProsesKlp;
        logProses.unitap = tabProsesUp.unitap;
        logProses.unitup = tabProsesUp.unitup;
        logProses.batch = batch;
        logProses.proses = "REKAP";
        logProses.startProses = LocalDateTime.now();
        logProses.byProses = userId;

        return logProses;
    }
}
