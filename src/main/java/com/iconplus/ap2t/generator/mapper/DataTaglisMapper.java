package com.iconplus.ap2t.generator.mapper;


import com.iconplus.ap2t.data.entity.billing.Billing;
import com.iconplus.ap2t.generator.dto.DataTaglisDto;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

public class DataTaglisMapper {

    private static DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    public static DataTaglisDto mapToDto(Billing billing) {
        DataTaglisDto dto = new DataTaglisDto();

        dto.idpel = billing.idpel;
        dto.thblrek = billing.thblrek;
        dto.unitupi = billing.unitupi;
        dto.unitap = billing.unitap;
        dto.unitup = billing.unitup;
        dto.tarif = billing.tarif;
        dto.kdpt = billing.kdpt;
        dto.kdpt2 = billing.kdpt_2;
        dto.daya = BigDecimal.valueOf(billing.daya);
        dto.faknpremium = billing.faknpremium;
        dto.dayar312 = billing.dayar312;
        dto.kogol = billing.kogol;
        dto.subkogol = billing.subkogol;
        dto.pemda = billing.pemda;
        dto.kdppj = billing.kdppj;
        dto.kdinkaso = billing.kdinkaso;
        dto.kdklp = billing.kdklp;
        dto.kdind = billing.kdind;
        dto.kdam = billing.kdam;
        dto.kdkvamaks = billing.kdkvamaks;
        dto.maxDemand = billing.maxDemand;
        dto.frt = billing.frt;
        dto.fjn = billing.fjn;
        dto.kdbpt = billing.kdbpt;
        dto.dayabpt = billing.dayabpt;
        dto.kddayabpt = billing.kddayabpt;
        dto.jnsmut = billing.jnsmut;
        dto.thblmut = billing.thblmut;
        dto.kdmut = billing.kdmut;
        dto.tglnyala = billing.tglnyala.format(formatter1);
        dto.tglrubah = billing.tglrubah.format(formatter1);
        dto.kdpembmeter = billing.kdpembmeter;
        dto.fakm = billing.fakm;
        dto.fakmkvarh = billing.fakmkvarh;
        dto.fakmkvam = billing.fakmkvam;
        dto.tglbacalalu = billing.tglbacalalu;
        dto.tglbacaakhir = billing.tglbacaakhir;
        dto.slalwbp = billing.slalwbp;
        dto.sahlwbp = billing.sahlwbp;
        dto.slawbp = billing.slawbp;
        dto.sahwbp = billing.sahwbp;
        dto.slakvarh = billing.slakvarh;
        dto.sahkvarh = billing.sahkvarh;
        dto.sahkvamaks = billing.sahkvamaks;
        dto.dayamaks = billing.dayamaks;
        dto.sahkvamaxWbp = billing.sahkvamaxWbp;
        dto.dayamaxWbp = billing.dayamaxWbp;
        dto.rasioDaya = billing.rasioDaya;
        dto.kwhlwbp = billing.kwhlwbp;
        dto.kwhwbp = billing.kwhwbp;
        dto.blok3 = billing.blok3;
        dto.pemkwh = billing.pemkwh;
        dto.pemkvarh = billing.pemkvarh;
        dto.kelbkvarh = billing.kelbkvarh;
        dto.jamnyala = billing.jamnyala;
        dto.rpwbp = billing.rpwbp;
        dto.rpkvarh = billing.rpkvarh;
        dto.rptb = billing.rptb;
        dto.kdangsa = billing.kdangsa;
        dto.lamaangsa = billing.lamaangsa;
        dto.thblangs1a = billing.thblangs1a;
        dto.angskea = billing.angskea;
        dto.kdangsb = billing.kdangsb;
        dto.lamaangsb = billing.lamaangsb;
        dto.thblangs1b = billing.thblangs1b;
        dto.angskeb = billing.angskeb;
        dto.kdangsc = billing.kdangsc;
        dto.lamaangsc = billing.lamaangsc;
        dto.thblangs1c = billing.thblangs1c;
        dto.angskec = billing.angskec;
        dto.rppln = billing.rppln;
        dto.rpproduksi = billing.rpproduksi;
        dto.rpsubsidi = billing.rpsubsidi;
        dto.rptdllama = billing.rptdllama;
        dto.rptdlbaru = billing.rptdlbaru;
        dto.rpselisih = billing.rpselisih;
        dto.rpreduksi = billing.rpreduksi;
        dto.rpbk1 = billing.rpbk1;
        dto.rpbk2 = billing.rpbk2;
        dto.rpbk3 = billing.rpbk3;
        dto.norek = billing.norek;
        dto.rprekBruto = billing.rprekBruto;
        dto.rprekNetto = billing.rprekNetto;
        dto.rptagBruto = billing.rptagBruto;
        dto.rptagNetto = billing.rptagNetto;
        dto.rpjbst = billing.rpjbst;
        dto.rpkompensasi = billing.rpkompensasi;
        dto.rpdiskonPremium = billing.rpdiskonPremium;
        dto.rpdiskon = billing.rpdiskon;
        dto.flagdiskon = billing.flagdiskon;
        dto.prosendiskon = billing.prosendiskon;
        dto.kddiskon = billing.kddiskon;
        dto.jnsdiskon = billing.jnsdiskon;
        dto.rpinvoice = billing.rpinvoice;
        dto.kdinvoice = billing.kdinvoice;
        dto.statusemin = billing.statusemin;
        dto.fraksibptrafo = billing.fraksibptrafo;
        dto.trfbptrafo = billing.trfbptrafo;
        dto.trfsewakap = billing.trfsewakap;
        dto.slalwbpPasang = billing.slalwbpPasang;
        dto.sahlwbpCabut = billing.sahlwbpCabut;
        dto.slawbpPasang = billing.slawbpPasang;
        dto.sahwbpCabut = billing.sahwbpCabut;
        dto.slakvarhPasang = billing.slakvarhPasang;
        dto.sahkvarhCabut = billing.sahkvarhCabut;
        dto.sahkvamaksCabut = billing.sahkvamaksCabut;
        dto.dayamaksCabut = billing.dayamaksCabut;
        dto.dayajbst = billing.dayajbst;
        dto.kdbedajbst = billing.kdbedajbst;
        dto.jumlahPadam = billing.jumlahPadam;
        dto.tglinisialisasi = billing.tglinisialisasi != null ? billing.tglinisialisasi.format(formatter1) : null;
        dto.tglcatat = billing.tglcatat != null ? billing.tglcatat.format(formatter1) : null;
        dto.tglhitung = billing.tglhitung != null ? billing.tglhitung.format(formatter1) : null;
        dto.tglsah = billing.tglsah != null ? billing.tglsah.format(formatter1) : null;
        dto.initby = billing.initby;
        dto.catatby = billing.catatby;
        dto.hitungby = billing.hitungby;
        dto.sahby = billing.sahby;
        dto.pecahan = billing.pecahan;
        dto.msg = billing.msg;
        dto.batch = billing.batch;
        dto.postingbilling = billing.postingbilling;
        dto.kdproses = billing.kdproses;
        dto.kdprosesklp = billing.kdprosesklp;
        dto.tgljttempo = billing.tgljttempo;
        dto.tglbayarAwal = billing.tglbayarAwal;
        dto.tglbayarAkhir = billing.tglbayarAkhir;
        dto.dlpd = billing.dlpd;
        dto.tglDpp = billing.tglDpp != null ? billing.tglDpp.format(formatter1) : null;
        dto.rpptlplus = billing.rpptlplus;
        dto.rpptlminus = billing.rpptlminus;
        dto.prosenppj = billing.prosenppj;
        dto.tglKargo = billing.tglKargo != null ? billing.tglKargo.format(formatter1) : null;
        dto.slalwbp1 = billing.slalwbp1;
        dto.sahlwbp1 = billing.sahlwbp1;
        dto.slalwbp2 = billing.slalwbp2;
        dto.sahlwbp2 = billing.sahlwbp2;
        dto.wbprataIns = billing.wbprataIns;
        dto.lwbp1rataIns = billing.lwbp1rataIns;
        dto.tipeskema = billing.tipeskema;
        dto.skema4 = billing.skema4;
        dto.sahlwbp1Cabut = billing.sahlwbp1Cabut;
        dto.slalwbp1Pasang = billing.slalwbp1Pasang;
        dto.sahlwbp2Cabut = billing.sahlwbp2Cabut;
        dto.slalwbp2Pasang = billing.slalwbp2Pasang;
        dto.kwhWbpPadamHari = billing.kwhWbpPadamHari;
        dto.jmlpadam = billing.jmlpadam;
        dto.kdgerak = billing.kdgerak;
        dto.rpppjptl = billing.rpppjptl;
        dto.rpppjangsa = billing.rpppjangsa;
        dto.rpppjangsb = billing.rpppjangsb;
        dto.rpppjangsc = billing.rpppjangsc;
        dto.tarifLmPindahTrf = billing.tarifLmPindahTrf;
        dto.dayaLmPindahTrf = billing.dayaLmPindahTrf;
        dto.kdptLamaPindahTrf = billing.kdptLamaPindahTrf;
        dto.dlpdLm = billing.dlpdLm;
        dto.dlpdFkm = billing.dlpdFkm;
        dto.dlpdKvarh = billing.dlpdKvarh;
        dto.dlpd3bln = billing.dlpd3bln;
        dto.dlpdJnsmutasi = billing.dlpdJnsmutasi;
        dto.dlpdTglbaca = billing.dlpdTglbaca;
        dto.alasanKoreksi = billing.alasanKoreksi;
        dto.pemkwhLwbp1 = billing.pemkwhLwbp1;
        dto.pemkwhLwbp2 = billing.pemkwhLwbp2;
        dto.rpppnR3 = billing.rpppnR3;
        dto.rpppnBptrafo = billing.rpppnBptrafo;
        dto.rpppnSewatrafo = billing.rpppnSewatrafo;
        dto.rpppnOpspararel = billing.rpppnOpspararel;
        dto.rpppnSewakap = billing.rpppnSewakap;
        dto.rpppnLain = billing.rpppnLain;
        dto.rptagMat = billing.rptagMat;
        dto.rpppnRec = billing.rpppnRec;
        dto.rprec = billing.rprec;
        dto.rpppnUap = billing.rpppnUap;
        dto.kwhKompor = billing.kwhKompor;
        dto.rpKompor = billing.rpKompor;
        dto.blok4 = billing.blok4;
        dto.rpblok4 = billing.rpblok4;
        dto.rpopsel = billing.rpopsel;
        dto.sahlwbpExport = billing.sahlwbpExport;
        dto.sahwbpExport = billing.sahwbpExport;
        dto.sahkvarhExport = billing.sahkvarhExport;
        dto.slalwbpPasangExp = billing.slalwbpPasangExp;
        dto.sahlwbpCabutExp = billing.sahlwbpCabutExp;
        dto.slawbpPasangExp = billing.slawbpPasangExp;
        dto.sahwbpCabutExp = billing.sahwbpCabutExp;
        dto.slakvarhPasangExp = billing.slakvarhPasangExp;
        dto.sahkvarhCabutExp = billing.sahkvarhCabutExp;
        dto.eminRmPltsBaru = billing.eminRmPltsBaru;
        dto.eminRmPltsLama = billing.eminRmPltsLama;
        dto.rpsewatrafoDil = billing.rpsewatrafoDil;
        dto.flagsewakap = billing.flagsewakap;
        dto.faradkap = billing.faradkap;
        dto.nama = billing.nama;
        dto.alamat = billing.alamat;
        dto.nopel = billing.nopel;
        dto.kddk = billing.kddk;
        dto.kdbacameter = billing.kdbacameter;
        dto.kdrekg = billing.kdrekg;
        dto.copyrek = billing.copyrek;
        dto.kdmeterai = billing.kdmeterai;
        dto.lokettgk = billing.lokettgk;
        dto.tahunKe = billing.tahunKe;
        dto.jnsmutAde = billing.jnsmutAde;
        dto.flagppjangsa = billing.flagppjangsa;
        dto.flagppjangsb = billing.flagppjangsb;
        dto.flagppjangsc = billing.flagppjangsc;

        dto.uraianNilaiRupiah = Arrays.asList(
            new DataTaglisDto.NilaiRupiahDto("RPMAT", billing.rpmat),
            new DataTaglisDto.NilaiRupiahDto("RPTAG", billing.rptag),
            new DataTaglisDto.NilaiRupiahDto("RPBPJU", billing.rpbpju),
            new DataTaglisDto.NilaiRupiahDto("RPBPTRAFO", billing.rpbptrafo),
            new DataTaglisDto.NilaiRupiahDto("RPSEWATRAFO", billing.rpsewatrafo),
            new DataTaglisDto.NilaiRupiahDto("RPLWBP", billing.rplwbp),
            new DataTaglisDto.NilaiRupiahDto("RPBLOK3", billing.rpblok3),
            new DataTaglisDto.NilaiRupiahDto("RPBEBAN", billing.rpbeban),
            new DataTaglisDto.NilaiRupiahDto("RPPTL", billing.rpptl),
            new DataTaglisDto.NilaiRupiahDto("RPPPN", billing.rpppn),
            new DataTaglisDto.NilaiRupiahDto("RPSEWAKAP", billing.rpsewakap),
            new DataTaglisDto.NilaiRupiahDto("RPANGSA", billing.rpangsa),
            new DataTaglisDto.NilaiRupiahDto("RPANGSB", billing.rpangsb),
            new DataTaglisDto.NilaiRupiahDto("RPANGSC", billing.rpangsc)
        );

        //    public BigDecimal rpmat;
        //    public BigDecimal rptag;
        //    public BigDecimal rpbpju;
        //    public BigDecimal rpbptrafo;
        //    public BigDecimal rpsewatrafo;
        //    public BigDecimal rplwbp;
        //    public BigDecimal rpblok3;
        //    public BigDecimal rpbeban;
        //    public BigDecimal rpptl;
        //    public BigDecimal rpppn;
        //    public BigDecimal rpsewakap;
        //    public BigDecimal rpangsa;
        //    public BigDecimal rpangsb;
        //    public BigDecimal rpangsc;

        return dto;
    }
}
