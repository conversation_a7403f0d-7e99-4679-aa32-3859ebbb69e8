package com.iconplus.ap2t.generator.mapper;


import com.iconplus.ap2t.common.param.billing.BillDppPpnParam;
import com.iconplus.ap2t.common.param.billing.Output;
import com.iconplus.ap2t.data.entity.billing.BillDppPpn;

public class BillDppPpnMapper {

    public static BillDppPpn mapToEntity(BillDppPpn billDppPpn, Output output, BillDppPpnParam request) {
        if (billDppPpn.id == null) {
            billDppPpn.id = request.idpel + request.thblrek;
        }
        billDppPpn.idpel = request.idpel;
        billDppPpn.thblrek = request.thblrek;
        billDppPpn.unitupi = request.unitupi;
        billDppPpn.unitap = request.unitap;
        billDppPpn.unitup = request.unitup;
        billDppPpn.dppPtl = output.getDppPtl();
        billDppPpn.dppSewakap = output.getDppSewakap();
        billDppPpn.dppBptrafo = output.getDppBptrafo();
        billDppPpn.dppSewatrafo = output.getDppSewatrafo();
        billDppPpn.dpp_opsel = output.getDppOpsel();
        billDppPpn.dppRec = output.getDppRec();
        billDppPpn.dppUap = output.getDppUap();
        billDppPpn.dppPpn = output.getDppPpn();
        billDppPpn.valueA = output.getValueA();
        billDppPpn.valueB = output.getValueB();
        billDppPpn.prosentase = output.getProsentase();

        return billDppPpn;
    }
}
