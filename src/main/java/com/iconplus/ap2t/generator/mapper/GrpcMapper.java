package com.iconplus.ap2t.generator.mapper;


import com.iconpln.ap2t.grpc.service.EnergiParam;
import com.iconpln.ap2t.grpc.service.InitResult;
import com.iconplus.ap2t.common.param.DataEnergi;
import com.iconplus.ap2t.common.param.InisialisasiBillingDTO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class GrpcMapper {
    public static InisialisasiBillingDTO toInisialisasi(InitResult item){
        InisialisasiBillingDTO initial = new InisialisasiBillingDTO();
        initial.id = item.getId();
        initial.idpel = item.getIdpel();
        initial.thblrek = item.getThblrek();
        initial.kdprosesklp = item.getKdprosesklp();
        initial.unitupi = item.getUnitupi();
        initial.unitap = item.getUnitap();
        initial.unitup = item.getUnitup();
        initial.jnsmut = item.getJnsmut();
        initial.kdmut = item.getKdmut();
        initial.thblmut = item.getThblmut();
        initial.tglnyala = item.getTglnyala();
        initial.tglrubah = item.getTglrubah();
        initial.kdproses = item.getKdproses();
        initial.nopel = item.getNopel();
        initial.nama = item.getNama();
        initial.tarif = item.getTarif();
        initial.kdpt = item.getKdpt();
        initial.kdpt2 = item.getKdpt2();
        initial.daya = (long)item.getDaya();
        initial.faknpremium = item.getFaknpremium();
        initial.dayajbst = BigDecimal.valueOf(item.getDayajbst());
        initial.kdbedajbst = item.getKdbedajbst();
        initial.kddk = item.getKddk();
        initial.kdbacameter = item.getKdbacameter();
        initial.kdmeterai = item.getKdmeterai();
        initial.lokettgk = item.getLokettgk();
        initial.kogol = item.getKogol();
        initial.subkogol = item.getSubkogol();
        initial.pemda = item.getPemda();
        initial.kdppj = item.getKdppj();
        initial.kdinkaso = item.getKdinkaso();
        initial.kdklp = item.getKdklp();
        initial.kdind = item.getKdind();
        initial.kdam = item.getKdam();
        initial.kdkvamaks = item.getKdkvamaks();
        initial.maxDemand = item.getMaxDemand();
        initial.frt = item.getFrt();
        initial.fjn = item.getFjn();
        initial.kdbpt = item.getKdbpt();
        initial.dayabpt = BigDecimal.valueOf(item.getDaya());
        initial.kddayabpt = item.getKddayabpt();
        initial.kdpembmeter = item.getKdpembmeter();
        initial.fakm = BigDecimal.valueOf(item.getFakm());
        initial.fakmkvarh = BigDecimal.valueOf(item.getFakmkvarh());
        initial.fakmkvam = BigDecimal.valueOf(item.getFakmkvam());
        initial.rpsewatrafoDil = BigDecimal.valueOf(item.getRpsewatrafoDil());
        initial.flagsewakap = item.getFlagsewakap();
        initial.faradkap = BigDecimal.valueOf(item.getFaradkap());
        initial.rpsewakap = BigDecimal.valueOf(item.getRpsewakap());
        initial.kdangsa = item.getKdangsa();
        initial.rpangsa = BigDecimal.valueOf(item.getRpangsa());
        initial.lamaangsa = BigDecimal.valueOf(item.getLamaangsa());
        initial.thblangs1a = item.getThblangs1A();
        initial.angskea = BigDecimal.valueOf(item.getAngskea());
        initial.kdangsb = item.getKdangsb();
        initial.rpangsb = BigDecimal.valueOf(item.getRpangsb());
        initial.lamaangsb = BigDecimal.valueOf(item.getLamaangsb());
        initial.thblangs1b = item.getThblangs1B();
        initial.angskeb = BigDecimal.valueOf(item.getAngskeb());
        initial.kdangsc = item.getKdangsc();
        initial.rpangsc = BigDecimal.valueOf(item.getRpangsc());
        initial.lamaangsc = BigDecimal.valueOf(item.getLamaangsc());
        initial.thblangs1c = item.getThblangs1C();
        initial.angskec = BigDecimal.valueOf(item.getAngskec());
        initial.kdinvoice = item.getKdinvoice();
        initial.jnsmutAde = item.getJnsmutAde();
        initial.tahunKe = item.getTahunKe();
        initial.flagppjangsa = item.getFlagppjangsa();
        initial.flagppjangsb = item.getFlagppjangsb();
        initial.flagppjangsc = item.getFlagppjangsc();
        initial.sahlwbp = BigDecimal.valueOf(item.getSahlwbp());
        initial.sahwbp = BigDecimal.valueOf(item.getSahwbp());
        initial.sahkvarh = BigDecimal.valueOf(item.getSahkvarh());
        initial.slalwbp = BigDecimal.valueOf(item.getSlalwbp());
        initial.slawbp = BigDecimal.valueOf(item.getSlawbp());
        initial.slakvarh = BigDecimal.valueOf(item.getSlakvarh());
        initial.dayamaxWbp = BigDecimal.valueOf(item.getDayamaxWbp());
        initial.tglbacalalu=item.getTglbacalalu();
        initial.tglbacaakhir=item.getTglbacaakhir();
        initial.faktorFjn = BigDecimal.valueOf(item.getFaktorFjn());
        initial.faktorFrt = BigDecimal.valueOf(item.getFaktorFrt());
        initial.faktorFakm = BigDecimal.valueOf(item.getFaktorFakm());
        initial.dayaMax = BigDecimal.valueOf(item.getDayaMax());
        initial.jamNyalaTanpaMeter=BigDecimal.valueOf(item.getJamNyalaTanpaMeter());

        initial.blok = item.getBlok();
        int batasBlok1 = item.getBatasBlok1();
        initial.batasBlok1 = (long) batasBlok1;
        initial.batasBlok2 = (long) item.getBatasBlok2();
        initial.biayaBeban = BigDecimal.valueOf(item.getBiayaBeban());
        initial.biayaPakai1 = BigDecimal.valueOf(item.getBiayaPakai1());
        initial.biayaPakai2 = BigDecimal.valueOf(item.getBiayaPakai2());
        initial.biayaPakai3 = BigDecimal.valueOf(item.getBiayaPakai3());
        initial.biayaKvar = BigDecimal.valueOf(item.getBiayaKvar());
        initial.prosenPpj = BigDecimal.valueOf(item.getProsenPpj());
        initial.prosenPpn = BigDecimal.valueOf(item.getProsenPpj());
        initial.jenisBatas = item.getJenisBatas();
        initial.emin = item.getEmin();
        initial.statusEmin = item.getStatusEmin();
        initial.jenisBatasEmin = item.getJenisBatasEmin();
        initial.metKwh = item.getMetKwh();
        initial.metKvarh = item.getMetKvarh();
        initial.metKvamaks = item.getMetKvamaks();
        initial.faktorKValue = BigDecimal.valueOf(item.getFaktorKValue());
        initial.faktorNValue = BigDecimal.valueOf(item.getFaktorNValue());
        initial.faktorPValue = BigDecimal.valueOf(item.getFaktorPValue());
        initial.workflowName = item.getWorkflowName();

        initial.workflowVersion = item.getWorkflowVersion();
        if(item.getDataEnergiList().size()>0) {
            List<DataEnergi> params = new ArrayList<>();
            for(EnergiParam param:item.getDataEnergiList()){
                DataEnergi e = new DataEnergi();
                e.batasBlok1 = (long)param.getBatasBlok1();
                e.batasBlok2 = (long)param.getBatasBlok2();
                e.biayaBeban = BigDecimal.valueOf(param.getBiayaBeban());
                e.biayaPakai1 = BigDecimal.valueOf(param.getBiayaPakai1());
                e.biayaPakai2 = BigDecimal.valueOf(param.getBiayaPakai2());
                e.biayaPakai3 = BigDecimal.valueOf(param.getBiayaPakai3());
                e.biayaKvar = BigDecimal.valueOf(param.getBiayaKvar());
                e.blok = param.getBlok();
                e.daya = param.getDaya();
                e.dayaMax = param.getDayaMax();
                e.dayamaxWbp = BigDecimal.valueOf(param.getDayamaxWbp());
                e.emin = param.getEmin();
                e.fakm = BigDecimal.valueOf(param.getFakm());
                e.faktorFakm = BigDecimal.valueOf(param.getFaktorFakm());
                e.faktorFjn = param.getFaktorFjn();
                e.faktorFrt = param.getFaktorFrt();
                e.faktorFakm = BigDecimal.valueOf(param.getFaktorFakm());
                e.faktorKValue = BigDecimal.valueOf(param.getFaktorKValue());
                e.faktorQValue = BigDecimal.valueOf(param.getFaktorQValue());
                e.faktorNValue = BigDecimal.valueOf(param.getFaktorNValue());
                e.faktorPValue = BigDecimal.valueOf(param.getFaktorPValue());
                e.frt = param.getFrt();
                e.fjn = param.getFjn();
                e.fraksiDmp = param.getFraksiDmp();
                e.fraksiEmin = param.getFraksiEmin();
                e.fraksiHemat = param.getFraksiHemat();
                e.fraksiKwh = param.getFraksiKwh();
                e.id = param.getId();
                e.idpel = param.getIdpel();
                e.thblmut = param.getThblmut();
                e.thblrek = param.getThblrek();
                e.capacityChargePltsLama = BigDecimal.valueOf(param.getCapacityChargePltsLama());
                e.flagDiskonStimulus = param.getFlagDiskonStimulus();
                e.prosentaseDiskonStimulus = BigDecimal.valueOf(param.getProsentaseDiskonStimulus());
                e.slakvarh = BigDecimal.valueOf(param.getSlakvarh());
                e.slalwbp = BigDecimal.valueOf(param.getSlalwbp());
                e.sahkvarh = BigDecimal.valueOf(param.getSahkvarh());
                e.sahlwbp = BigDecimal.valueOf(param.getSahlwbp());
                e.sahwbp = BigDecimal.valueOf(param.getSahwbp());
                e.slawbp = BigDecimal.valueOf(param.getSlawbpExport());
                params.add(e);
            }
//                initial.setDataEnergi(item.getDataEnergiList());
        }

        return initial;
    }
}
