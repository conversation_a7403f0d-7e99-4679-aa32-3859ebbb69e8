package com.iconplus.ap2t.generator.mapper;

import cn.hutool.core.bean.BeanUtil;

import com.iconplus.ap2t.common.exception.AppException;
import com.iconplus.ap2t.common.param.billing.BillingParam;
import com.iconplus.ap2t.common.param.billing.Output;
import com.iconplus.ap2t.common.param.billing.VerifikasiDlpdParam;
import com.iconplus.ap2t.data.entity.billing.Billing;
import com.iconplus.ap2t.data.entity.billing.Billing720JN;
import com.iconplus.ap2t.data.entity.billing.Dpm;
import com.iconplus.ap2t.data.entity.log.LogBilling;
import com.iconplus.ap2t.generator.dto.BillingDTO;
import com.iconplus.ap2t.generator.dto.KoreksiBillingDTO;
import com.iconplus.ap2t.generator.dto.KoreksiBillingPltsDTO;
import com.iconplus.ap2t.generator.response.SuplaiDILDTO;
import io.smallrye.mutiny.Uni;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class BillingMapper {

    private static DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    public static Billing mapToBilling(Billing bill, SuplaiDILDTO dil, Dpm dpm, boolean newEntity) {
        if (newEntity) {
            bill.id = dpm.idpel + dpm.thblrek;
        }
        bill.idpel = dpm.idpel;
        bill.thblrek = dpm.thblrek;
        bill.unitupi = dil.unitupi;
        bill.unitap = dil.unitap;
        bill.unitup = dil.unitup;
        bill.kdprosesklp = dil.kdprosesklp;
        bill.jnsmut = dil.jnsmut;
        bill.jnsmutAde = dil.jnsmutAde;
        bill.thblmut = dil.thblmut;
        bill.kdmut = dil.kdmut;
        if (dil.tglnyala != null) {
            bill.tglnyala = LocalDate.parse(dil.tglnyala);
        }
        if (dil.tglrubah != null) {
            bill.tglrubah = LocalDate.parse(dil.tglrubah);
        }
        bill.kdproses = dil.kdproses;
        bill.nopel = dil.nopel;
        bill.nama = dil.nama;
        bill.tarif = dil.tarif;
        bill.kdpt = dil.kdpt;
        bill.kdpt_2 = dil.kdpt2;
        bill.daya = dil.daya.longValue();
        bill.faknpremium = dil.faknpremium;
        bill.dayajbst = dil.dayajbst;
        bill.kdbedajbst = dil.kdbedajbst;
        bill.kddk = dil.kddk;
        bill.kdbacameter = dil.kdbacameter;
        bill.kdrekg = dil.kdrekG;
        bill.copyrek = dil.copyrek;
        bill.kdmeterai = dil.kdmeterai;
        bill.lokettgk = dil.lokettgk;
        bill.kogol = dil.kogol;
        bill.subkogol = dil.subkogol;
        bill.pemda = dil.pemda;
        bill.kdppj = dil.kdppj;
        bill.kdinkaso = dil.kdinkaso;
        bill.kdklp = dil.kdklp;
        bill.kdind = dil.kdind;
        bill.kdam = dil.kdam;
        bill.kdkvamaks = dil.kdkvamaks;
        bill.maxDemand = dil.maxDemand;
        bill.frt = dil.frt;
        bill.fjn = dil.fjn;
        bill.kdbpt = dil.kdbpt;
        bill.dayabpt = dil.dayabpt;
        bill.kddayabpt = dil.kddayabpt;
        bill.kdpembmeter = dil.kdpembmeter;
        bill.fakm = dil.fakm;
        bill.fakmkvarh = dil.fakmkvarh;
        bill.fakmkvam = dil.fakmkvam;
        bill.rpsewatrafo = dil.rpsewatrafo;
        //bill.kdsewakap = dil.kdsewakap;
        bill.flagsewakap = dil.flagsewakap;
        bill.faradkap = dil.faradkap;
        bill.rpsewakap = dil.rpsewakap;
        bill.kdangsa = dil.kdangsa;
        bill.rpangsa = dil.rpangsa;
        bill.lamaangsa = dil.lamaangsa;
        bill.thblangs1a = dil.thblangs1a;
        bill.angskea = dil.angskea;
        bill.kdangsb = dil.kdangsb;
        bill.rpangsb = dil.rpangsb;
        bill.lamaangsb = dil.lamaangsb;
        bill.thblangs1b = dil.thblangs1b;
        bill.angskeb = dil.angskeb;
        bill.kdangsc = dil.kdangsc;
        bill.rpangsc = dil.rpangsc;
        bill.lamaangsc = dil.lamaangsc;
        bill.thblangs1c = dil.thblangs1c;
        bill.angskec = dil.angskec;
        bill.tarifLmPindahTrf = dil.tarifLm;
        bill.kdptLamaPindahTrf = dil.kdptLm;
        //bill.kdpt2Lm = dil.kdpt2Lm;
        bill.dayaLmPindahTrf = dil.dayaLm;
        //bill.faknpremiumLm = dil.faknpremiumLm;
        //bill.frtLm = dil.frtLm;
        //bill.fjnLm = dil.fjnLm;
        //bill.fakmLm = dil.fakmLm;
        //bill.fakmkvarhLm = dil.fakmkvarhLm;
        //bill.dayajbstLm = dil.dayajbstLm;
        bill.kdinvoice = dil.kdinvoice;
        //bill.rata2lwbp = dil.rata2lwbp;
        //bill.rata2wbp = dil.rata2wbp;
        bill.tahunKe = dil.tahunKe;
        //bill.dayaLmB2b = dil.dayaLmB2b;
        bill.flagppjangsa = dil.flagppjangsa;
        bill.flagppjangsb = dil.flagppjangsb;
        bill.flagppjangsc = dil.flagppjangsc;
        bill.postingbilling = "1";
        bill.sahlwbp = dpm.sahlwbp;
        bill.sahwbp = dpm.sahwbp;
        bill.sahkvarh = dpm.sahkvarh;
        bill.slalwbp = dpm.slalwbp;
        bill.slawbp = dpm.slawbp;
        bill.slakvarh = dpm.slakvarh;
        bill.kdkvamaks = dpm.kddayamax;

        BigDecimal resultDayaMaxWbp = BigDecimal.ZERO;
        if (dpm.kddayamax_wbp != null && dpm.kddayamax_wbp.equals("K")) {
            resultDayaMaxWbp = dpm.dayamax_wbp.multiply(BigDecimal.valueOf(1000));
        } else {
            resultDayaMaxWbp = dpm.dayamax_wbp;
        }
        if (resultDayaMaxWbp != null) {
            resultDayaMaxWbp = resultDayaMaxWbp.multiply(dpm.fkmkwh);
            bill.dayamaxWbp = resultDayaMaxWbp;
        }

        BigDecimal resultDayaMax = BigDecimal.ZERO;
        if (dpm.kddayamax != null && dpm.kddayamax.equals("K")) {
            resultDayaMax = dpm.dayamax.multiply(BigDecimal.valueOf(1000));
        } else {
            resultDayaMax = dpm.dayamax;
        }
        if (resultDayaMax != null) {
            resultDayaMax = resultDayaMax.multiply(dpm.fkmkwh);
            bill.dayamaks = resultDayaMax;
        }

        bill.tglcatat = LocalDateTime.now();
        bill.tglinisialisasi = LocalDateTime.now();
        bill.tglbacalalu = dpm.tglbaca1.format(formatter1);
        bill.tglbacaakhir = dpm.tglbaca.format(formatter1);
        bill.initby = dpm.uploadby;

        return bill;
    }

    public static Uni<BillingDTO> mapFromEntity(Billing billing) {
        BillingDTO response = new BillingDTO();
        response.id = billing.id;
        response.idpel = billing.idpel;
        response.nopel = billing.nopel;
        response.nama = billing.nama;
        response.norek = billing.norek;
        response.unitup = billing.unitup;
        response.unitupi = billing.unitupi;
        response.unitap = billing.unitap;
        response.thblrek = billing.thblrek;
        response.frt = billing.frt;
        response.fjn = billing.fjn;
        response.kdppj = billing.kdppj;
        response.pemda = billing.pemda;
        response.tarif = billing.tarif;
        response.kogol = billing.kogol;
        response.kdinkaso = billing.kdinkaso;
        response.tgljttempo = billing.tgljttempo;
        response.kwhlwbp = billing.kwhlwbp;
        response.kwhwbp = billing.kwhwbp;
        response.rplwbp = billing.rplwbp;
        response.rpwbp = billing.rpwbp;
        response.rpblok3 = billing.rpblok3;
        response.rpkvarh = billing.rpkvarh;
        response.rpbeban = billing.rpbeban;
        response.rpptl = billing.rpptl;
        response.rptb = billing.rptb;
        response.rpppn = billing.rpppn;
        response.rpbpju = billing.rpbpju;
        response.rpbptrafo = billing.rpbptrafo;
        response.rpsewatrafo = billing.rpsewatrafo;
        response.rpsewakap = billing.rpsewakap;
        response.rpangsa = billing.rpangsa;
        response.rpangsb = billing.rpangsb;
        response.rpangsc = billing.rpangsc;
        response.rpmat = billing.rpmat;
        response.rppln = billing.rppln;
        response.rptag = billing.rptag;
        response.rpproduksi = billing.rpproduksi;
        response.rpsubsidi = billing.rpsubsidi;
        response.rptdllama = billing.rptdllama;
        response.rptdlbaru = billing.rptdlbaru;
        response.rpselisih = billing.rpselisih;
        response.rpreduksi = billing.rpreduksi;
        response.rpbk1 = billing.rpbk1;
        response.rpbk2 = billing.rpbk2;
        response.rpbk3 = billing.rpbk3;
        response.rprekBruto = billing.rprekBruto;
        response.rprekNetto = billing.rprekNetto;
        response.rptagBruto = billing.rptagBruto;
        response.rptagNetto = billing.rptagNetto;
        response.rpjbst = billing.rpjbst;
        response.rpkompensasi = billing.rpkompensasi;
        response.rpdiskonPremium = billing.rpdiskonPremium;
        response.rpdiskon = billing.rpdiskon;
        response.rpinvoice = billing.rpinvoice;
        response.rpptlplus = billing.rpptlplus;
        response.rpptlminus = billing.rpptlminus;
        response.rpppjptl = billing.rpppjptl;
        response.rpppjangsa = billing.rpppjangsa;
        response.rpppjangsb = billing.rpppjangsb;
        response.rpppjangsc = billing.rpppjangsc;
        response.rpppnR3 = billing.rpppnR3;
        response.rpppnBptrafo = billing.rpppnBptrafo;
        response.rpppnSewatrafo = billing.rpppnSewatrafo;
        response.rpppnOpspararel = billing.rpppnOpspararel;
        response.rpppnSewakap = billing.rpppnSewakap;
        response.rpppnLain = billing.rpppnLain;
        response.rptagMat = billing.rptagMat;
        response.rpppnRec = billing.rpppnRec;
        response.rprec = billing.rprec;
        response.rpppnUap = billing.rpppnUap;
        response.rpblok4 = billing.rpblok4;
        response.rpopsel = billing.rpopsel;
        response.rpsewatrafoDil = billing.rpsewatrafoDil;
        response.slalwbp = billing.slalwbp;
        response.sahlwbp = billing.sahlwbp;
        response.slalwbpPasang = billing.slalwbpPasang;
        response.sahlwbpCabut = billing.sahlwbpCabut;
        response.slalwbp1 = billing.slalwbp1;
        response.sahlwbp1 = billing.sahlwbp1;
        response.slalwbp2 = billing.slalwbp2;
        response.sahlwbp2 = billing.sahlwbp2;
        response.lwbp1rataIns = billing.lwbp1rataIns;
        response.sahlwbp1Cabut = billing.sahlwbp1Cabut;
        response.slalwbp1Pasang = billing.slalwbp1Pasang;
        response.sahlwbp2Cabut = billing.sahlwbp2Cabut;
        response.slalwbp2Pasang = billing.slalwbp2Pasang;
        response.pemkwhLwbp1 = billing.pemkwhLwbp1;
        response.pemkwhLwbp2 = billing.pemkwhLwbp2;
        response.sahlwbpExport = billing.sahlwbpExport;
        response.slalwbpPasangExp = billing.slalwbpPasangExp;
        response.sahlwbpCabutExp = billing.sahlwbpCabutExp;

        return Uni.createFrom().item(response);
    }

    public static Billing mapFromDto(Billing bill, BillingParam req) {
        bill.idpel = req.idpel;
        bill.nopel = req.nopel;
        bill.nama = req.nama;
        bill.norek = req.norek;
        bill.unitup = req.unitup;
        bill.unitupi = req.unitupi;
        bill.unitap = req.unitap;
        bill.thblrek = req.thblrek;
        bill.frt = req.frt;
        bill.fjn = req.fjn;
        bill.kdppj = req.kdppj;
        bill.pemda = req.pemda;
        bill.tarif = req.tarif;
        bill.kogol = req.kogol;
        bill.kdinkaso = req.kdinkaso;
        bill.tgljttempo = req.tgljttempo;
        bill.kwhlwbp = req.kwhlwbp;
        bill.kwhwbp = req.kwhwbp;
        bill.rplwbp = req.rplwbp;
        bill.rpwbp = req.rpwbp;
        bill.rpblok3 = req.rpblok3;
        bill.rpkvarh = req.rpkvarh;
        bill.rpbeban = req.rpbeban;
        bill.rpptl = req.rpptl;
        bill.rptb = req.rptb;
        bill.rpppn = req.rpppn;
        bill.rpbpju = req.rpbpju;
        bill.rpbptrafo = req.rpbptrafo;
        bill.rpsewatrafo = req.rpsewatrafo;
        bill.rpsewakap = req.rpsewakap;
        bill.rpangsa = req.rpangsa;
        bill.rpangsb = req.rpangsb;
        bill.rpangsc = req.rpangsc;
        bill.rpmat = req.rpmat;
        bill.rppln = req.rppln;
        bill.rptag = req.rptag;
        bill.rpproduksi = req.rpproduksi;
        bill.rpsubsidi = req.rpsubsidi;
        bill.rptdllama = req.rptdllama;
        bill.rptdlbaru = req.rptdlbaru;
        bill.rpselisih = req.rpselisih;
        bill.rpreduksi = req.rpreduksi;
        bill.rpbk1 = req.rpbk1;
        bill.rpbk2 = req.rpbk2;
        bill.rpbk3 = req.rpbk3;
        bill.rprekBruto = req.rprekBruto;
        bill.rprekNetto = req.rprekNetto;
        bill.rptagBruto = req.rptagBruto;
        bill.rptagNetto = req.rptagNetto;
        bill.rpjbst = req.rpjbst;
        bill.rpkompensasi = req.rpkompensasi;
        bill.rpdiskonPremium = req.rpdiskonPremium;
        bill.rpdiskon = req.rpdiskon;
        bill.rpinvoice = req.rpinvoice;
        bill.rpptlplus = req.rpptlplus;
        bill.rpptlminus = req.rpptlminus;
        bill.rpppjptl = req.rpppjptl;
        bill.rpppjangsa = req.rpppjangsa;
        bill.rpppjangsb = req.rpppjangsb;
        bill.rpppjangsc = req.rpppjangsc;
        bill.rpppnR3 = req.rpppnR3;
        bill.rpppnBptrafo = req.rpppnBptrafo;
        bill.rpppnSewatrafo = req.rpppnSewatrafo;
        bill.rpppnOpspararel = req.rpppnOpspararel;
        bill.rpppnSewakap = req.rpppnSewakap;
        bill.rpppnLain = req.rpppnLain;
        bill.rptagMat = req.rptagMat;
        bill.rpppnRec = req.rpppnRec;
        bill.rprec = req.rprec;
        bill.rpppnUap = req.rpppnUap;
        bill.rpblok4 = req.rpblok4;
        bill.rpopsel = req.rpopsel;
        bill.rpsewatrafoDil = req.rpsewatrafoDil;
        bill.slalwbp = req.rasioDaya;
        bill.sahlwbp = req.sahlwbp;
        bill.blok4 = req.blok4;
        bill.kdpt = req.kdpt;
        bill.daya = req.daya.longValue();
        bill.faknpremium = req.faknpremium;
        bill.dayar312 = req.dayar312;
        bill.subkogol = req.subkogol;
        bill.kdklp = req.kdklp;
        bill.kdind = req.kdind;
        bill.kdam = req.kdam;
        bill.kdkvamaks = req.kdkvamaks;
        bill.maxDemand = req.maxDemand;
        bill.kdbpt = req.kdbpt;
        bill.dayabpt = req.dayabpt;
        bill.kddayabpt = req.kddayabpt;
        bill.jnsmut = req.jnsmut;
        bill.thblmut = req.thblmut;
        bill.kdmut = req.kdmut;
        bill.tglnyala = req.tglnyala;
        bill.tglrubah = req.tglrubah;
        bill.kdpembmeter = req.kdpembmeter;
        bill.fakm = req.fakm;
        bill.fakmkvarh = req.fakmkvarh;
        bill.fakmkvam = req.fakmkvam;
        bill.tglbacalalu = req.tglbacalalu;
        bill.tglbacaakhir = req.tglbacaakhir;
        bill.slawbp = req.slawbp;
        bill.sahwbp = req.sahwbp;
        bill.slakvarh = req.slakvarh;
        bill.sahkvarh = req.sahkvarh;
        bill.sahkvamaks = req.sahkvamaks;
        bill.dayamaks = req.dayamaks;
        bill.sahkvamaxWbp = req.sahkvamaxWbp;
        bill.dayamaxWbp = req.dayamaxWbp;
        bill.rasioDaya = req.rasioDaya;
        bill.blok3 = req.blok3;
        bill.pemkwh = req.pemkwh;
        bill.pemkvarh = req.pemkvarh;
        bill.kelbkvarh = req.kelbkvarh;
        bill.jamnyala = req.jamnyala;
        bill.kdangsa = req.kdangsa;
        bill.lamaangsa = req.lamaangsa;
        bill.thblangs1a = req.thblangs1a;
        bill.angskea = req.angskea;
        bill.kdangsb = req.kdangsb;
        bill.lamaangsb = req.lamaangsb;
        bill.thblangs1b = req.thblangs1b;
        bill.angskeb = req.angskeb;
        bill.kdangsc = req.kdangsc;
        bill.lamaangsc = req.lamaangsc;
        bill.thblangs1c = req.thblangs1c;
        bill.angskec = req.angskec;
        bill.flagdiskon = req.flagdiskon;
        bill.prosendiskon = req.prosendiskon;
        bill.kddiskon = req.kddiskon;
        bill.jnsdiskon = req.jnsdiskon;
        bill.kdinvoice = req.kdinvoice;
        bill.statusemin = req.statusemin;
        bill.fraksibptrafo = req.fraksibptrafo;
        bill.trfbptrafo = req.trfbptrafo;
        bill.trfsewakap = req.trfsewakap;
        bill.dayajbst = req.dayajbst;
        bill.kdbedajbst = req.kdbedajbst;
        bill.jumlahPadam = req.jumlahPadam;
        bill.hitungby = req.hitungby;
        bill.msg = req.msg;
        bill.kdproses = req.kdproses;
        bill.kdprosesklp = req.kdprosesklp;
        bill.dlpd = req.dlpd;
        bill.prosenppj = req.prosenppj;
        bill.dlpdLm = req.dlpdLm;
        bill.dlpdFkm = req.dlpdFkm;
        bill.dlpdKvarh = req.dlpdKvarh;
        bill.dlpd3bln = req.dlpd3bln;
        bill.dlpdJnsmutasi = req.dlpdJnsmutasi;
        bill.dlpdTglbaca = req.dlpdTglbaca;
        bill.alasanKoreksi = req.alasanKoreksi;
        bill.flagsewakap = req.flagsewakap;
        bill.faradkap = req.faradkap;
        bill.kddk = req.kddk;
        bill.kdbacameter = req.kdbacameter;
        bill.kdrekg = req.kdrekg;
        bill.copyrek = req.copyrek;
        bill.kdmeterai = req.kdmeterai;
        bill.jnsmutAde = req.jnsmutAde;
        bill.flagppjangsa = req.flagppjangsa;
        bill.flagppjangsb = req.flagppjangsb;
        bill.flagppjangsc = req.flagppjangsc;


        return bill;
    }

    public static Billing OutputToEntity(Billing billing, Output output, String tglmulaihitung, String tglselesaihitung) {
        billing.pemkwh = output.getPemkwh() == null ? BigDecimal.ZERO : output.getPemkwh();
        billing.kwhlwbp = output.getKwhlwbp() == null ? BigDecimal.ZERO : output.getKwhlwbp();
        billing.kwhwbp = output.getKwhwbp() == null ? BigDecimal.ZERO : output.getKwhwbp();
        billing.blok3 = output.getBlok3() == null ? BigDecimal.ZERO : output.getBlok3();
        billing.pemkvarh = output.getPemkvarh() == null ? BigDecimal.ZERO : output.getPemkvarh();
        billing.kelbkvarh = output.getKelbkvarh() == null ? BigDecimal.ZERO : output.getKelbkvarh();
        billing.rpbeban = output.getRpbeban() == null ? BigDecimal.ZERO : output.getRpbeban();
        billing.rplwbp = output.getRplwbp() == null ? BigDecimal.ZERO : output.getRplwbp();
        billing.rpwbp = output.getRpwbp() == null ? BigDecimal.ZERO : output.getRpwbp();
        billing.rpblok3 = output.getRpblok3() == null ? BigDecimal.ZERO : output.getRpblok3();
        billing.rpkvarh = output.getRpkvarh() == null ? BigDecimal.ZERO : output.getRpkvarh();
        billing.rpptl = output.getRpptl() == null ? BigDecimal.ZERO : output.getRpptl();
        billing.rpdiskon = output.getRpdiskon() == null ? BigDecimal.ZERO : output.getRpdiskon();
        billing.rpbpju = output.getRpbpju() == null ? BigDecimal.ZERO : output.getRpbpju();
        billing.rpppn = output.getRpppn() == null ? BigDecimal.ZERO : output.getRpppn();
        billing.rpsewakap = output.getRpsewakap() == null ? BigDecimal.ZERO : output.getRpsewakap();
        billing.rpbptrafo = output.getRpbptrafo() == null ? BigDecimal.ZERO : output.getRpbptrafo();
        billing.rpsewatrafo = output.getRpsewatrafo() == null ? BigDecimal.ZERO : output.getRpsewatrafo();
        billing.rpangsa = output.getRpangsa() == null ? BigDecimal.ZERO : output.getRpangsa();
        billing.rpangsb = output.getRpangsb() == null ? BigDecimal.ZERO : output.getRpangsb();
        billing.rpangsc = output.getRpangsc() == null ? BigDecimal.ZERO : output.getRpangsc();
        billing.rptag = output.getRptag() == null ? BigDecimal.ZERO : output.getRptag();
        billing.rpmat = output.getRpmat() == null ? BigDecimal.ZERO : output.getRpmat();
        billing.rptagMat = output.getRptag_mat() == null ? BigDecimal.ZERO : output.getRptag_mat();
        billing.rpbk1 = output.getRpbk1() == null ? BigDecimal.ZERO : output.getRpbk1();
        billing.rpbk2 = output.getRpbk2() == null ? BigDecimal.ZERO : output.getRpbk2();
        billing.rpbk3 = output.getRpbk3() == null ? BigDecimal.ZERO : output.getRpbk3();
        billing.rprekNetto = output.getRprek_netto() == null ? BigDecimal.ZERO : output.getRprek_netto();
        billing.rprekBruto = output.getRprek_bruto() == null ? BigDecimal.ZERO : output.getRprek_bruto();
        billing.rptagNetto = output.getRptag_netto() == null ? BigDecimal.ZERO : output.getRptag_netto();
        billing.rptagBruto = output.getRptag_bruto() == null ? BigDecimal.ZERO : output.getRptag_bruto();
        billing.rpppnR3 = output.getRpppn_r3() == null ? BigDecimal.ZERO : output.getRpppn_r3();
        billing.rpppnUap = output.getRpppn_uap() == null ? BigDecimal.ZERO : output.getRpppn_uap();
        billing.rpppnSewatrafo = output.getRpppn_sewatrafo() == null ? BigDecimal.ZERO : output.getRpppn_sewatrafo();
        billing.rpppnSewakap = output.getRpppnSewakap() == null ? BigDecimal.ZERO : output.getRpppnSewakap();
        billing.rpppnBptrafo = output.getRpppn_bptrafo() == null ? BigDecimal.ZERO : output.getRpppn_bptrafo();
        billing.rpppnRec = output.getRpppn_rec() == null ? BigDecimal.ZERO : output.getRpppn_rec();
        billing.rpppnOpspararel = output.getRpppn_opspararel() == null ? BigDecimal.ZERO : output.getRpppn_opspararel();
        billing.rpppjptl = output.getRpppjptl() == null ? BigDecimal.ZERO : output.getRpppjptl();
        billing.rpppjangsa = output.getRpppjangsa() == null ? BigDecimal.ZERO : output.getRpppjangsa();
        billing.rpppjangsb = output.getRpppjangsb() == null ? BigDecimal.ZERO : output.getRpppjangsb();
        billing.rpppjangsc = output.getRpppjangsc() == null ? BigDecimal.ZERO : output.getRpppjangsc();
        billing.rppln = output.getRppln() == null ? BigDecimal.ZERO : output.getRppln();
        billing.rpinvoice = output.getRpinvoice() == null ? BigDecimal.ZERO : output.getRpinvoice();
        billing.rpopsel = output.getRpopsel() == null ? BigDecimal.ZERO : output.getRpopsel();
        billing.rpdiskonPremium = output.getRpdiskon_premium() == null ? BigDecimal.ZERO : output.getRpdiskon_premium();
        billing.rpkompensasi = output.getRpkompensasi() == null ? BigDecimal.ZERO : output.getRpkompensasi();
        billing.postingbilling = output.getRptag() == null ? "1" : "2";
        billing.msg = output.getRptag() == null ? "Gagal Hitung" : "0";
        billing.tglVerifikasiDlpd = null;
        billing.ptgVerifikasiDlpd = null;
        billing.tglhitung = LocalDateTime.now();
        billing.tglmulaihitung=LocalDateTime.parse(tglmulaihitung, formatter2);
        billing.tglselesaihitung=LocalDateTime.parse(tglselesaihitung, formatter2);
        billing.rprec = output.getRprec() == null ? BigDecimal.ZERO : output.getRprec();
        billing.rpreduksi = output.getRpreduksi() == null ? BigDecimal.ZERO : output.getRpreduksi();
        billing.rpcapacity_plts = output.getRpcapacity_plts()==null ? BigDecimal.ZERO : output.getRpcapacity_plts();
        billing.rpppn_capacity_plts = output.getRpppn_capacity_plts()==null ? BigDecimal.ZERO : output.getRpppn_capacity_plts();
        billing.jamnyala = output.getJamnyala()== null ? BigDecimal.ZERO : output.getJamnyala();
        billing.prosenppj = output.getProsenPpj()== null ? BigDecimal.ZERO : output.getProsenPpj();
        billing.rpuap = output.getRpuap()== null ? BigDecimal.ZERO : output.getRpuap();
        billing.jmlunitrec = output.getJmlunitrec()== null ? 0 : output.getJmlunitrec();
        billing.hargaRec = output.getHargarec() == null ? BigDecimal.ZERO : output.getHargarec();
        return billing;
    }

    public static Billing mapToEntityForVerifikasiDlpd(Billing bill, VerifikasiDlpdParam req) {
        bill.ptgVerifikasiDlpd = req.userId;
        bill.flagVerifikasiDlpd = req.isApprove ? 1 : 0;
        bill.alasanVerifikasiDlpd = req.reason;
        bill.tglVerifikasiDlpd = LocalDateTime.now();
        bill.postingbilling = req.isApprove ? bill.postingbilling : "1";

        return bill;
    }

    public static Billing mapToEntityForKoreksiBilling(Billing bill, List<KoreksiBillingDTO.DataBillEnergiDTO> lstDataEnergi, String id_user) {
        if (lstDataEnergi.size() > 1){
            KoreksiBillingDTO.DataBillEnergiDTO koreksiBaru=lstDataEnergi
                    .stream().filter(dataenergi -> dataenergi.pecahKe==0).findFirst().orElseThrow(() -> new AppException("Data energi tidak ditemukan"));
            KoreksiBillingDTO.DataBillEnergiDTO koreksiLama=lstDataEnergi
                    .stream().filter(dataenergi -> dataenergi.pecahKe==1).findFirst().orElseThrow(() -> new AppException("Data energi tidak ditemukan"));
            bill.sahlwbp = koreksiBaru.lwbpAkhir;
            bill.sahwbp = koreksiBaru.wbpAkhir;
            bill.sahkvarh = koreksiBaru.kvarhAkhir;
            bill.ptg_koreksi = id_user;
            bill.tgl_koreksi = LocalDateTime.now();
//            bill.slalwbp = koreksiLama.lwbpLalu;
//            bill.slawbp = koreksiLama.wbpLalu;
//            bill.slakvarh = koreksiLama.kvarhLalu;
        }else {
            KoreksiBillingDTO.DataBillEnergiDTO koreksi=lstDataEnergi.getFirst();
            bill.sahlwbp = koreksi.lwbpAkhir;
            bill.sahwbp = koreksi.wbpAkhir;
            bill.sahkvarh = koreksi.kvarhAkhir;
            bill.ptg_koreksi = id_user;
            bill.tgl_koreksi = LocalDateTime.now();
//            bill.slalwbp = koreksi.lwbpLalu;
//            bill.slawbp = koreksi.wbpLalu;
//            bill.slakvarh = koreksi.kvarhLalu;

        }

        bill.postingbilling = "1";
        bill.tglrequesthitung = null;
        bill.tglmulaihitung = null;
        bill.tglselesaihitung = null;
        return bill;
    }
    public static Billing mapToEntityForKoreksiBillingPlts(Billing bill, List<KoreksiBillingPltsDTO.DataBillEnergiPltsDTO> lstDataEnergi, String id_user) {
        if (lstDataEnergi.size() > 1){
            KoreksiBillingPltsDTO.DataBillEnergiPltsDTO koreksiBaru=lstDataEnergi.stream().filter(dataenergi -> dataenergi.pecahKe==0).findFirst().orElseThrow(() -> new AppException("Data energi tidak ditemukan"));
            KoreksiBillingPltsDTO.DataBillEnergiPltsDTO koreksiLama=lstDataEnergi.stream().filter(dataenergi -> dataenergi.pecahKe==1).findFirst().orElseThrow(() -> new AppException("Data energi tidak ditemukan"));
            bill.sahlwbp = koreksiBaru.lwbpAkhir;
            bill.sahwbp = koreksiBaru.wbpAkhir;
            bill.sahkvarh = koreksiBaru.kvarhAkhir;
            bill.ptg_koreksi = id_user;
            bill.tgl_koreksi = LocalDateTime.now();
            // bill.slalwbp = koreksiLama.lwbpLalu;
            // bill.slawbp = koreksiLama.wbpLalu;
            // bill.slakvarh = koreksiLama.kvarhLalu;
        }else {
            KoreksiBillingPltsDTO.DataBillEnergiPltsDTO koreksi=lstDataEnergi.getFirst();
            bill.sahlwbp = koreksi.lwbpAkhir;
            bill.sahwbp = koreksi.wbpAkhir;
            bill.sahkvarh = koreksi.kvarhAkhir;
            bill.ptg_koreksi = id_user;
            bill.tgl_koreksi = LocalDateTime.now();
            // bill.slalwbp = koreksi.lwbpLalu;
            // bill.slawbp = koreksi.wbpLalu;
            // bill.slakvarh = koreksi.kvarhLalu;

        }

        bill.postingbilling = "1";
        bill.tglrequesthitung = null;
        bill.tglmulaihitung = null;
        bill.tglselesaihitung = null;
        return bill;
    }

    private static String formatTanggal(String tanggal) {
        if (tanggal != null && tanggal.contains("/")) {
            return tanggal.replace("/", "");
        }
        return tanggal; // jika sudah dalam format yyyyMMdd
    }

    public static Billing720JN mapBilling720(Billing720JN billing720, Billing param) {
        BeanUtil.copyProperties(param, billing720);
        // billing720.id_log = UUID.randomUUID().toString();
        // Format ulang tglbacalalu dan tglbacaakhir jika masih mengandung '/'
        billing720.tglbacalalu = formatTanggal(param.tglbacalalu);
        billing720.tglbacaakhir = formatTanggal(param.tglbacaakhir);
        return billing720;
    }

    public static LogBilling toLogEntity(Billing billing) {
        LogBilling billingLog = new LogBilling();
        billingLog.id = UUID.randomUUID();
        billingLog.idDataBil = billing.id;
        billingLog.idpel = billing.idpel;
        billingLog.thblrek = billing.thblrek;
        billingLog.unitupi = billing.unitupi;
        billingLog.unitap = billing.unitap;
        billingLog.unitup = billing.unitup;
        billingLog.tarif = billing.tarif;
        billingLog.kdpt = billing.kdpt;
        billingLog.kdpt_2 = billing.kdpt_2;
        billingLog.daya = BigDecimal.valueOf(billing.daya);
        billingLog.faknpremium = billing.faknpremium;
        billingLog.dayar312 = billing.dayar312;
        billingLog.kogol = billing.kogol;
        billingLog.subkogol = billing.subkogol;
        billingLog.pemda = billing.pemda;
        billingLog.kdppj = billing.kdppj;
        billingLog.kdinkaso = billing.kdinkaso;
        billingLog.kdklp = billing.kdklp;
        billingLog.kdind = billing.kdind;
        billingLog.kdam = billing.kdam;
        billingLog.kdkvamaks = billing.kdkvamaks;
        billingLog.maxDemand = billing.maxDemand;
        billingLog.frt = billing.frt;
        billingLog.fjn = billing.fjn;
        billingLog.kdbpt = billing.kdbpt;
        billingLog.dayabpt = billing.dayabpt;
        billingLog.kddayabpt = billing.kddayabpt;
        billingLog.jnsmut = billing.jnsmut;
        billingLog.thblmut = billing.thblmut;
        billingLog.kdmut = billing.kdmut;
        billingLog.tglnyala = billing.tglnyala;
        billingLog.tglrubah = billing.tglrubah;
        billingLog.kdpembmeter = billing.kdpembmeter;
        billingLog.fakm = billing.fakm;
        billingLog.fakmkvarh = billing.fakmkvarh;
        billingLog.fakmkvam = billing.fakmkvam;
        billingLog.tglbacalalu = billing.tglbacalalu;
        billingLog.tglbacaakhir = billing.tglbacaakhir;
        billingLog.slalwbp = billing.slalwbp;
        billingLog.sahlwbp = billing.sahlwbp;
        billingLog.slawbp = billing.slawbp;
        billingLog.sahwbp = billing.sahwbp;
        billingLog.slakvarh = billing.slakvarh;
        billingLog.sahkvarh = billing.sahkvarh;
        billingLog.sahkvamaks = billing.sahkvamaks;
        billingLog.dayamaks = billing.dayamaks;
        billingLog.sahkvamaxWbp = billing.sahkvamaxWbp;
        billingLog.dayamaxWbp = billing.dayamaxWbp;
        billingLog.rasioDaya = billing.rasioDaya;
        billingLog.kwhlwbp = billing.kwhlwbp;
        billingLog.jmlunitrec = billing.jmlunitrec;
        billingLog.hargaRec = billing.hargaRec;
        billingLog.kwhwbp = billing.kwhwbp;
        billingLog.blok3 = billing.blok3;
        billingLog.pemkwh = billing.pemkwh;
        billingLog.pemkvarh = billing.pemkvarh;
        billingLog.kelbkvarh = billing.kelbkvarh;
        billingLog.jamnyala = billing.jamnyala;
        billingLog.rplwbp = billing.rplwbp;
        billingLog.rpwbp = billing.rpwbp;
        billingLog.rpblok3 = billing.rpblok3;
        billingLog.rpkvarh = billing.rpkvarh;
        billingLog.rpbeban = billing.rpbeban;
        billingLog.rpptl = billing.rpptl;
        billingLog.rptb = billing.rptb;
        billingLog.rpuap = billing.rpuap;
        billingLog.rpppn = billing.rpppn;
        billingLog.rpbpju = billing.rpbpju;
        billingLog.rpbptrafo = billing.rpbptrafo;
        billingLog.rpsewatrafo = billing.rpsewatrafo;
        billingLog.rpsewakap = billing.rpsewakap;
        billingLog.kdangsa = billing.kdangsa;
        billingLog.rpangsa = billing.rpangsa;
        billingLog.lamaangsa = billing.lamaangsa;
        billingLog.thblangs1a = billing.thblangs1a;
        billingLog.angskea = billing.angskea;
        billingLog.kdangsb = billing.kdangsb;
        billingLog.rpangsb = billing.rpangsb;
        billingLog.lamaangsb = billing.lamaangsb;
        billingLog.thblangs1b = billing.thblangs1b;
        billingLog.angskeb = billing.angskeb;
        billingLog.kdangsc = billing.kdangsc;
        billingLog.rpangsc = billing.rpangsc;
        billingLog.lamaangsc = billing.lamaangsc;
        billingLog.thblangs1c = billing.thblangs1c;
        billingLog.angskec = billing.angskec;
        billingLog.rpmat = billing.rpmat;
        billingLog.rppln = billing.rppln;
        billingLog.rptag = billing.rptag;
        billingLog.rpproduksi = billing.rpproduksi;
        billingLog.rpsubsidi = billing.rpsubsidi;
        billingLog.rptdllama = billing.rptdllama;
        billingLog.rptdlbaru = billing.rptdlbaru;
        billingLog.rpselisih = billing.rpselisih;
        billingLog.rpreduksi = billing.rpreduksi;
        billingLog.rpbk1 = billing.rpbk1;
        billingLog.rpbk2 = billing.rpbk2;
        billingLog.rpbk3 = billing.rpbk3;
        billingLog.norek = billing.norek;
        billingLog.rprekBruto = billing.rprekBruto;
        billingLog.rprekNetto = billing.rprekNetto;
        billingLog.rptagBruto = billing.rptagBruto;
        billingLog.rptagNetto = billing.rptagNetto;
        billingLog.rpjbst = billing.rpjbst;
        billingLog.rpkompensasi = billing.rpkompensasi;
        billingLog.rpdiskonPremium = billing.rpdiskonPremium;
        billingLog.rpdiskon = billing.rpdiskon;
        billingLog.flagdiskon = billing.flagdiskon;
        billingLog.prosendiskon = billing.prosendiskon;
        billingLog.kddiskon = billing.kddiskon;
        billingLog.jnsdiskon = billing.jnsdiskon;
        billingLog.rpinvoice = billing.rpinvoice;
        billingLog.kdinvoice = billing.kdinvoice;
        billingLog.statusemin = billing.statusemin;
        billingLog.fraksibptrafo = billing.fraksibptrafo;
        billingLog.trfbptrafo = billing.trfbptrafo;
        billingLog.trfsewakap = billing.trfsewakap;
        billingLog.slalwbpPasang = billing.slalwbpPasang;
        billingLog.sahlwbpCabut = billing.sahlwbpCabut;
        billingLog.slawbpPasang = billing.slawbpPasang;
        billingLog.sahwbpCabut = billing.sahwbpCabut;
        billingLog.slakvarhPasang = billing.slakvarhPasang;
        billingLog.sahkvarhCabut = billing.sahkvarhCabut;
        billingLog.sahkvamaksCabut = billing.sahkvamaksCabut;
        billingLog.dayamaksCabut = billing.dayamaksCabut;
        billingLog.dayajbst = billing.dayajbst;
        billingLog.kdbedajbst = billing.kdbedajbst;
        billingLog.jumlahPadam = billing.jumlahPadam;
        billingLog.tglinisialisasi = billing.tglinisialisasi;
        billingLog.tglcatat = billing.tglcatat;
        billingLog.tglhitung = billing.tglhitung;
        billingLog.tglsah = billing.tglsah;
        billingLog.initby = billing.initby;
        billingLog.catatby = billing.catatby;
        billingLog.hitungby = billing.hitungby;
        billingLog.sahby = billing.sahby;
        billingLog.pecahan = billing.pecahan;
        billingLog.msg = billing.msg;
        billingLog.batch = billing.batch;
        billingLog.postingbilling = billing.postingbilling;
        billingLog.kdproses = billing.kdproses;
        billingLog.kdprosesklp = billing.kdprosesklp;
        billingLog.tgljttempo = billing.tgljttempo;
        billingLog.tglbayarAwal = billing.tglbayarAwal;
        billingLog.tglbayarAkhir = billing.tglbayarAkhir;
        billingLog.dlpd = billing.dlpd;
        billingLog.tglDpp = billing.tglDpp;
        billingLog.rpptlplus = billing.rpptlplus;
        billingLog.rpptlminus = billing.rpptlminus;
        billingLog.prosenppj = billing.prosenppj;
        billingLog.tglKargo = billing.tglKargo;
        billingLog.slalwbp1 = billing.slalwbp1;
        billingLog.sahlwbp1 = billing.sahlwbp1;
        billingLog.slalwbp2 = billing.slalwbp2;
        billingLog.sahlwbp2 = billing.sahlwbp2;
        billingLog.wbprataIns = billing.wbprataIns;
        billingLog.lwbp1rataIns = billing.lwbp1rataIns;
        billingLog.tipeskema = billing.tipeskema;
        billingLog.skema4 = billing.skema4;
        billingLog.sahlwbp1Cabut = billing.sahlwbp1Cabut;
        billingLog.slalwbp1Pasang = billing.slalwbp1Pasang;
        billingLog.sahlwbp2Cabut = billing.sahlwbp2Cabut;
        billingLog.slalwbp2Pasang = billing.slalwbp2Pasang;
        billingLog.kwhWbpPadamHari = billing.kwhWbpPadamHari;
        billingLog.jmlpadam = billing.jmlpadam;
        billingLog.kdgerak = billing.kdgerak;
        billingLog.rpppjptl = billing.rpppjptl;
        billingLog.rpppjangsa = billing.rpppjangsa;
        billingLog.rpppjangsb = billing.rpppjangsb;
        billingLog.rpppjangsc = billing.rpppjangsc;
        billingLog.tarifLmPindahTrf = billing.tarifLmPindahTrf;
        billingLog.dayaLmPindahTrf = billing.dayaLmPindahTrf;
        billingLog.kdptLamaPindahTrf = billing.kdptLamaPindahTrf;
        billingLog.dlpdLm = billing.dlpdLm;
        billingLog.dlpdFkm = billing.dlpdFkm;
        billingLog.dlpdKvarh = billing.dlpdKvarh;
        billingLog.dlpd3bln = billing.dlpd3bln;
        billingLog.dlpdJnsmutasi = billing.dlpdJnsmutasi;
        billingLog.dlpdTglbaca = billing.dlpdTglbaca;
        billingLog.alasanKoreksi = billing.alasanKoreksi;
        billingLog.pemkwhLwbp1 = billing.pemkwhLwbp1;
        billingLog.pemkwhLwbp2 = billing.pemkwhLwbp2;
        billingLog.rpppnR3 = billing.rpppnR3;
        billingLog.rpppnBptrafo = billing.rpppnBptrafo;
        billingLog.rpppnSewatrafo = billing.rpppnSewatrafo;
        billingLog.rpppnOpspararel = billing.rpppnOpspararel;
        billingLog.rpppnSewakap = billing.rpppnSewakap;
        billingLog.rpppnLain = billing.rpppnLain;
        billingLog.rptagMat = billing.rptagMat;
        billingLog.rpppnRec = billing.rpppnRec;
        billingLog.rprec = billing.rprec;
        billingLog.rpppnUap = billing.rpppnUap;
        billingLog.kwhKompor = billing.kwhKompor;
        billingLog.rpKompor = billing.rpKompor;
        billingLog.blok4 = billing.blok4;
        billingLog.rpblok4 = billing.rpblok4;
        billingLog.rpopsel = billing.rpopsel;
        billingLog.sahlwbpExport = billing.sahlwbpExport;
        billingLog.sahwbpExport = billing.sahwbpExport;
        billingLog.sahkvarhExport = billing.sahkvarhExport;
        billingLog.slalwbpPasangExp = billing.slalwbpPasangExp;
        billingLog.sahlwbpCabutExp = billing.sahlwbpCabutExp;
        billingLog.slawbpPasangExp = billing.slawbpPasangExp;
        billingLog.sahwbpCabutExp = billing.sahwbpCabutExp;
        billingLog.slakvarhPasangExp = billing.slakvarhPasangExp;
        billingLog.sahkvarhCabutExp = billing.sahkvarhCabutExp;
        billingLog.eminRmPltsBaru = billing.eminRmPltsBaru;
        billingLog.eminRmPltsLama = billing.eminRmPltsLama;
        billingLog.rpsewatrafoDil = billing.rpsewatrafoDil;
        billingLog.flagsewakap = billing.flagsewakap;
        billingLog.faradkap = billing.faradkap;
        billingLog.nama = billing.nama;
        billingLog.alamat = billing.alamat;
        billingLog.nopel = billing.nopel;
        billingLog.kddk = billing.kddk;
        billingLog.kdbacameter = billing.kdbacameter;
        billingLog.kdrekg = billing.kdrekg;
        billingLog.copyrek = billing.copyrek;
        billingLog.kdmeterai = billing.kdmeterai;
        billingLog.lokettgk = billing.lokettgk;
        billingLog.tahunKe = billing.tahunKe;
        billingLog.jnsmutAde = billing.jnsmutAde;
        billingLog.flagppjangsa = billing.flagppjangsa;
        billingLog.flagppjangsb = billing.flagppjangsb;
        billingLog.flagppjangsc = billing.flagppjangsc;
        billingLog.tglrequesthitung = billing.tglrequesthitung;
        billingLog.flagVerifikasiDlpd = billing.flagVerifikasiDlpd;
        billingLog.ptgVerifikasiDlpd = billing.ptgVerifikasiDlpd;
        billingLog.alasanVerifikasiDlpd = billing.alasanVerifikasiDlpd;
        billingLog.tglVerifikasiDlpd = billing.tglVerifikasiDlpd;
        billingLog.tglmulaihitung = billing.tglmulaihitung;
        billingLog.tglselesaihitung = billing.tglselesaihitung;
        billingLog.idbatchHitung = billing.idbatchHitung;
        billingLog.slalwbpExport = billing.slalwbpExport;
        billingLog.slawbpExport = billing.slawbpExport;
        billingLog.slakvarhExport = billing.slakvarhExport;
        billingLog.rpcapacity_plts = billing.rpcapacity_plts;
        billingLog.rpppn_capacity_plts = billing.rpppn_capacity_plts;
        billingLog.ptg_koreksi = billing.ptg_koreksi;
        billingLog.tgl_koreksi = billing.tgl_koreksi;

        return billingLog;
    }

}