package com.iconplus.ap2t.generator.mapper;

import com.iconplus.ap2t.data.entity.billing.Billing;
import com.iconplus.ap2t.data.entity.billing.BillingEnergi;
import com.iconplus.ap2t.generator.dto.KoreksiBillingDTO;
import com.iconplus.ap2t.generator.param.KoreksiBillingParam;
import io.smallrye.mutiny.Uni;

import java.util.ArrayList;
import java.util.List;

/**
 * @AUTHOR RR
 * @DATE 15/10/2024
 */
public class KoreksiBillingMapper {

    public static Uni<KoreksiBillingDTO> mapFromEntity(Billing billing, BillingEnergi billingEnergi){
        KoreksiBillingDTO respone = new KoreksiBillingDTO();

        respone.idpel = billing.idpel;
        respone.bulanRekening = billing.thblrek;
        respone.tahun = billing.thblrek.substring(0,4);
        respone.nama = billing.nama;
        respone.alamat = billing.alamat;
        respone.unitup = billing.unitup;
        respone.tarif = billing.tarif;
        respone.daya = billing.daya;
        respone.dlpd = billing.dlpd;
        respone.tarifLama =billing.tarifLmPindahTrf;
        respone.dayaLama = billing.dayaLmPindahTrf;
        respone.frt = billing.frt;
        respone.blthMutasi = billing.thblmut;
        respone.jenisMK = billing.jnsmut;
        respone.tglPerubahan = billing.tglrubah;
        respone.tglBacaLalu = billing.tglbacalalu;
        respone.tglBacaAkhir = billing.tglbacaakhir;
        respone.lwbpLalu = billing.slalwbp;
        respone.lwbpAkhir = billing.sahlwbp;
        respone.kwhLwbp = billing.kwhlwbp;
        respone.wbpLalu = billing.slawbp;
        respone.wbpAkhir = billing.sahwbp;
        respone.kwhWpb = billing.kwhwbp;
        respone.kvarhLalu = billing.slakvarh;
        respone.kvarhAkhir = billing.sahkvarh;
        respone.kwhKvarh = billing.pemkvarh;
        respone.kvamaxAkhir = billing.sahkvamaks;
        respone.kwhKvamax = billing.dayamaks;
        respone.kvawbpAkhir = billing.sahkvamaxWbp;
        respone.kwhKvawbp = billing.dayamaxWbp;
        respone.jamNyala = billing.jamnyala;
        respone.dataBillEnergiList = mapToDataBillEnergiList(billingEnergi);
        respone.uraianNilaiList = List.of(
                new KoreksiBillingDTO.UraianNilaiDTO("KWHLWBP", billing.kwhlwbp),
                new KoreksiBillingDTO.UraianNilaiDTO("KWHWBP", billing.kwhwbp),
                new KoreksiBillingDTO.UraianNilaiDTO("BLOK3", billing.blok3),
                new KoreksiBillingDTO.UraianNilaiDTO("PEMKWH", billing.pemkwh),
                new KoreksiBillingDTO.UraianNilaiDTO("PEMKVARH", billing.pemkvarh),
                new KoreksiBillingDTO.UraianNilaiDTO("KELBKVARH", billing.kelbkvarh),
                new KoreksiBillingDTO.UraianNilaiDTO("RPLWBP", billing.rplwbp),
                new KoreksiBillingDTO.UraianNilaiDTO("RPWBP", billing.rpwbp),
                new KoreksiBillingDTO.UraianNilaiDTO("RPBLOK3", billing.rpblok3),
                new KoreksiBillingDTO.UraianNilaiDTO("RPKVARH", billing.rpkvarh),
                new KoreksiBillingDTO.UraianNilaiDTO("RPBEBAN", billing.rpbeban),
                new KoreksiBillingDTO.UraianNilaiDTO("RPPTL", billing.rpptl),
                new KoreksiBillingDTO.UraianNilaiDTO("RPTB", billing.rptb),
                new KoreksiBillingDTO.UraianNilaiDTO("RPPPN", billing.rpppn),
                new KoreksiBillingDTO.UraianNilaiDTO("RPBPJU", billing.rpbpju),
                new KoreksiBillingDTO.UraianNilaiDTO("RPBPTRAFO", billing.rpbptrafo),
                new KoreksiBillingDTO.UraianNilaiDTO("RPSEWATRAFO", billing.rpsewatrafo),
                new KoreksiBillingDTO.UraianNilaiDTO("RPSEWAKAP", billing.rpsewakap),
                new KoreksiBillingDTO.UraianNilaiDTO("RPANGSA", billing.rpangsa),
                new KoreksiBillingDTO.UraianNilaiDTO("RPANGSB", billing.rpangsb),
                new KoreksiBillingDTO.UraianNilaiDTO("RPANGSC", billing.rpangsc),
                new KoreksiBillingDTO.UraianNilaiDTO("RPMAT", billing.rpmat),
                new KoreksiBillingDTO.UraianNilaiDTO("RPPLN", billing.rppln),
                new KoreksiBillingDTO.UraianNilaiDTO("RPTAG", billing.rptag),
                new KoreksiBillingDTO.UraianNilaiDTO("RPPRODUKSI", billing.rpproduksi),
                new KoreksiBillingDTO.UraianNilaiDTO("RPSUBSIDI", billing.rpsubsidi),
                new KoreksiBillingDTO.UraianNilaiDTO("RPTDLLAMA", billing.rptdllama),
                new KoreksiBillingDTO.UraianNilaiDTO("RPTDLBARU", billing.rptdlbaru),
                new KoreksiBillingDTO.UraianNilaiDTO("RPSELSILIH", billing.rpselisih),
                new KoreksiBillingDTO.UraianNilaiDTO("RPREDUKSI", billing.rpreduksi),
                new KoreksiBillingDTO.UraianNilaiDTO("RPBK1", billing.rpbk1),
                new KoreksiBillingDTO.UraianNilaiDTO("RPBK2", billing.rpbk2),
                new KoreksiBillingDTO.UraianNilaiDTO("RPBK3", billing.rpbk3),
                new KoreksiBillingDTO.UraianNilaiDTO("RPREK_BRUTO", billing.rprekBruto),
                new KoreksiBillingDTO.UraianNilaiDTO("RPREK_NETTO", billing.rprekNetto),
                new KoreksiBillingDTO.UraianNilaiDTO("RPTAG_BRUTO", billing.rptagBruto),
                new KoreksiBillingDTO.UraianNilaiDTO("RPTAG_NETTO", billing.rptagNetto),
                new KoreksiBillingDTO.UraianNilaiDTO("RPJBST", billing.rpjbst),
                new KoreksiBillingDTO.UraianNilaiDTO("RPKOMPENSASI", billing.rpkompensasi),
                new KoreksiBillingDTO.UraianNilaiDTO("RPDISKON_PREMIUM", billing.rpdiskonPremium),
                new KoreksiBillingDTO.UraianNilaiDTO("RPDISKON", billing.rpdiskon),
                new KoreksiBillingDTO.UraianNilaiDTO("RPINVOICE", billing.rpinvoice)
        );
        return Uni.createFrom().item(respone);
    }

    public static List<KoreksiBillingDTO.DataBillEnergiDTO> mapToDataBillEnergiList(BillingEnergi billingEnergi) {
        List<KoreksiBillingDTO.DataBillEnergiDTO> dataBillLEnergiList = new ArrayList<>();

        KoreksiBillingDTO.DataBillEnergiDTO dataBill = new KoreksiBillingDTO.DataBillEnergiDTO(
                billingEnergi.id,
                billingEnergi.pecahKe,
                billingEnergi.jenisEnergi,
                billingEnergi.slalwbp,   // lwbpLalu
                billingEnergi.sahlwbp,   // lwbpAkhir
                billingEnergi.slawbp,    // wbpLalu
                billingEnergi.sahwbp,    // wbpAkhir
                billingEnergi.slakvarh,  // kvarhLalu
                billingEnergi.sahkvarh,  // kvarhAkhir
                billingEnergi.sahkvamaks,// kvamaxAkhir
                billingEnergi.sahkvamaxWbp,// kvawbpAkhir
                billingEnergi.tglrubah);

        dataBillLEnergiList.add(dataBill);

        return dataBillLEnergiList;
    }




    public static void mapUpdateToBilling(KoreksiBillingParam koreksiBillingParam, BillingEnergi billingEnergi) {

        billingEnergi.id = koreksiBillingParam.dataBillEnergiList.getFirst().id;
        billingEnergi.slalwbp = koreksiBillingParam.dataBillEnergiList.getFirst().lwbpLalu;
        billingEnergi.slawbp = koreksiBillingParam.dataBillEnergiList.getFirst().wbpLalu;
        billingEnergi.slakvarh = koreksiBillingParam.dataBillEnergiList.getFirst().kvarhLalu;
        billingEnergi.sahlwbp = koreksiBillingParam.dataBillEnergiList.getFirst().lwbpAkhir;
        billingEnergi.sahwbp = koreksiBillingParam.dataBillEnergiList.getFirst().wbpAkhir;
        billingEnergi.sahkvarh = koreksiBillingParam.dataBillEnergiList.getFirst().kvarhAkhir;
        billingEnergi.sahkvamaks = koreksiBillingParam.dataBillEnergiList.getFirst().kvamaxAkhir;
        billingEnergi.sahkvamaxWbp = koreksiBillingParam.dataBillEnergiList.getFirst().kvawbpAkhir;
        if (billingEnergi.tglrubah != null) {
            billingEnergi.tglrubah =koreksiBillingParam.dataBillEnergiList.getFirst().tglPerubahan;
        }
    }
    public static BillingEnergi mapUpdateToBilling2(KoreksiBillingDTO.DataBillEnergiDTO koreksi,BillingEnergi billingEnergi) {

        billingEnergi.id = koreksi.id;
        billingEnergi.sahlwbp = koreksi.lwbpAkhir;
        billingEnergi.sahwbp = koreksi.wbpAkhir;
        billingEnergi.sahkvarh = koreksi.kvarhAkhir;
//        billingEnergi.slalwbp = koreksi.lwbpLalu;
//        billingEnergi.slawbp = koreksi.wbpLalu;
//        billingEnergi.slakvarh = koreksi.kvarhLalu;
//        billingEnergi.sahkvamaks = koreksi.kvamaxAkhir;
//        billingEnergi.sahkvamaxWbp = koreksi.kvawbpAkhir;
//        if (billingEnergi.tglrubah != null) {
//            billingEnergi.tglrubah =koreksi.tglPerubahan;
//        }

        return billingEnergi;
    }

}
