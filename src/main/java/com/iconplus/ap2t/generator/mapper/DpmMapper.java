package com.iconplus.ap2t.generator.mapper;

import com.iconplus.ap2t.common.param.billing.CrmDpmParam;
import com.iconplus.ap2t.common.param.billing.EntryStandParam;
import com.iconplus.ap2t.common.param.billing.MeterParam;
import com.iconplus.ap2t.common.utility.Helper;
import com.iconplus.ap2t.data.entity.billing.Dpm;
import com.iconplus.ap2t.data.entity.log.LogDPMKoreksi;
import com.iconplus.ap2t.data.entity.log.LogDataMeterAcmt;
import com.iconplus.ap2t.generator.response.StandMeterDil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class DpmMapper {

    private static final Logger log = LoggerFactory.getLogger(DpmMapper.class);

    public static void mapToDpm(Dpm dpm, MeterParam params) {
        dpm.thblrek = params.blth;
        dpm.idpel = params.idpel;
        dpm.unitup = params.unitup;
        dpm.kodeposting = "3";
        dpm.kd_pesan = params.kdBaca;
        dpm.tglbaca = params.tglBaca;
        dpm.sahwbp = params.wbp;
        dpm.sahlwbp = params.lwbp;
        dpm.sahkvarh = params.kvarh;
        if (params.kdDayaKvaMax != null && params.kdDayaKvaMax.equals("K")) {
            dpm.dayamax = params.dayaMax.multiply(BigDecimal.valueOf(1000));
        } else {
            dpm.dayamax = params.dayaMax;
        }
        dpm.dayamax_wbp = params.dayaMaxWbp;
        dpm.sahwbp_import = params.wbp_import;
        dpm.sahlwbp_import = params.lwbp_import;
        dpm.sahkvarh_import = params.kvarh_import;
        dpm.kddayamax_wbp = params.kdDayaMaxWbp;
        dpm.kvamaks = params.dayaKvaMax;
        dpm.uploadby = params.transaksi_by;
        dpm.ptgentrimeter = params.transaksi_by;
    }

    public static void mapToDpmTanpaMeter(Dpm dpm, MeterParam params) {
        dpm.thblrek = params.blth;
        dpm.idpel = params.idpel;
        dpm.unitup = params.unitup;
        dpm.kodeposting = "3";
        dpm.kd_pesan = params.kdBaca;
        dpm.tglbaca = params.tglBaca;
        dpm.sahwbp = BigDecimal.ZERO;
        dpm.sahlwbp = BigDecimal.ZERO;
        dpm.sahkvarh = BigDecimal.ZERO;
        if (params.kdDayaKvaMax != null && params.kdDayaKvaMax.equals("K")) {
            dpm.dayamax = params.dayaMax.multiply(BigDecimal.valueOf(1000));
        } else {
            dpm.dayamax = params.dayaMax;
        }
        dpm.dayamax_wbp = params.dayaMaxWbp;
        dpm.sahwbp_import = BigDecimal.ZERO;
        dpm.sahlwbp_import = BigDecimal.ZERO;
        dpm.sahkvarh_import = BigDecimal.ZERO;
        dpm.kddayamax_wbp = params.kdDayaMaxWbp;
        dpm.kvamaks = params.dayaKvaMax;
        dpm.uploadby = params.transaksi_by;
        dpm.ptgentrimeter = params.transaksi_by;
    }
    public static void mapToEntryStand(Dpm dpm, EntryStandParam params) {
        dpm.thblrek = params.blth;
        dpm.idpel = params.idpel;
        // dpm.unitup = params.unitup;
        dpm.kodeposting = "3";
        // dpm.kd_pesan = params.kdBaca;
        dpm.tglbaca = params.tglBaca;
        dpm.sahwbp = params.wbp;
        dpm.sahlwbp = params.lwbp;
        dpm.sahkvarh = params.kvarh;
        // if (params.kdDayaKvaMax != null && params.kdDayaKvaMax.equals("K")) {
        //     dpm.dayamax = params.dayaMax.multiply(BigDecimal.valueOf(1000));
        // } else {
        //     dpm.dayamax = params.dayaMax;
        // }
        dpm.dayamax_wbp = params.dayaMaxWbp;
        // dpm.sahwbp_import = params.wbp_import;
        // dpm.sahlwbp_import = params.lwbp_import;
        // dpm.sahkvarh_import = params.kvarh_import;
        // dpm.kddayamax_wbp = params.kdDayaMaxWbp;
        dpm.kvamaks = params.dayaKvaMax;
        dpm.uploadby = params.transaksi_by;
        dpm.ptgentrimeter = params.transaksi_by;
    }

    public static LogDPMKoreksi mapToLogDpm(LogDPMKoreksi logDpm, MeterParam params) {

        // logDpm.id = params.idpel + params.blth;
        logDpm.id = UUID.randomUUID().toString();
        logDpm.thblrek = params.blth;
        logDpm.idpel = params.idpel;
        logDpm.unitupi = params.unitupi;
        logDpm.unitap = params.unitap;
        logDpm.unitup = params.unitup;
        logDpm.kodeposting = "3";
        logDpm.kd_pesan = params.kdBaca;
        logDpm.tglbaca = params.tglBaca;
        logDpm.sahwbp = params.wbp;
        logDpm.sahlwbp = params.lwbp;
        logDpm.sahkvarh = params.kvarh;
        if (params.kdDayaKvaMax != null && params.kdDayaKvaMax.equals("K")) {
            logDpm.dayamax = params.dayaMax.multiply(BigDecimal.valueOf(1000));
        } else {
            logDpm.dayamax = params.dayaMax;
        }
        logDpm.dayamax_wbp = params.dayaMaxWbp;
        logDpm.sahwbp_import = params.wbp_import;
        logDpm.sahlwbp_import = params.lwbp_import;
        logDpm.sahkvarh_import = params.kvarh_import;
        logDpm.kddayamax_wbp = params.kdDayaMaxWbp;
        logDpm.kvamaks = params.dayaKvaMax;
        logDpm.uploadby = params.transaksi_by;
        logDpm.ptgentrimeter = params.transaksi_by;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        logDpm.log_tgl_dpm = LocalDateTime.now().format(formatter);
        logDpm.ptg_log_dpm = params.transaksi_by;
        return logDpm;
    }
    public static LogDPMKoreksi mapToLogDpmEntry(LogDPMKoreksi logDpm, EntryStandParam params) {

        // logDpm.id = params.idpel + params.blth;
        logDpm.id = UUID.randomUUID().toString();
        logDpm.thblrek = params.blth;
        logDpm.idpel = params.idpel;
        // logDpm.unitup = params.unitup;
        logDpm.kodeposting = "3";
        // logDpm.kd_pesan = params.kdBaca;
        logDpm.tglbaca = params.tglBaca;
        logDpm.sahwbp = params.wbp;
        logDpm.sahlwbp = params.lwbp;
        logDpm.sahkvarh = params.kvarh;
        // if (params.kdDayaKvaMax != null && params.kdDayaKvaMax.equals("K")) {
        //     logDpm.dayamax = params.dayaMax.multiply(BigDecimal.valueOf(1000));
        // } else {
        //     logDpm.dayamax = params.dayaMax;
        // }
        logDpm.dayamax_wbp = params.dayaMaxWbp;
        // logDpm.sahwbp_import = params.wbp_import;
        // logDpm.sahlwbp_import = params.lwbp_import;
        // logDpm.sahkvarh_import = params.kvarh_import;
        // logDpm.kddayamax_wbp = params.kdDayaMaxWbp;
        logDpm.kvamaks = params.dayaKvaMax;
        logDpm.uploadby = params.transaksi_by;
        logDpm.ptgentrimeter = params.transaksi_by;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        logDpm.log_tgl_dpm = LocalDateTime.now().format(formatter);
        logDpm.ptg_log_dpm = params.transaksi_by;
        return logDpm;
    }

    public static LogDataMeterAcmt mapToLogDataMeterAcmt(MeterParam params) {
        LogDataMeterAcmt logAcmt = new LogDataMeterAcmt();
        logAcmt.id = UUID.randomUUID().toString();
        logAcmt.idtransaksi = params.idtransaksi;
        logAcmt.unitup = params.unitup;
        logAcmt.idpel = params.idpel;
        logAcmt.blth = params.blth;
        logAcmt.kdStan = params.kdStan;
        logAcmt.kdBaca = params.kdBaca;
        logAcmt.tglBaca = String.valueOf(params.tglBaca);
        logAcmt.lwbp = params.lwbp;
        logAcmt.lwbp2 = params.lwbp2;
        logAcmt.lwbp_import = params.lwbp_import;
        logAcmt.wbp = params.wbp;
        logAcmt.wbp_import = params.wbp_import;
        logAcmt.kvarh = params.kvarh;
        logAcmt.kvarh_import = params.kvarh_import;
        logAcmt.transaksi_by = params.transaksi_by;
        logAcmt.dayaMax = params.dayaMax;
        logAcmt.dayaMaxWbp = params.dayaMaxWbp;
        logAcmt.kdDayaMaxWbp = params.kdDayaMaxWbp;
        logAcmt.dayaKvaMax = params.dayaKvaMax;
        logAcmt.kdDayaKvaMax = params.kdDayaKvaMax;

        return logAcmt;
    }

    public static Dpm mapToDpm(Dpm dpm, Dpm oldDpm, StandMeterDil dil, String thblrek, String userId) {
        dpm.id = dil.idpel+thblrek;
        dpm.idpel = dil.idpel;
        dpm.thblrek = thblrek;
        dpm.unitupi = dil.unitupi;
        dpm.unitap = dil.unitap;
        dpm.unitup = dil.unitup;
        dpm.kdkendala = null;
        dpm.koderbm = dil.kddk.substring(0,7);
        dpm.kdkondisimeter = null;
        dpm.kddk = dil.kddk;
        dpm.nopel = dil.nopel;
        dpm.nama = dil.nama;
        dpm.namapnj = dil.namaPnj;
        dpm.nourutrbm = dil.kddk.substring(7,12);
        dpm.areabm = dil.kddk.substring(0,2);
        dpm.bedarute = dil.kddk.substring(2,4);
        dpm.sahlwbp = null;
        dpm.lwbppakai = null;
        dpm.slawbp = null;
        dpm.sahwbp = null;
        dpm.wbppakai = null;
        dpm.sahkvarh = null;
        dpm.kvarhpakai = null;
        dpm.dlpd = BigDecimal.ZERO;
        dpm.fkmkwh = dil.fakmkwh;
        dpm.fkmkvarh = dil.fakmkvarh;
        dpm.fkmkvam = dil.fakmkvam;
        dpm.kvamax_wbp = BigDecimal.ZERO;
        dpm.kvamaks = BigDecimal.ZERO;
        dpm.kvampakai = BigDecimal.ZERO;
        dpm.ptgentrimeter = null;
        dpm.tglentri = null;
        dpm.tglupload = LocalDate.now();
        dpm.uploadby = userId;
        dpm.tglhitung = null;
        dpm.hitungby = null;
        dpm.tglkirimf3 = null;
        dpm.kirimby = null;
        dpm.kdkelompok = dil.kdKlp;
        dpm.tarif = dil.tarif;
        if (dil.kDaya != null && dil.kDaya.equals("K")) {
            dpm.daya = dil.daya.multiply(BigDecimal.valueOf(1000));
        } else {
            dpm.daya = dil.daya;
        }
        dpm.tglpengesahan = null;
        dpm.sah_by = null;
        dpm.tg = dil.tg;
        dpm.kdbacameter = dil.kdBacaMeter;
        dpm.letakapp = dil.letaApp;
        dpm.fjn = dil.fjn;
        dpm.frt = dil.frt;
        dpm.kdppj = dil.kdPpj;
        dpm.kdsewakap = dil.kdSewaKap;
        dpm.kdbpt = dil.kdbpt;
        dpm.nobang = dil.nobang;
        dpm.ketnobang = dil.ketNobang;
        dpm.rt = dil.rt;
        dpm.rw = dil.rw;
        dpm.pnj = dil.pnj;
        dpm.kdpt = dil.kdpt;
        dpm.kdpt_2 = dil.kdpt2;
        dpm.kdam = dil.kdam;
        dpm.kdproses = dil.kdProses;
        dpm.jenis_mk = dil.jenisMk;
        dpm.thblmut = dil.thblmut;
        dpm.tglperubahan = LocalDate.parse(dil.tglRubahMk, DateTimeFormatter.ISO_DATE_TIME);
        dpm.tarif_lm = dil.tarifLm;
        dpm.kdpt_2_lm = dil.kdpt2Lm;
        dpm.frt_lm = dil.frtLm;
        dpm.fkmkwh_lm = dil.fkmKwhLm;
        dpm.tg_lm = dil.tgLm;
        dpm.slalwbp1_ins = oldDpm.sahlwbp1_ins;
        dpm.slalwbp2_ins = oldDpm.sahlwbp2_ins;

        dpm.slalwbp1 = oldDpm.slalwbp;
        dpm.slalwbp2 = oldDpm.slalwbp1;
        dpm.slalwbp3 = oldDpm.slalwbp2;
        dpm.slalwbp4 = oldDpm.slalwbp3;
        dpm.slalwbp5 = oldDpm.slalwbp4;
        dpm.slalwbp6 = oldDpm.slalwbp5;
        dpm.slalwbp7 = oldDpm.slalwbp6;
        dpm.slalwbp8 = oldDpm.slalwbp7;
        dpm.slalwbp9 = oldDpm.slalwbp8;
        dpm.slalwbp10 = oldDpm.slalwbp9;
        dpm.slalwbp11 = oldDpm.slalwbp10;

        dpm.slawbp1 = oldDpm.slawbp;
        dpm.slawbp2 = oldDpm.slawbp1;
        dpm.slawbp3 = oldDpm.slawbp2;
        dpm.slawbp4 = oldDpm.slawbp3;
        dpm.slawbp5 = oldDpm.slawbp4;
        dpm.slawbp6 = oldDpm.slawbp5;
        dpm.slawbp7 = oldDpm.slawbp6;
        dpm.slawbp8 = oldDpm.slawbp7;
        dpm.slawbp9 = oldDpm.slawbp8;
        dpm.slawbp10 = oldDpm.slawbp9;
        dpm.slawbp11 = oldDpm.slawbp10;

        dpm.slakvarh1 = oldDpm.slakvarh;
        dpm.slakvarh2 = oldDpm.slakvarh1;
        dpm.slakvarh3 = oldDpm.slakvarh2;
        dpm.slakvarh4 = oldDpm.slakvarh3;
        dpm.slakvarh5 = oldDpm.slakvarh4;
        dpm.slakvarh6 = oldDpm.slakvarh5;
        dpm.slakvarh7 = oldDpm.slakvarh6;
        dpm.slakvarh8 = oldDpm.slakvarh7;
        dpm.slakvarh9 = oldDpm.slakvarh8;
        dpm.slakvarh10 = oldDpm.slakvarh9;
        dpm.slakvarh11 = oldDpm.slakvarh10;

        dpm.tglbaca2=oldDpm.tglbaca1;
        dpm.tglbaca3=oldDpm.tglbaca2;
        dpm.tglbaca4=oldDpm.tglbaca3;
        dpm.tglbaca5=oldDpm.tglbaca4;
        dpm.tglbaca6=oldDpm.tglbaca5;
        dpm.tglbaca7=oldDpm.tglbaca6;
        dpm.tglbaca8=oldDpm.tglbaca7;
        dpm.tglbaca9=oldDpm.tglbaca8;
        dpm.tglbaca10=oldDpm.tglbaca9;
        dpm.tglbaca11=oldDpm.tglbaca10;

        dpm.pemkwh1 = ((BigDecimal) Helper.getOrDefault(oldDpm.lwbppakai, BigDecimal.ZERO)).add((BigDecimal) Helper.getOrDefault(oldDpm.wbppakai, BigDecimal.ZERO));
        dpm.pemkwh2 = oldDpm.pemkwh1;
        dpm.pemkwh3 = oldDpm.pemkwh2;
        dpm.pemkwh4 = oldDpm.pemkwh3;
        dpm.pemkwh5 = oldDpm.pemkwh4;
        dpm.pemkwh6 = oldDpm.pemkwh5;
        dpm.pemkwh7 = oldDpm.pemkwh6;
        dpm.pemkwh8 = oldDpm.pemkwh7;
        dpm.pemkwh9 = oldDpm.pemkwh8;
        dpm.pemkwh10 = oldDpm.pemkwh9;
        dpm.pemkwh11 = oldDpm.pemkwh10;

        if (dil.kDayaLm != null && dil.kDayaLm.equals("K")) {
            dpm.daya_lm = dil.dayaLm.multiply(BigDecimal.valueOf(1000));
        } else {
            dpm.daya_lm = dil.dayaLm;
        }

        if (dil.kdam != null && dil.kdam.equals("A")) {
            dpm.kodeposting = "1";
        } else {
            dpm.kodeposting = "0";
        }

        if (Objects.equals(thblrek, dil.thblmut) && dil.jenisMk.equals("A")) {
            dpm.slalwbp = dil.kddkAwalLwbp;
            dpm.tglbaca1 = LocalDate.parse(dil.tglNyalaPb);
            dpm.slakvarh = dil.kddkAwalKvarh;
            dpm.slawbp = dil.kddkAwalWbp;
        } else {
            dpm.tglbaca1 = oldDpm.tglbaca;
            dpm.slalwbp = oldDpm.sahlwbp;
            dpm.slakvarh = oldDpm.sahkvarh;
            dpm.slawbp = oldDpm.sahwbp;
        }

        log.info("mapped new dpm : {}", dpm.id);

        return dpm;
    }

    public static Dpm toEntity(Dpm dpm, CrmDpmParam req) {
        dpm.tarif = req.getTarif();
        dpm.unitup = req.getUnitup();
        dpm.koderbm = req.getKoderbm();
        dpm.kdkendala = req.getKdkendala();
        dpm.kdkondisimeter = req.getKdkondisimeter();
        dpm.unitupi = req.getUnitupi();
        dpm.unitap = req.getUnitap();
        dpm.daya = req.getDaya();
        dpm.kdpt = req.getKdpt();
        dpm.kdpt_2 = req.getKdpt2();
        dpm.tg = req.getTg();
        dpm.kddk = req.getKddk();
//        dpm.koreksiKe;
        dpm.nopel = req.getNopel();
        dpm.nama = req.getNama();
        dpm.namapnj = req.getNamapnj();
        dpm.nourutrbm = req.getNourutrbm();
        dpm.areabm = req.getAreabm();
        dpm.bedarute = req.getBedarute();
        dpm.dlpd = req.getDlpd();
        dpm.fkmkwh = req.getFkmkwh();
        dpm.fkmkvarh = req.getFkmkvarh();
        dpm.fkmkvam = req.getFkmkvam();
        dpm.kvamax_wbp = req.getKvamaxWbp();
        dpm.kvamaks = req.getKvamaks();
        dpm.kvampakai = req.getKvampakai();
        dpm.kodeposting = req.getKodeposting();
        dpm.ptgentrimeter = req.getPtgentrimeter();
        dpm.tglentri = req.getTglentri();
        dpm.uploadby = req.getUploadby();
        dpm.tglupload = req.getTglupload();
        dpm.kdkelompok = req.getKdkelompok();
        dpm.tglbaca = req.getTglbaca();
        dpm.tglbaca1 = req.getTglbaca1();
        dpm.slalwbp = req.getSlalwbp();
        dpm.sahlwbp = req.getSahlwbp();
        dpm.slawbp = req.getSlawbp();
        dpm.sahwbp = req.getSahwbp();
        dpm.slakvarh = req.getSlakvarh();
        dpm.sahkvarh = req.getSahkvarh();
        dpm.dayamax = req.getDayamax();
        dpm.kddayamax = req.getKddayamax();
        dpm.kd_pesan = req.getKdPesan();
        dpm.dayamax_wbp = req.getDayamaxWbp();
        dpm.kddayamax_wbp = req.getKddayamaxWbp();
        dpm.st_prm = req.getStPrm();
        dpm.kdbacameter = req.getKdbacameter();
        dpm.letakapp = req.getLetakapp();
        dpm.fjn = req.getFjn();
        dpm.frt = req.getFrt();
        dpm.kdppj = req.getKdppj();
        dpm.kdsewakap = req.getKdsewakap();
        dpm.kdbpt = req.getKdbpt();
        dpm.nobang = req.getNobang();
        dpm.ketnobang = req.getKetnobang();
        dpm.rt = req.getRt();
        dpm.rw = req.getRw();
        dpm.pnj = req.getPnj();
        dpm.id_prosesdlpd = req.getIdProsesdlpd();
        dpm.kdam = req.getKdam();
        dpm.kdproses = req.getKdproses();
        dpm.jenis_mk = req.getJenisMk();
        dpm.thblmut = req.getThblmut();
        dpm.tglperubahan = req.getTglperubahan();
        dpm.msg = req.getMsg();
        dpm.jamnyala = req.getJamnyala();
        dpm.tarif_lm = req.getTarifLm();
        dpm.kdpt_lm = req.getKdptLm();
        dpm.daya_lm = req.getDayaLm();
        dpm.kdpt_2_lm = req.getKdpt2Lm();
        dpm.frt_lm = req.getFrtLm();
        dpm.fkmkwh_lm = req.getFkmkwhLm();
        dpm.fkmkvarh_lm = req.getFkmkvarhLm();
        dpm.tg_lm = req.getTgLm();
        dpm.sahlwbp1_ins = req.getSahlwbp1Ins();
        dpm.sahlwbp2_ins = req.getSahlwbp2Ins();
        dpm.slalwbp1_ins = req.getSlalwbp1Ins();
        dpm.slalwbp2_ins = req.getSlalwbp2Ins();
        dpm.sahlwbp1_ins_cabut = req.getSahlwbp1InsCabut();
        dpm.sahlwbp2_ins_cabut = req.getSahlwbp2InsCabut();
        dpm.slalwbp1_ins_pasang = req.getSlalwbp1InsPasang();
        dpm.slalwbp2_ins_pasang = req.getSlalwbp2InsPasang();
        dpm.sahlwbp_import = req.getSahlwbpImport();
        dpm.sahwbp_import = req.getSahwbpImport();
        dpm.sahkvarh_import = req.getSahkvarhImport();
//        dpm.flagDpmBaru;
        return dpm;
    }
}
