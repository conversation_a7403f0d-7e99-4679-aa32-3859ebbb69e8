package com.iconplus.ap2t.generator.mapper;


import com.iconplus.ap2t.common.param.billing.DpmPecahanParam;
import com.iconplus.ap2t.data.entity.billing.DpmPecahan;

public class DpmPecahanMapper {
    public static DpmPecahan mappToEntity(DpmPecahanParam req) {
        DpmPecahan pecahan = new DpmPecahan();
        pecahan.id = req.getIdpel() + req.getThblrek() + req.getPecahKe();
        pecahan.idpel = req.getIdpel();
        pecahan.thblrek = req.getThblrek();
        pecahan.pecahKe = req.getPecahKe();
        pecahan.jnsmut = req.getJnsmut();
        pecahan.thblmut = req.getThblmut();
        pecahan.kdmut = req.getKdmut();
        pecahan.tglrubah = req.getTglrubah();
        pecahan.tarif = req.getTarif();
        pecahan.kdpt = req.getKdpt();
        pecahan.kdpt2 = req.getKdpt2();
        pecahan.daya = req.getDaya();
        pecahan.dayar312 = req.getDayar312();
        pecahan.kogol = req.getKogol();
        pecahan.subkogol = req.getSubkogol();
        pecahan.faknpremium = req.getFaknpremium();
        pecahan.kdam = req.getKdam();
        pecahan.frt = req.getFrt();
        pecahan.fjn = req.getFjn();
        pecahan.kdind = req.getKdind();
        pecahan.maxDemand = req.getMaxDemand();
        pecahan.kdkvamaks = req.getKdkvamaks();
        pecahan.sahkvamaxWbp = req.getSahkvamaxWbp();
        pecahan.dayamaxWbp = req.getDayamaxWbp();
        pecahan.fakm = req.getFakm();
        pecahan.fakmkvarh = req.getFakmkvarh();
        pecahan.fakmkvam = req.getFakmkvam();
        pecahan.kdpembmeter = req.getKdpembmeter();
        pecahan.dayajbst = req.getDayajbst();
        pecahan.kdbedajbst = req.getKdbedajbst();
        pecahan.slalwbpPasang = req.getSlalwbpPasang();
        pecahan.sahlwbpCabut = req.getSahlwbpCabut();
        pecahan.slawbpPasang = req.getSlawbpPasang();
        pecahan.sahwbpCabut = req.getSahwbpCabut();
        pecahan.slakvarhPasang = req.getSlakvarhPasang();
        pecahan.sahkvarhCabut = req.getSahkvarhCabut();
        pecahan.sahkvamaksCabut = req.getSahkvamaksCabut();
        return pecahan;
    }
}
