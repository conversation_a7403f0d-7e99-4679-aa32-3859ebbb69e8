package com.iconplus.ap2t.generator.mapper;

import com.iconplus.ap2t.data.entity.billing.BillKirimCargo;

import java.time.LocalDateTime;

public class BillCargoMapper {

    public static BillKirimCargo mapToEntity(
        BillKirimCargo billCargo, String batch, String unitap, String unitup,
        String kdProsesKlp, String userId, int recordSuccess, String thblrek
    ) {
        billCargo.noLog = Long.valueOf(batch);
        billCargo.unitap = unitap;
        billCargo.unitup = unitup;
        billCargo.kdProsesKlp = kdProsesKlp;
        billCargo.sahBy = userId;
        billCargo.tglCatat = LocalDateTime.now();
        billCargo.jmlTotal = (long) recordSuccess;
        billCargo.thblrek = thblrek;
        billCargo.statusDownload = 0;
        return billCargo;
    }
}
