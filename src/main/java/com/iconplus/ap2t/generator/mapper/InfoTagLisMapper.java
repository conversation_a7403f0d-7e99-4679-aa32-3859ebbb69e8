package com.iconplus.ap2t.generator.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.iconplus.ap2t.data.entity.view.VwInfoTaglis;
import com.iconplus.ap2t.generator.dto.InfoTagLisDTO;
import io.quarkus.logging.Log;

/**
 * <AUTHOR>
 */
public class InfoTagLisMapper {
    private static final ObjectMapper objectMapper = new ObjectMapper()
            .configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    // Method to convert entity to DTO
    public static InfoTagLisDTO toDto(VwInfoTaglis entity) {
        if (entity == null) {
            return null;
        }
        Log.info("entity mapp : "+entity);
        return objectMapper.convertValue(entity, InfoTagLisDTO.class);
    }

    // Method to convert DTO to entity
    public static VwInfoTaglis toEntity(InfoTagLisDTO dto) {
        if (dto == null) {
            return null;
        }
        return objectMapper.convertValue(dto, VwInfoTaglis.class);
    }

    // Example method to convert entity to JSON (optional)
    public static String toJson(VwInfoTaglis entity) {
        try {
            return objectMapper.writeValueAsString(entity);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return null;
        }
    }

    // Example method to convert JSON to entity (optional)
    public static VwInfoTaglis fromJson(String json) {
        try {
            return objectMapper.readValue(json, VwInfoTaglis.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return null;
        }
    }
}
