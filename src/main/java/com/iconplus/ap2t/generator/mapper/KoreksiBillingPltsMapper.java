package com.iconplus.ap2t.generator.mapper;

import com.iconplus.ap2t.data.entity.billing.Billing;
import com.iconplus.ap2t.data.entity.billing.BillingEnergi;
import com.iconplus.ap2t.generator.dto.KoreksiBillingPltsDTO;
import com.iconplus.ap2t.generator.param.KoreksiBillingPltsParam;
import io.smallrye.mutiny.Uni;

import java.util.ArrayList;
import java.util.List;

public class KoreksiBillingPltsMapper {
    public static Uni<KoreksiBillingPltsDTO> mapFromEntity(Billing billing, BillingEnergi billingEnergi){
        KoreksiBillingPltsDTO respone = new KoreksiBillingPltsDTO();

        respone.idpel = billing.idpel;
        respone.bulanRekening = billing.thblrek;
        respone.tahun = billing.thblrek.substring(0,4);
        respone.nama = billing.nama;
        respone.alamat = billing.alamat;
        respone.unitup = billing.unitup;
        respone.tarif = billing.tarif;
        respone.daya = billing.daya;
        respone.dlpd = billing.dlpd;
        respone.tarifLama =billing.tarifLmPindahTrf;
        respone.dayaLama = billing.dayaLmPindahTrf;
        respone.frt = billing.frt;
        respone.blthMutasi = billing.thblmut;
        respone.jenisMK = billing.jnsmut;
        respone.tglPerubahan = billing.tglrubah;
        respone.tglBacaLalu = billing.tglbacalalu;
        respone.tglBacaAkhir = billing.tglbacaakhir;
        respone.lwbpLalu = billing.slalwbp;
        respone.lwbpAkhir = billing.sahlwbp;
        respone.kwhLwbp = billing.kwhlwbp;
        respone.wbpLalu = billing.slawbp;
        respone.wbpAkhir = billing.sahwbp;
        respone.kwhWpb = billing.kwhwbp;
        respone.kvarhLalu = billing.slakvarh;
        respone.kvarhAkhir = billing.sahkvarh;
        respone.kwhKvarh = billing.pemkvarh;
        respone.kvamaxAkhir = billing.sahkvamaks;
        respone.kwhKvamax = billing.dayamaks;
        respone.kvawbpAkhir = billing.sahkvamaxWbp;
        respone.kwhKvawbp = billing.dayamaxWbp;
        respone.jamNyala = billing.jamnyala;
        respone.dataBillEnergiPltsList = mapToDataBillEnergiPltsList(billingEnergi);
        respone.uraianNilaiPltsList = List.of(
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("KWHLWBP", billing.kwhlwbp),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("KWHWBP", billing.kwhwbp),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("BLOK3", billing.blok3),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("PEMKWH", billing.pemkwh),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("PEMKVARH", billing.pemkvarh),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("KELBKVARH", billing.kelbkvarh),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPLWBP", billing.rplwbp),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPWBP", billing.rpwbp),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPBLOK3", billing.rpblok3),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPKVARH", billing.rpkvarh),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPBEBAN", billing.rpbeban),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPPTL", billing.rpptl),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPTB", billing.rptb),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPPPN", billing.rpppn),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPBPJU", billing.rpbpju),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPBPTRAFO", billing.rpbptrafo),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPSEWATRAFO", billing.rpsewatrafo),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPSEWAKAP", billing.rpsewakap),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPANGSA", billing.rpangsa),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPANGSB", billing.rpangsb),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPANGSC", billing.rpangsc),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPMAT", billing.rpmat),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPPLN", billing.rppln),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPTAG", billing.rptag),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPPRODUKSI", billing.rpproduksi),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPSUBSIDI", billing.rpsubsidi),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPTDLLAMA", billing.rptdllama),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPTDLBARU", billing.rptdlbaru),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPSELSILIH", billing.rpselisih),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPREDUKSI", billing.rpreduksi),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPBK1", billing.rpbk1),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPBK2", billing.rpbk2),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPBK3", billing.rpbk3),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPREK_BRUTO", billing.rprekBruto),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPREK_NETTO", billing.rprekNetto),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPTAG_BRUTO", billing.rptagBruto),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPTAG_NETTO", billing.rptagNetto),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPJBST", billing.rpjbst),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPKOMPENSASI", billing.rpkompensasi),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPDISKON_PREMIUM", billing.rpdiskonPremium),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPDISKON", billing.rpdiskon),
                new KoreksiBillingPltsDTO.UraianNilaiPltsDTO("RPINVOICE", billing.rpinvoice)
        );
        return Uni.createFrom().item(respone);
    }

    public static List<KoreksiBillingPltsDTO.DataBillEnergiPltsDTO> mapToDataBillEnergiPltsList(BillingEnergi billingEnergi) {
        List<KoreksiBillingPltsDTO.DataBillEnergiPltsDTO> dataBillLEnergiList = new ArrayList<>();

        KoreksiBillingPltsDTO.DataBillEnergiPltsDTO dataBill = new KoreksiBillingPltsDTO.DataBillEnergiPltsDTO(
                billingEnergi.id,
                billingEnergi.pecahKe,
                billingEnergi.jenisEnergi,
                billingEnergi.slalwbp,   // lwbpLalu
                billingEnergi.sahlwbp,   // lwbpAkhir
                billingEnergi.slawbp,    // wbpLalu
                billingEnergi.sahwbp,    // wbpAkhir
                billingEnergi.slakvarh,  // kvarhLalu
                billingEnergi.sahkvarh,  // kvarhAkhir
                billingEnergi.sahkvamaks,// kvamaxAkhir
                billingEnergi.sahkvamaxWbp,// kvawbpAkhir
                billingEnergi.tglrubah,
                billingEnergi.slalwbpExport,
                billingEnergi.slawbpExport,
                billingEnergi.slakvarhExport,
                billingEnergi.sahlwbpExport,
                billingEnergi.sahwbpExport,
                billingEnergi.sahkvarhExport);

        dataBillLEnergiList.add(dataBill);

        return dataBillLEnergiList;
    }


    public static void mapUpdateToBilling(KoreksiBillingPltsParam koreksiBillingPltsParam, BillingEnergi billingEnergi) {

        billingEnergi.id = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().id;
        billingEnergi.pecahKe = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().pecahKe;
        // billingEnergi.jenisEnergi = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().jenisEnergi;
        billingEnergi.slalwbp = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().lwbpLalu;
        billingEnergi.slawbp = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().wbpLalu;
        billingEnergi.slakvarh = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().kvarhLalu;
        billingEnergi.sahlwbp = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().lwbpAkhir;
        billingEnergi.sahwbp = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().wbpAkhir;
        billingEnergi.sahkvarh = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().kvarhAkhir;
        billingEnergi.sahkvamaks = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().kvamaxAkhir;
        billingEnergi.sahkvamaxWbp = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().kvawbpAkhir;
        if (billingEnergi.tglrubah != null) {
            billingEnergi.tglrubah =koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().tglPerubahan;
        }

        billingEnergi.slalwbpExport = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().slalwbpExport;
        billingEnergi.slawbpExport = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().slawbpExport;
        billingEnergi.slakvarhExport = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().slakvarhExport;
        billingEnergi.sahlwbpExport = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().sahlwbpExport;
        billingEnergi.sahwbpExport = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().sahwbpExport;
        billingEnergi.sahkvarhExport = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().sahkvarhExport;

        // billingEnergi.slalwbp = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().lwbpLalu;
        // billingEnergi.slawbp = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().wbpLalu;
        // billingEnergi.slakvarh = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().kvarhLalu;
        // billingEnergi.sahlwbp = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().lwbpAkhir;
        // billingEnergi.sahwbp = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().wbpAkhir;
        // billingEnergi.sahkvarh = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().kvarhAkhir;
        // billingEnergi.sahkvamaks = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().kvamaxAkhir;
        // billingEnergi.sahkvamaxWbp = koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().kvawbpAkhir;
        // if (billingEnergi.tglrubah != null) {
        //     billingEnergi.tglrubah =koreksiBillingPltsParam.dataBillEnergiPltsList.getFirst().tglPerubahan;
        // }
    }
    public static BillingEnergi mapUpdateToBilling2(KoreksiBillingPltsDTO.DataBillEnergiPltsDTO koreksi,BillingEnergi billingEnergi) {

        billingEnergi.id = koreksi.id;
        billingEnergi.pecahKe = koreksi.pecahKe;
        billingEnergi.jenisEnergi = koreksi.jenisEnergi;
          billingEnergi.sahlwbp = koreksi.lwbpAkhir;
        billingEnergi.sahwbp = koreksi.wbpAkhir;
        billingEnergi.sahkvarh = koreksi.kvarhAkhir;
        billingEnergi.slalwbp = koreksi.lwbpLalu;
        billingEnergi.slawbp = koreksi.wbpLalu;
        billingEnergi.slakvarh = koreksi.kvarhLalu;
       billingEnergi.sahkvamaks = koreksi.kvamaxAkhir;
       billingEnergi.sahkvamaxWbp = koreksi.kvawbpAkhir;
       if (billingEnergi.tglrubah != null) {
           billingEnergi.tglrubah =koreksi.tglPerubahan;
       }

        billingEnergi.slalwbpExport = koreksi.slalwbpExport;
        billingEnergi.slawbpExport = koreksi.slawbpExport;
        billingEnergi.slakvarhExport = koreksi.slakvarhExport;
        billingEnergi.sahlwbpExport = koreksi.sahlwbpExport;
        billingEnergi.sahwbpExport = koreksi.sahwbpExport;
        billingEnergi.sahkvarhExport = koreksi.sahkvarhExport;
        // billingEnergi.sahlwbp = koreksi.lwbpAkhir;
        // billingEnergi.sahwbp = koreksi.wbpAkhir;
        // billingEnergi.sahkvarh = koreksi.kvarhAkhir;
        // billingEnergi.slalwbp = koreksi.lwbpLalu;
        // billingEnergi.slawbp = koreksi.wbpLalu;
        // billingEnergi.slakvarh = koreksi.kvarhLalu;
//        billingEnergi.sahkvamaks = koreksi.kvamaxAkhir;
//        billingEnergi.sahkvamaxWbp = koreksi.kvawbpAkhir;
//        if (billingEnergi.tglrubah != null) {
//            billingEnergi.tglrubah =koreksi.tglPerubahan;
//        }

        return billingEnergi;
    }
}
