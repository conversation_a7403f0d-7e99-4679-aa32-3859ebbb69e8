package com.iconplus.ap2t.generator.mapper;

import cn.hutool.core.bean.BeanUtil;
import com.iconplus.ap2t.common.param.InisialisasiBillingDTO;
import com.iconplus.ap2t.common.param.billing.BillingEnergiParam;
import com.iconplus.ap2t.common.param.billing.Output;
import com.iconplus.ap2t.common.param.billing.OutputEnergiParam;
import com.iconplus.ap2t.common.utility.Helper;
import com.iconplus.ap2t.data.entity.billing.BillingEnergi;
import com.iconplus.ap2t.data.entity.billing.BillingEnergi720JN;
import com.iconplus.ap2t.data.entity.billing.Dpm;
import com.iconplus.ap2t.data.entity.billing.DpmPecahan;
import com.iconplus.ap2t.data.entity.log.LogBillingEnergi;
import com.iconplus.ap2t.generator.dto.BillingEnergiBulanLaluDTO;
import com.iconplus.ap2t.generator.response.SuplaiDILDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class BillingEnergiMapper {

    private static final DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final Logger log = LoggerFactory.getLogger(BillingEnergiMapper.class);

    public static BillingEnergi mapToBilling(BillingEnergi bill, SuplaiDILDTO dil, Dpm dpm, boolean newEntity, BillingEnergiBulanLaluDTO data) {
        if (newEntity) {
            bill.id = dpm.idpel + dpm.thblrek + 0;
        }
        bill.billId = dpm.idpel + dpm.thblrek;
        bill.idpel = dpm.idpel;
        bill.thblrek = dpm.thblrek;
        bill.jenisEnergi = "NORMAL";
        bill.pecahKe = 0;
        bill.jnsmut = dil.jnsmut;
        bill.thblmut = dil.thblmut;
        bill.kdmut = dil.kdmut;
        if (dil.tglrubah != null) {
            bill.tglrubah = LocalDate.parse(dil.tglrubah);
        }
        bill.tarif = dil.tarif;
        bill.kdpt = dil.kdpt;
        bill.kdpt2 = dil.kdpt2;
        bill.daya = dil.daya.longValue();
        bill.faknpremium = dil.faknpremium;
        bill.dayajbst = dil.dayajbst;
        bill.kdbedajbst = dil.kdbedajbst;
        bill.kogol = dil.kogol;
        bill.subkogol = dil.subkogol;
        bill.kdind = dil.kdind;
        bill.kdam = dil.kdam;
        bill.kdkvamaks = dil.kdkvamaks;
        bill.maxDemand = dil.maxDemand;
        bill.frt = dil.frt;
        bill.fjn = dil.fjn;
        bill.kdpembmeter = dil.kdpembmeter;
        bill.fakm = dil.fakm;
        bill.fakmkvarh = dil.fakmkvarh;
        bill.fakmkvam = dil.fakmkvam;
        bill.sahlwbp = dpm.sahlwbp;
        bill.sahwbp = dpm.sahwbp;
        bill.sahkvarh = dpm.sahkvarh;
        bill.slalwbp = dpm.slalwbp;
        bill.slawbp = dpm.slawbp;
        bill.slakvarh = dpm.slakvarh;
        bill.kdkvamaks = dpm.kddayamax;

        BigDecimal resultDayaMaxWbp = BigDecimal.ZERO;
        if (dpm.kddayamax != null && dpm.kddayamax.equals("K")) {
            resultDayaMaxWbp = dpm.dayamax_wbp.multiply(BigDecimal.valueOf(1000));
        } else {
            resultDayaMaxWbp = dpm.dayamax_wbp;
        }
        if (resultDayaMaxWbp != null) {
            resultDayaMaxWbp = resultDayaMaxWbp.multiply(dpm.fkmkwh);
            bill.dayamaxWbp = resultDayaMaxWbp;
        }

        BigDecimal resultDayaMax = BigDecimal.ZERO;
        if (dpm.kddayamax != null && dpm.kddayamax.equals("K")) {
            resultDayaMax = dpm.dayamax.multiply(BigDecimal.valueOf(1000));
        } else {
            resultDayaMax = dpm.dayamax;
        }
        if (resultDayaMax != null) {
            resultDayaMax = resultDayaMax.multiply(dpm.fkmkwh != null ? dpm.fkmkwh : BigDecimal.ZERO);
            bill.dayamaks = resultDayaMax;
        }

        bill.tglbacalalu = dpm.tglbaca1.format(formatter1);
        bill.tglbacaakhir = dpm.tglbaca.format(formatter1);
        bill.slalwbp = dpm.slalwbp;
        bill.sahlwbp = dpm.sahlwbp;
        bill.slawbp = dpm.slawbp;
        bill.sahwbp = dpm.sahwbp;
        bill.slakvarh = dpm.slakvarh;
        bill.sahkvarh = dpm.sahkvarh;
        if (dpm.sahlwbp_import != null) {
            bill.sahlwbpExport = dpm.sahlwbp_import;
        } else {
            bill.sahlwbpExport = BigDecimal.ZERO;
        }
        if (dpm.sahwbp_import != null) {
            bill.sahwbpExport = dpm.sahwbp_import;
        } else {
            bill.sahwbpExport = BigDecimal.ZERO;
        }
        if (dpm.sahkvarh_import != null) {
            bill.sahkvarhExport = dpm.sahkvarh_import;
        } else {
            bill.sahkvarhExport = BigDecimal.ZERO;
        }

        // SLA =stand lalu
        if (data.slawbp_import != null) {
            bill.slawbpExport = data.slawbp_import;
        }
        if (data.slalwbp_import != null) {
            bill.slalwbpExport = data.slalwbp_import;
        }
        if (data.slakvarh_import != null) {
            bill.slakvarhExport = data.slakvarh_import;
        }

        bill.slakvarhPasang = dpm.slakvarh_pasang;
        bill.sahkvarhCabut = dpm.sahkvarh_cabut;
        bill.slalwbpPasang = dpm.slalwbp_pasang;
        bill.sahlwbpCabut = dpm.sahlwbp_cabut;
        bill.slawbpPasang = dpm.slawbp_pasang;
        bill.sahwbpCabut = dpm.sahwbp_cabut;
        return bill;
    }

    public static BillingEnergi mapToBillingEnergiPecahan(BillingEnergi bill, SuplaiDILDTO dil, DpmPecahan dpmPecahan, List<DpmPecahan> lstDpmPecahan, Dpm dpm, boolean is_pecahan) {
        if (!is_pecahan) {
            bill.id = dpm.idpel + dpm.thblrek + 0;
            bill.billId = dpm.idpel + dpm.thblrek;
            bill.idpel = dpm.idpel;
            bill.thblrek = dpm.thblrek;
            bill.jenisEnergi = "NORMAL";
            bill.pecahKe = 0;
            bill.jnsmut = dil.jnsmut;
            bill.thblmut = dil.thblmut;
            bill.kdmut = dil.kdmut;
            if (dil.tglrubah != null) {
                bill.tglrubah = LocalDate.parse(dil.tglrubah);
            }
            bill.tarif = dil.tarif;
            bill.kdpt = dil.kdpt;
            bill.kdpt2 = dil.kdpt2;
            bill.daya = dil.daya.longValue();
            bill.faknpremium = dil.faknpremium;
            bill.dayajbst = dil.dayajbst;
            bill.kdbedajbst = dil.kdbedajbst;
            bill.kogol = dil.kogol;
            bill.subkogol = dil.subkogol;
            bill.kdind = dil.kdind;
            bill.kdam = dil.kdam;
            bill.kdkvamaks = dil.kdkvamaks;
            bill.maxDemand = dil.maxDemand;
            bill.frt = dil.frt;
            bill.fjn = dil.fjn;
            bill.kdpembmeter = dil.kdpembmeter;
            bill.fakm = dil.fakm;
            bill.fakmkvarh = dil.fakmkvarh;
            bill.fakmkvam = dil.fakmkvam;
            bill.kdkvamaks = dpm.kddayamax;

            BigDecimal resultDayaMaxWbp = BigDecimal.ZERO;
            if (dpm.kddayamax != null && dpm.kddayamax.equals("K")) {
                resultDayaMaxWbp = dpm.dayamax_wbp.multiply(BigDecimal.valueOf(1000));
            } else {
                resultDayaMaxWbp = dpm.dayamax_wbp;
            }
            if (resultDayaMaxWbp != null) {
                resultDayaMaxWbp = resultDayaMaxWbp.multiply(dpm.fkmkwh);
                bill.dayamaxWbp = resultDayaMaxWbp;
            }

            BigDecimal resultDayaMax = BigDecimal.ZERO;
            if (dpm.kddayamax != null && dpm.kddayamax.equals("K")) {
                resultDayaMax = dpm.dayamax.multiply(BigDecimal.valueOf(1000));
            } else {
                resultDayaMax = dpm.dayamax;
            }
            if (resultDayaMax != null) {
                resultDayaMax = resultDayaMax.multiply(dpm.fkmkwh);
                bill.dayamaks = resultDayaMax;
            }

            if (Helper.isMutationJorK(dil.thblmut, dpm.thblrek, dil.jnsmut)) {
                bill.slakvarhPasang = dpmPecahan.slakvarhPasang;
                bill.sahkvarhCabut = dpmPecahan.sahkvarhCabut;
                bill.slalwbpPasang = dpmPecahan.slalwbpPasang;
                bill.sahlwbpCabut = dpmPecahan.sahlwbpCabut;
                bill.slawbpPasang = dpmPecahan.slawbpPasang;
                bill.sahwbpCabut = dpmPecahan.sahwbpCabut;

                bill.sahlwbp = dpm.sahlwbp;
                bill.sahwbp = dpm.sahwbp;
                bill.sahkvarh = dpm.sahkvarh;
                bill.slalwbp = dpm.slalwbp;
                bill.slawbp = dpm.slawbp;
                bill.slakvarh = dpm.slakvarh;

                bill.fakmLm = dil.fakmLm;
                bill.fakmkvarhLm = dil.fakmkvarhLm;
                bill.frtLm = dil.frtLm;
            } else {
                bill.sahlwbp = dpm.sahlwbp;
                bill.sahwbp = dpm.sahwbp;
                bill.sahkvarh = dpm.sahkvarh;
                bill.slalwbp = dpmPecahan.slalwbpPasang;
                bill.slawbp = dpmPecahan.slawbpPasang;
                bill.slakvarh = dpmPecahan.slakvarhPasang;
            }
            bill.tglbacalalu = dpmPecahan.tglrubah.format(formatter1);
            bill.tglbacaakhir = dpm.tglbaca.format(formatter1);

        } else {
            bill.id = dpm.idpel + dpm.thblrek + dpmPecahan.pecahKe;
            bill.billId = dpm.idpel + dpm.thblrek;
            bill.idpel = dpm.idpel;
            bill.thblrek = dpm.thblrek;
            bill.jenisEnergi = "PECAHAN";
            bill.pecahKe = dpmPecahan.pecahKe;
            bill.jnsmut = dpmPecahan.jnsmut;
            bill.thblmut = dpmPecahan.thblmut;
            bill.kdmut = dpmPecahan.kdmut;
            bill.tglrubah = dpmPecahan.tglrubah;
            bill.tarif = dpmPecahan.tarif;
            bill.kdpt = dpmPecahan.kdpt;
            bill.kdpt2 = dpmPecahan.kdpt2;
            bill.daya = dpmPecahan.daya.longValue();
            bill.faknpremium = dpmPecahan.faknpremium;
            bill.dayajbst = dpmPecahan.dayajbst;
            bill.kdbedajbst = dpmPecahan.kdbedajbst;
            bill.kogol = dpmPecahan.kogol;
            bill.subkogol = dpmPecahan.subkogol;
            bill.kdind = dpmPecahan.kdind;
            bill.kdam = dpmPecahan.kdam;
            bill.kdkvamaks = dpmPecahan.kdkvamaks;
            bill.maxDemand = dpmPecahan.maxDemand;
            bill.frt = dpmPecahan.frt;
            bill.fjn = dpmPecahan.fjn;
            bill.kdpembmeter = dpmPecahan.kdpembmeter;
            bill.fakm = dpmPecahan.fakm;
            bill.fakmkvarh = dpmPecahan.fakmkvarh;
            bill.fakmkvam = dpmPecahan.fakmkvam;
            if (dpmPecahan.pecahKe > 1) {
//                if (Helper.isMutationJ(bill.thblrek, bill.thblmut, bill.jnsmut)) {
//                    bill.slakvarhPasang = dpmPecahan.slakvarhPasang;
//                    bill.sahkvarhCabut = dpmPecahan.sahkvarhCabut;
//                    bill.slalwbpPasang = dpmPecahan.slalwbpPasang;
//                    bill.sahlwbpCabut = dpmPecahan.sahlwbpCabut;
//                    bill.slawbpPasang = dpmPecahan.slawbpPasang;
//                    bill.sahwbpCabut = dpmPecahan.sahwbpCabut;
//
//                    bill.sahlwbp = dpm.sahlwbp;
//                    bill.sahwbp = dpm.sahwbp;
//                    bill.sahkvarh = dpm.sahkvarh;
//                    bill.slalwbp = dpm.slalwbp;
//                    bill.slawbp = dpm.slawbp;
//                    bill.slakvarh = dpm.slakvarh;
//                } else {
                    bill.sahlwbp = dpmPecahan.sahlwbpCabut;
                    bill.sahwbp = dpmPecahan.sahwbpCabut;
                    bill.sahkvarh = dpmPecahan.sahkvarhCabut;
                    bill.slalwbp = lstDpmPecahan.get(dpmPecahan.pecahKe - 1).slalwbpPasang;
                    bill.slawbp = lstDpmPecahan.get(dpmPecahan.pecahKe - 1).slawbpPasang;
                    bill.slakvarh = lstDpmPecahan.get(dpmPecahan.pecahKe - 1).slakvarhPasang;
                    bill.tglbacalalu = lstDpmPecahan.get(dpmPecahan.pecahKe - 1).tglrubah.format(formatter1);
                    bill.tglbacaakhir = dpmPecahan.tglrubah.format(formatter1);
//                }
            } else {
                bill.sahlwbp = dpmPecahan.sahlwbpCabut;
                bill.sahwbp = dpmPecahan.sahwbpCabut;
                bill.sahkvarh = dpmPecahan.sahkvarhCabut;
                bill.slalwbp = dpm.slalwbp;
                bill.slawbp = dpm.slawbp;
                bill.slakvarh = dpm.slakvarh;
                bill.tglbacalalu = dpm.tglbaca1.format(formatter1);
                bill.tglbacaakhir = dpmPecahan.tglrubah.format(formatter1);
            }
        }

//        bill.kdkvamaks = dpmPecahan.kddayamax;
//        BigDecimal resultDayaMaxWbp = BigDecimal.ZERO;
//        if (dpm.kddayamax != null && dpmPecahan.kddayamax.equals("K")) {
//            resultDayaMaxWbp = dpmPecahan.dayamax_wbp.multiply(BigDecimal.valueOf(1000));
//        } else {
//            resultDayaMaxWbp = dpmPecahan.dayamax_wbp;
//        }
//        resultDayaMaxWbp = resultDayaMaxWbp.multiply(BigDecimal.valueOf(1000));
//        resultDayaMaxWbp = resultDayaMaxWbp.multiply(dpm.fkmkwh);
//        bill.dayamaxWbp = resultDayaMaxWbp;

//        BigDecimal resultDayaMax = BigDecimal.ZERO;
//        if (dpm.kddayamax != null && dpm.kddayamax.equals("K")) {
//            resultDayaMax = dpmPecahan.dayamax.multiply(BigDecimal.valueOf(1000));
//        } else {
//            resultDayaMax = dpm.dayamax;
//        }
//        resultDayaMax = resultDayaMax.multiply(BigDecimal.valueOf(1000));
//        resultDayaMax = resultDayaMax.multiply(dpm.fkmkwh);
//        bill.dayamaks = resultDayaMax;

        return bill;
    }

    public static BillingEnergi mapFromDto(BillingEnergi energi, BillingEnergiParam param) {
        energi.fraksibeban = param.fraksibeban;
        energi.haribeban = param.haribeban;
        energi.haribebanBulan = param.haribebanBulan;
        energi.fraksikwh = param.fraksikwh;
        energi.fraksiemin = param.fraksiemin;
        energi.fraksihemat = param.fraksihemat;
        energi.fraksidmp = param.fraksidmp;
        energi.haripakai = param.haripakai;
        energi.haripakaiBulan = param.haripakaiBulan;
        energi.jamnyala350Batas = param.jamnyala350Batas;
        energi.kwhlwbpReal = param.kwhlwbpReal;
        energi.kwhwbpReal = param.kwhwbpReal;
        energi.kwhblok3Real = param.kwhblok3Real;
        energi.pemkwhReal = param.pemkwhReal;
        energi.kvarhpakaiReal = param.kvarhpakaiReal;
        energi.kvarhlebih = param.kvarhlebih;
        energi.cosphiReal = param.cosphiReal;
        energi.jamnyalaReal = param.jamnyalaReal;
        energi.kwhlwbpRegReal = param.kwhlwbpRegReal;
        energi.kwhwbpRegReal = param.kwhwbpRegReal;
        energi.pemkwhRegReal = param.pemkwhRegReal;
        energi.jamnyalaRegReal = param.jamnyalaRegReal;
        energi.kwhlwbpPremiumReal = param.kwhlwbpPremiumReal;
        energi.kwhwbpPremiumReal = param.kwhwbpPremiumReal;
        energi.pemkwhPremiumReal = param.pemkwhPremiumReal;
        energi.kwhlwbpBtobReal = param.kwhlwbpBtobReal;
        energi.kwhwbpBtobReal = param.kwhwbpBtobReal;
        energi.pemkwhBtobReal = param.pemkwhBtobReal;
        energi.kwhlwbpReg = param.kwhlwbpReg;
        energi.kwhwbpReg = param.kwhwbpReg;
        energi.kwhblok3Reg = param.kwhblok3Reg;
        energi.pemkwhReg = param.pemkwhReg;
        energi.kwhminEminReg = param.kwhminEminReg;
        energi.eminReg = param.eminReg;
        energi.rprekmin = param.rprekmin;
        energi.jnsbatasEminReg = param.jnsbatasEminReg;
        energi.kwhlwbpPremium = param.kwhlwbpPremium;
        energi.kwhwbpPremium = param.kwhwbpPremium;
        energi.pemkwhPremium = param.pemkwhPremium;
        energi.kwhminEminPremium = param.kwhminEminPremium;
        energi.eminPremium = param.eminPremium;
        energi.jnsbatasEminPremium = param.jnsbatasEminPremium;
        energi.kwhlwbpBtob = param.kwhlwbpBtob;
        energi.kwhwbpBtob = param.kwhwbpBtob;
        energi.pemkwhBtob = param.pemkwhBtob;
        energi.kwhminEminBtob = param.kwhminEminBtob;
        energi.eminBtob = param.eminBtob;
        energi.jnsbatasEminBtob = param.jnsbatasEminBtob;
        energi.statussubsidi = param.statussubsidi;
        energi.rpsub = param.rpsub;
        energi.rpnonsub = param.rpnonsub;
        energi.kwhsub = param.kwhsub;
        energi.kwhnonsub = param.kwhnonsub;
        energi.btskwhsubsidi = param.btskwhsubsidi;
        energi.rpblok1Sub = param.rpblok1Sub;
        energi.rpblok2Sub = param.rpblok2Sub;
        energi.rpblok3Sub = param.rpblok3Sub;
        energi.rpblok1Nonsub = param.rpblok1Nonsub;
        energi.rpblok2Nonsub = param.rpblok2Nonsub;
        energi.rpblok3Nonsub = param.rpblok3Nonsub;
        energi.kwhblok1Sub = param.kwhblok1Sub;
        energi.kwhblok2Sub = param.kwhblok2Sub;
        energi.kwhblok3Sub = param.kwhblok3Sub;
        energi.kwhblok1Nonsub = param.kwhblok1Nonsub;
        energi.kwhblok2Nonsub = param.kwhblok2Nonsub;
        energi.kwhblok3Nonsub = param.kwhblok3Nonsub;
        energi.jnsbatas101a = param.jnsbatas101a;
        energi.btsblok101a = param.btsblok101a;
        energi.bpakai101a = param.bpakai101a;
        energi.kwhwbpBatas = param.kwhwbpBatas;
        energi.dayamaxwbpBatas = param.dayamaxwbpBatas;
        energi.dayamaxSuplai = param.dayamaxSuplai;
        energi.dayamaxwbpSuplai = param.dayamaxwbpSuplai;
        energi.kdjnsmeter = param.kdjnsmeter;
        energi.suplaiDayamax = param.suplaiDayamax;
        energi.suplaiDayamaxwbp = param.suplaiDayamaxwbp;
        energi.dayamaxReal = param.dayamaxReal;
        energi.dayamaxwbpReal = param.dayamaxwbpReal;
        energi.cosphiDmp = param.cosphiDmp;
        energi.kwhpakaimaks = param.kwhpakaimaks;
        energi.kwhpakainormal = param.kwhpakainormal;
        energi.syaratpakai1 = param.syaratpakai1;
        energi.syaratpakai2 = param.syaratpakai2;
        energi.statuspakai = param.statuspakai;
        energi.statusdmp = param.statusdmp;
        energi.kwhwbp1Reg = param.kwhwbp1Reg;
        energi.kwhwbp2Reg = param.kwhwbp2Reg;
        energi.dayawbpDis = param.dayawbpDis;
        energi.kwhwbpDasarIns = param.kwhwbpDasarIns;
        energi.kwhwbpTambahIns = param.kwhwbpTambahIns;
        energi.trfwbp1Reg = param.trfwbp1Reg;
        energi.trfwbp2Reg = param.trfwbp2Reg;
        energi.trfdayawbpDis = param.trfdayawbpDis;
        energi.trfwbpIns = param.trfwbpIns;
        energi.rpwbp1Reg = param.rpwbp1Reg;
        energi.rpwbp2Reg = param.rpwbp2Reg;
        energi.rpdayawbpDis = param.rpdayawbpDis;
        energi.rpinsDasar = param.rpinsDasar;
        energi.rpinsTambah = param.rpinsTambah;
        energi.rpinsDmp = param.rpinsDmp;
        energi.rpinsMax = param.rpinsMax;
        energi.rplwbpReg = param.rplwbpReg;
        energi.rpwbpReg = param.rpwbpReg;
        energi.rpblok3Reg = param.rpblok3Reg;
        energi.rpkwhReg = param.rpkwhReg;
        energi.rplwbpPremium = param.rplwbpPremium;
        energi.rpwbpPremium = param.rpwbpPremium;
        energi.rpkwhPremium = param.rpkwhPremium;
        energi.rplwbpBtob = param.rplwbpBtob;
        energi.rpwbpBtob = param.rpwbpBtob;
        energi.rpkwhBtob = param.rpkwhBtob;
        energi.rpkvarh = param.rpkvarh;
        energi.rpbeban = param.rpbeban;
        energi.trfbeban = param.trfbeban;
        energi.trfkvarh = param.trfkvarh;
        energi.trflwbpReg = param.trflwbpReg;
        energi.trfwbpReg = param.trfwbpReg;
        energi.trfblok3 = param.trfblok3;
        energi.trflwbpPremium = param.trflwbpPremium;
        energi.trfwbpPremium = param.trfwbpPremium;
        energi.trflwbpBtob = param.trflwbpBtob;
        energi.trfwbpBtob = param.trfwbpBtob;
        energi.faktorkTdl = param.faktorkTdl;
        energi.faktorpTdl = param.faktorpTdl;
        energi.trflwbpTdl = param.trflwbpTdl;
        energi.trfwbpTdl = param.trfwbpTdl;
        energi.lwbp3 = param.lwbp3;
        energi.wbp3 = param.wbp3;
        energi.blok33 = param.blok33;
        energi.kwhpakai3 = param.kwhpakai3;
        energi.rplwbp3 = param.rplwbp3;
        energi.rpwbp3 = param.rpwbp3;
        energi.rpblok33 = param.rpblok33;
        energi.rpkwh3 = param.rpkwh3;
        energi.trflwbpDasar = param.trflwbpDasar;
        energi.trfwbpDasar = param.trfwbpDasar;
        energi.trfkvarhDasar = param.trfkvarhDasar;
        return energi;
    }

    public static BillingEnergi OutputToEntity(BillingEnergi billingEnergi, InisialisasiBillingDTO input, Output output) {

        billingEnergi.kwhminEminReg = output.getKwhmin_emin_reg() == null ? null : output.getKwhmin_emin_reg();
        billingEnergi.rprekmin = output.getRprekmin() == null ? null : output.getRprekmin();
        billingEnergi.kwhlwbpReal = output.getKwhlwbp_real() == null ? null : output.getKwhlwbp_real();
        billingEnergi.kwhlwbpReg = output.getKwhlwbp() == null ? null : output.getKwhlwbp();
        billingEnergi.kwhwbpReal = output.getKwhwbp() == null ? null : output.getKwhwbp();
        billingEnergi.kwhwbpReg = output.getKwhwbp() == null ? null : output.getKwhwbp();
        billingEnergi.kwhblok3Real = output.getBlok3() == null ? null : output.getBlok3();
        billingEnergi.kwhblok3Reg = output.getBlok3() == null ? null : output.getBlok3();
        billingEnergi.rpblok3Reg = output.getRpblok3() == null ? null : output.getRpblok3();
        billingEnergi.pemkwhReal = output.getPemkwh_real() == null ? null : output.getPemkwh_real();
        billingEnergi.pemkwhReg = output.getPemkwh() == null ? null : output.getPemkwh();
        billingEnergi.rplwbpReg = output.getRplwbp_reg() == null ? null : output.getRplwbp_reg();
        billingEnergi.rpwbpReg = output.getRpwbp() == null ? null : output.getRpwbp();
        billingEnergi.rpkwhReg = output.getRpkwh_reg() == null ? null : output.getRpkwh_reg();
        billingEnergi.rpbeban = output.getRpbeban() == null ? null : output.getRpbeban();

        billingEnergi.trfbeban = input.biayaBeban;
        billingEnergi.trflwbpReg = output.getTrflwbp_reg();
        billingEnergi.trfwbpReg = output.getTrfwbp_reg();
        billingEnergi.trflwbpTdl = output.getTrflwbp_tdl();
        billingEnergi.trfwbpTdl = output.getTrfwbp_tdl();
        billingEnergi.trfblok3 = input.biayaPakai3;
        billingEnergi.tglhitung = LocalDateTime.now();
//mapping plts atap
        billingEnergi.kwhSaldoakhirPlts = output.getKwhSaldoakhirPlts() == null ? null : output.getKwhSaldoakhirPlts();
        billingEnergi.kwhSaldoawalPlts = output.getKwhSaldoawalPlts() == null ? null : output.getKwhSaldoawalPlts();
        billingEnergi.kwhKurangPlts = output.getKwhKurangPlts() == null ? null : output.getKwhKurangPlts();
        billingEnergi.kwhTambahPlts = output.getKwhTambahPlts() == null ? null : output.getKwhTambahPlts();
        billingEnergi.kwhlwbpNet = output.getKwhlwbpNet() == null ? null : output.getKwhlwbpNet();
        billingEnergi.kwhlwbpPotOffset = output.getKwhlwbpPotOffset() == null ? null : output.getKwhlwbpPotOffset();
        billingEnergi.kwhlwbpOffset = output.getKwhlwbpOffset() == null ? null : output.getKwhlwbpOffset();
        billingEnergi.pemkwhExport = output.getPemkwhExport() == null ? null : output.getPemkwhExport();
        billingEnergi.pemkvarhExport = output.getPemkvarhExport() == null ? null : output.getPemkvarhExport();
        billingEnergi.blok3Export = output.getBlok3Export() == null ? null : output.getBlok3Export();
        billingEnergi.kwhwbpExport = output.getKwhwbpExport() == null ? null : output.getKwhwbpExport();
        billingEnergi.kwhlwbpExport = output.getKwhlwbpExport() == null ? null : output.getKwhlwbpExport();
        billingEnergi.sahlwbpExport = output.getSahlwbpExport() == null ? null : output.getSahlwbpExport();
        billingEnergi.sahwbpExport = output.getSahwbpExport() == null ? null : output.getSahwbpExport();
        billingEnergi.sahkvarhExport = output.getSahkvarhExport() == null ? null : output.getSahkvarhExport();
        billingEnergi.slalwbpExport = output.getSlalwbpExport() == null ? null : output.getSlalwbpExport();
        billingEnergi.slawbpExport = output.getSlawbpExport() == null ? null : output.getSlawbpExport();
        billingEnergi.slakvarhExport = output.getSlakvarhExport() == null ? null : output.getSlakvarhExport();
        billingEnergi.kapasitasPltsAtap = output.getKapasitasPltsAtap() == null ? null : output.getKapasitasPltsAtap();
        billingEnergi.pemkvarhImport = output.getPemkvarhImport() == null ? null : output.getPemkvarhImport();
        billingEnergi.pemkwhImport = output.getPemkwhImport() == null ? null : output.getPemkwhImport();
        billingEnergi.blok3Import = output.getBlok3Import() == null ? null : output.getBlok3Import();
        billingEnergi.kwhwbpImport = output.getKwhwbpImport() == null ? null : output.getKwhwbpImport();
        billingEnergi.kwhlwbpImport = output.getKwhlwbpImport() == null ? null : output.getKwhlwbpImport();

        return billingEnergi;
    }

    public static BillingEnergi OutputToEntityMutasi(BillingEnergi billingEnergi, InisialisasiBillingDTO input, OutputEnergiParam outputEnergi) {

        billingEnergi.fraksibeban = outputEnergi.getFraksibeban() == null ? null : outputEnergi.getFraksibeban();
        billingEnergi.haribeban = outputEnergi.getHaribeban() == null ? null : outputEnergi.getHaribeban();
        billingEnergi.haribebanBulan = outputEnergi.getHaribeban_bulan() == null ? null : outputEnergi.getHaribeban_bulan();
        billingEnergi.fraksikwh = outputEnergi.getFraksikwh() == null ? null : outputEnergi.getFraksikwh();
        billingEnergi.fraksiemin = outputEnergi.getFraksiemin() == null ? null : outputEnergi.getFraksiemin();
        billingEnergi.haripakai = outputEnergi.getHaripakai() == null ? null : outputEnergi.getHaripakai();
        billingEnergi.haripakaiBulan = outputEnergi.getHaripakai_bulan() == null ? null : outputEnergi.getHaripakai_bulan();
        billingEnergi.kvarhlebih = outputEnergi.getKvarhlebih() == null ? null : outputEnergi.getKvarhlebih();
        billingEnergi.kvarhpakaiReal = outputEnergi.getKvarhpakai_real() == null ? null : outputEnergi.getKvarhpakai_real();
        billingEnergi.kvarhlebih = outputEnergi.getKvarhlebih() == null ? null : outputEnergi.getKvarhlebih();
        billingEnergi.jamnyalaReal = outputEnergi.getJamnyala_real() == null ? null : outputEnergi.getJamnyala_real();
        billingEnergi.kwhminEminReg = outputEnergi.getKwhmin_emin_reg() == null ? null : outputEnergi.getKwhmin_emin_reg();
        billingEnergi.eminReg = outputEnergi.getEmin_reg() == null ? null : outputEnergi.getEmin_reg();
        billingEnergi.jnsbatasEminReg = outputEnergi.getJnsbatas_emin_reg() == null ? null : outputEnergi.getJnsbatas_emin_reg();
        billingEnergi.kwhpakaimaks = outputEnergi.getKwhpakaimaks() == null ? null : outputEnergi.getKwhpakaimaks();
        billingEnergi.kwhpakainormal = outputEnergi.getKwhpakainormal() == null ? null : outputEnergi.getKwhpakainormal();
        billingEnergi.kwhminEminReg = outputEnergi.getKwhmin_emin_reg() == null ? null : outputEnergi.getKwhmin_emin_reg();
        billingEnergi.rprekmin = outputEnergi.getRprekmin() == null ? null : outputEnergi.getRprekmin();
        billingEnergi.kwhlwbpReal = outputEnergi.getKwhlwbp_real() == null ? null : outputEnergi.getKwhlwbp_real();
        billingEnergi.kwhlwbpReg = outputEnergi.getKwhlwbp_reg() == null ? null : outputEnergi.getKwhlwbp_reg();
        billingEnergi.kwhwbpReal = outputEnergi.getKwhwbp_real() == null ? null : outputEnergi.getKwhwbp_real();
        billingEnergi.kwhwbpReg = outputEnergi.getKwhwbp_reg() == null ? null : outputEnergi.getKwhwbp_reg();
        billingEnergi.kwhblok3Real = outputEnergi.getKwhblok3_real() == null ? null : outputEnergi.getKwhblok3_real();
        billingEnergi.kwhblok3Reg = outputEnergi.getKwhblok3_reg() == null ? null : outputEnergi.getKwhblok3_reg();
        billingEnergi.rpblok3Reg = outputEnergi.getRpblok3_reg() == null ? null : outputEnergi.getRpblok3_reg();
        billingEnergi.pemkwhReal = outputEnergi.getPemkwh_real() == null ? null : outputEnergi.getPemkwh_real();
        billingEnergi.pemkwhReg = outputEnergi.getPemkwh_reg() == null ? null : outputEnergi.getPemkwh_reg();
        billingEnergi.rplwbpReg = outputEnergi.getRplwbp_reg() == null ? null : outputEnergi.getRplwbp_reg();
        billingEnergi.rpwbpReg = outputEnergi.getRpwbp_reg() == null ? null : outputEnergi.getRpwbp_reg();
        billingEnergi.rpkwhReg = outputEnergi.getRpkwh_reg() == null ? null : outputEnergi.getRpkwh_reg();
        billingEnergi.rpbeban = outputEnergi.getRpbeban() == null ? null : outputEnergi.getRpbeban();
        billingEnergi.rpkvarh = outputEnergi.getRpkvarh() == null ? null : outputEnergi.getRpkvarh();

        billingEnergi.trfbeban = input.getDataEnergi().get(billingEnergi.pecahKe).biayaBeban;
        billingEnergi.trflwbpReg = input.getDataEnergi().get(billingEnergi.pecahKe).biayaPakai1;
        billingEnergi.trfwbpReg = input.getDataEnergi().get(billingEnergi.pecahKe).biayaPakai2;
        billingEnergi.trfblok3 = input.getDataEnergi().get(billingEnergi.pecahKe).biayaPakai3;
        billingEnergi.trfkvarh = input.getDataEnergi().get(billingEnergi.pecahKe).biayaKvar;
        if (input.getDataEnergi().get(billingEnergi.pecahKe).faktorKValue != null) {
            billingEnergi.faktorkTdl = input.getDataEnergi().get(billingEnergi.pecahKe).faktorKValue;
        }
        if (input.getDataEnergi().get(billingEnergi.pecahKe).faktorKValue != null) {
            billingEnergi.faktorpTdl = input.getDataEnergi().get(billingEnergi.pecahKe).faktorPValue;
        }

        billingEnergi.tglhitung = LocalDateTime.now();
        billingEnergi.trfwbpTdl = outputEnergi.getTrfwbp_tdl() == null ? null : outputEnergi.getTrfwbp_tdl();
        billingEnergi.trflwbpTdl = outputEnergi.getTrflwbp_tdl() == null ? null : outputEnergi.getTrflwbp_tdl();

        billingEnergi.kwhSaldoakhirPlts = outputEnergi.getKwh_saldoakhir_plts() == null ? BigDecimal.ZERO : outputEnergi.getKwh_saldoakhir_plts();
        billingEnergi.kwhSaldoawalPlts = outputEnergi.getKwh_saldoawal_plts() == null ? null : outputEnergi.getKwh_saldoawal_plts();
        billingEnergi.kwhKurangPlts = outputEnergi.getKwh_kurang_plts() == null ? null : outputEnergi.getKwh_kurang_plts();
        billingEnergi.kwhTambahPlts = outputEnergi.getKwh_tambah_plts() == null ? null : outputEnergi.getKwh_tambah_plts();
        billingEnergi.kwhlwbpNet = outputEnergi.getKwhlwbp_net() == null ? null : outputEnergi.getKwhlwbp_net();
        billingEnergi.kwhlwbpPotOffset = outputEnergi.getKwhlwbp_pot_offset() == null ? null : outputEnergi.getKwhlwbp_pot_offset();
        billingEnergi.kwhlwbpOffset = outputEnergi.getKwhlwbp_offset() == null ? null : outputEnergi.getKwhlwbp_offset();
        billingEnergi.pemkwhExport = outputEnergi.getPemkwh_export() == null ? null : outputEnergi.getPemkwh_export();
        billingEnergi.pemkvarhExport = outputEnergi.getPemkvarh_export() == null ? null : outputEnergi.getPemkvarh_export();
        billingEnergi.blok3Export = outputEnergi.getBlok3_export() == null ? null : outputEnergi.getBlok3_export();
        billingEnergi.kwhwbpExport = outputEnergi.getKwhwbp_export() == null ? null : outputEnergi.getKwhwbp_export();
        billingEnergi.kwhlwbpExport = outputEnergi.getKwhlwbp_export() == null ? null : outputEnergi.getKwhlwbp_export();
        billingEnergi.kapasitasPltsAtap = outputEnergi.getKapasitas_plts_atap() == null ? null : outputEnergi.getKapasitas_plts_atap();
        billingEnergi.pemkvarhImport = outputEnergi.getPemkvarh_import() == null ? null : outputEnergi.getPemkvarh_import();
        billingEnergi.pemkwhImport = outputEnergi.getPemkwh_import() == null ? null : outputEnergi.getPemkwh_import();
        billingEnergi.blok3Import = outputEnergi.getBlok3_import() == null ? null : outputEnergi.getBlok3_import();
        billingEnergi.kwhwbpImport = outputEnergi.getKwhwbp_import() == null ? null : outputEnergi.getKwhwbp_import();
        billingEnergi.kwhlwbpImport = outputEnergi.getKwhlwbp_import() == null ? null : outputEnergi.getKwhlwbp_import();

        return billingEnergi;
    }

    public static LogBillingEnergi mapLogBillingEnergi(LogBillingEnergi logEnergi, BillingEnergi param) {
        BeanUtil.copyProperties(param, logEnergi);
        logEnergi.id_log = UUID.randomUUID().toString();
        // Format ulang tglbacalalu dan tglbacaakhir jika masih mengandung '/'
        logEnergi.tglbacalalu = formatTanggal(param.tglbacalalu);
        logEnergi.tglbacaakhir = formatTanggal(param.tglbacaakhir);
        return logEnergi;
    }
    
    private static String formatTanggal(String tanggal) {
        if (tanggal != null && tanggal.contains("/")) {
            return tanggal.replace("/", "");
        }
        return tanggal; // jika sudah dalam format yyyyMMdd
    }

    public static BillingEnergi720JN mapBillingEnergi720(BillingEnergi720JN Energi720, BillingEnergi param) {
        BeanUtil.copyProperties(param, Energi720);
        // Energi720.id = UUID.randomUUID().toString();
        // Format ulang tglbacalalu dan tglbacaakhir jika masih mengandung '/'
        Energi720.tglbacalalu = formatTanggal(param.tglbacalalu);
        Energi720.tglbacaakhir = formatTanggal(param.tglbacaakhir);
        return Energi720;
    }
    
}