package com.iconplus.ap2t.generator.mapper;


import cn.hutool.core.bean.BeanUtil;
import com.iconplus.ap2t.common.param.billing.OutputEnergiParam;
import com.iconplus.ap2t.data.entity.billing.BillingEnergi;
import com.iconplus.ap2t.data.entity.billing.BillingEnergiBruto;
import com.iconplus.ap2t.data.entity.log.LogBillingEnergiBruto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

public class BillingEnergiBrutoMapper {

    public static BillingEnergiBruto OutputToEntityMutasi(
            BillingEnergiBruto billingEnergiBruto, BillingEnergi billingEnergi, OutputEnergiParam outBillEnergiBruto
    ) {
        billingEnergiBruto.id = billingEnergi.id;
        billingEnergiBruto.idpel = billingEnergi.idpel;
        billingEnergiBruto.thblrek = billingEnergi.thblrek;
        billingEnergiBruto.jenisEnergi = billingEnergi.jenisEnergi;
        billingEnergiBruto.pecahKe = billingEnergi.pecahKe;
        billingEnergiBruto.jnsmut = billingEnergi.jnsmut;
        billingEnergiBruto.thblmut = billingEnergi.thblmut;
        billingEnergiBruto.kdmut = billingEnergi.kdmut;
        billingEnergiBruto.tglrubah = billingEnergi.tglrubah;
        billingEnergiBruto.tarif = billingEnergi.tarif;
        billingEnergiBruto.kdpt = billingEnergi.kdpt;
        billingEnergiBruto.kdpt2 = billingEnergi.kdpt2;
        billingEnergiBruto.daya = BigDecimal.valueOf(billingEnergi.daya);
        billingEnergiBruto.dayar312 = billingEnergi.dayar312;
        billingEnergiBruto.kogol = billingEnergi.kogol;
        billingEnergiBruto.subkogol = billingEnergi.subkogol;
        billingEnergiBruto.faknpremium = billingEnergi.faknpremium;
        billingEnergiBruto.kdam = billingEnergi.kdam;
        billingEnergiBruto.frt = billingEnergi.frt;
        billingEnergiBruto.fjn = billingEnergi.fjn;
        billingEnergiBruto.kdind = billingEnergi.kdind;
        billingEnergiBruto.maxDemand = billingEnergi.maxDemand;
        billingEnergiBruto.kdkvamaks = billingEnergi.kdkvamaks;
        billingEnergiBruto.sahkvamaxWbp = billingEnergi.sahkvamaxWbp;
        billingEnergiBruto.dayamaks = billingEnergi.dayamaks;
        billingEnergiBruto.dayamaxWbp = billingEnergi.dayamaxWbp;
        billingEnergiBruto.fakm = billingEnergi.fakm;
        billingEnergiBruto.fakmkvarh = billingEnergi.fakmkvarh;
        billingEnergiBruto.fakmkvam = billingEnergi.fakmkvam;
        billingEnergiBruto.kdpembmeter = billingEnergi.kdpembmeter;
        billingEnergiBruto.dayajbst = billingEnergi.dayajbst;
        billingEnergiBruto.kdbedajbst = billingEnergi.kdbedajbst;
        billingEnergiBruto.tglinisialisasi = billingEnergi.tglinisialisasi;
        billingEnergiBruto.tglbacalalu = billingEnergi.tglbacalalu;
        billingEnergiBruto.tglbacaakhir = billingEnergi.tglbacaakhir;
        billingEnergiBruto.slalwbp = billingEnergi.slalwbp;
        billingEnergiBruto.sahlwbp = billingEnergi.sahlwbp;
        billingEnergiBruto.slawbp = billingEnergi.slawbp;
        billingEnergiBruto.sahwbp = billingEnergi.sahwbp;
        billingEnergiBruto.slakvarh = billingEnergi.slakvarh;
        billingEnergiBruto.sahkvarh = billingEnergi.sahkvarh;
        billingEnergiBruto.sahkvamaks = billingEnergi.sahkvamaks;
        billingEnergiBruto.batch = billingEnergi.batch;
        billingEnergiBruto.msg = billingEnergi.msg;
        billingEnergiBruto.fillerFraksi = billingEnergi.fillerFraksi;
        billingEnergiBruto.fraksihemat = billingEnergi.fraksihemat;
        billingEnergiBruto.fraksidmp = billingEnergi.fraksidmp;
        billingEnergiBruto.jamnyala350Batas = billingEnergi.jamnyala350Batas;
        billingEnergiBruto.fillerReal = billingEnergi.fillerReal;
        billingEnergiBruto.cosphiReal = billingEnergi.cosphiReal;
        billingEnergiBruto.fillerDivideReal = billingEnergi.fillerDivideReal;
        billingEnergiBruto.kwhwbpRegReal = billingEnergi.kwhwbpRegReal;
        billingEnergiBruto.pemkwhRegReal = billingEnergi.pemkwhRegReal;
        billingEnergiBruto.jamnyalaRegReal = billingEnergi.jamnyalaRegReal;
        billingEnergiBruto.kwhlwbpPremiumReal = billingEnergi.kwhlwbpPremiumReal;
        billingEnergiBruto.kwhwbpPremiumReal = billingEnergi.kwhwbpPremiumReal;
        billingEnergiBruto.pemkwhPremiumReal = billingEnergi.pemkwhPremiumReal;
        billingEnergiBruto.kwhlwbpBtobReal = billingEnergi.kwhlwbpBtobReal;
        billingEnergiBruto.kwhwbpBtobReal = billingEnergi.kwhwbpBtobReal;
        billingEnergiBruto.pemkwhBtobReal = billingEnergi.pemkwhBtobReal;
        billingEnergiBruto.fillerAfterEmin = billingEnergi.fillerAfterEmin;
        billingEnergiBruto.kwhlwbpPremium = billingEnergi.kwhlwbpPremium;
        billingEnergiBruto.kwhwbpPremium = billingEnergi.kwhwbpPremium;
        billingEnergiBruto.pemkwhPremium = billingEnergi.pemkwhPremium;
        billingEnergiBruto.kwhminEminPremium = billingEnergi.kwhminEminPremium;
        billingEnergiBruto.eminPremium = billingEnergi.eminPremium;
        billingEnergiBruto.jnsbatasEminPremium = billingEnergi.jnsbatasEminPremium;
        billingEnergiBruto.kwhlwbpBtob = billingEnergi.kwhlwbpBtob;
        billingEnergiBruto.kwhwbpBtob = billingEnergi.kwhwbpBtob;
        billingEnergiBruto.pemkwhBtob = billingEnergi.pemkwhBtob;
        billingEnergiBruto.kwhminEminBtob = billingEnergi.kwhminEminBtob;
        billingEnergiBruto.eminBtob = billingEnergi.eminBtob;
        billingEnergiBruto.jnsbatasEminBtob = billingEnergi.jnsbatasEminBtob;
        billingEnergiBruto.filler101a = billingEnergi.filler101a;
        billingEnergiBruto.statussubsidi = billingEnergi.statussubsidi;
        billingEnergiBruto.rpsub = billingEnergi.rpsub;
        billingEnergiBruto.rpnonsub = billingEnergi.rpnonsub;
        billingEnergiBruto.kwhsub = billingEnergi.kwhsub;
        billingEnergiBruto.kwhnonsub = billingEnergi.kwhnonsub;
        billingEnergiBruto.btskwhsubsidi = billingEnergi.btskwhsubsidi;
        billingEnergiBruto.rpblok1Sub = billingEnergi.rpblok1Sub;
        billingEnergiBruto.rpblok2Sub = billingEnergi.rpblok2Sub;
        billingEnergiBruto.rpblok3Sub = billingEnergi.rpblok3Sub;
        billingEnergiBruto.rpblok1Nonsub = billingEnergi.rpblok1Nonsub;
        billingEnergiBruto.rpblok2Nonsub = billingEnergi.rpblok2Nonsub;
        billingEnergiBruto.rpblok3Nonsub = billingEnergi.rpblok3Nonsub;
        billingEnergiBruto.kwhblok1Sub = billingEnergi.kwhblok1Sub;
        billingEnergiBruto.kwhblok2Sub = billingEnergi.kwhblok2Sub;
        billingEnergiBruto.kwhblok3Sub = billingEnergi.kwhblok3Sub;
        billingEnergiBruto.kwhblok1Nonsub = billingEnergi.kwhblok1Nonsub;
        billingEnergiBruto.kwhblok2Nonsub = billingEnergi.kwhblok2Nonsub;
        billingEnergiBruto.kwhblok3Nonsub = billingEnergi.kwhblok3Nonsub;
        billingEnergiBruto.jnsbatas101a = billingEnergi.jnsbatas101a;
        billingEnergiBruto.btsblok101a = billingEnergi.btsblok101a;
        billingEnergiBruto.bpakai101a = billingEnergi.bpakai101a;
        billingEnergiBruto.fillerDmp = billingEnergi.fillerDmp;
        billingEnergiBruto.kwhwbpBatas = billingEnergi.kwhwbpBatas;
        billingEnergiBruto.dayamaxwbpBatas = billingEnergi.dayamaxwbpBatas;
        billingEnergiBruto.dayamaxSuplai = billingEnergi.dayamaxSuplai;
        billingEnergiBruto.dayamaxwbpSuplai = billingEnergi.dayamaxwbpSuplai;
        billingEnergiBruto.kdjnsmeter = billingEnergi.kdjnsmeter;
        billingEnergiBruto.filler0 = billingEnergi.filler0;
        billingEnergiBruto.suplaiDayamax = billingEnergi.suplaiDayamax;
        billingEnergiBruto.suplaiDayamaxwbp = billingEnergi.suplaiDayamaxwbp;
        billingEnergiBruto.dayamaxReal = billingEnergi.dayamaxReal;
        billingEnergiBruto.dayamaxwbpReal = billingEnergi.dayamaxwbpReal;
        billingEnergiBruto.cosphiDmp = billingEnergi.cosphiDmp;
        billingEnergiBruto.syaratpakai1 = billingEnergi.syaratpakai1;
        billingEnergiBruto.syaratpakai2 = billingEnergi.syaratpakai2;
        billingEnergiBruto.statuspakai = billingEnergi.statuspakai;
        billingEnergiBruto.statusdmp = billingEnergi.statusdmp;
        billingEnergiBruto.kwhwbp1Reg = billingEnergi.kwhwbp1Reg;
        billingEnergiBruto.kwhwbp2Reg = billingEnergi.kwhwbp2Reg;
        billingEnergiBruto.dayawbpDis = billingEnergi.dayawbpDis;
        billingEnergiBruto.kwhwbpDasarIns = billingEnergi.kwhwbpDasarIns;
        billingEnergiBruto.kwhwbpTambahIns = billingEnergi.kwhwbpTambahIns;
        billingEnergiBruto.trfwbp1Reg = billingEnergi.trfwbp1Reg;
        billingEnergiBruto.trfwbp2Reg = billingEnergi.trfwbp2Reg;
        billingEnergiBruto.trfdayawbpDis = billingEnergi.trfdayawbpDis;
        billingEnergiBruto.trfwbpIns = billingEnergi.trfwbpIns;
        billingEnergiBruto.rpwbp1Reg = billingEnergi.rpwbp1Reg;
        billingEnergiBruto.rpwbp2Reg = billingEnergi.rpwbp2Reg;
        billingEnergiBruto.rpdayawbpDis = billingEnergi.rpdayawbpDis;
        billingEnergiBruto.rpinsDasar = billingEnergi.rpinsDasar;
        billingEnergiBruto.rpinsTambah = billingEnergi.rpinsTambah;
        billingEnergiBruto.rpinsDmp = billingEnergi.rpinsDmp;
        billingEnergiBruto.rpinsMax = billingEnergi.rpinsMax;
        billingEnergiBruto.fillerRp = billingEnergi.fillerRp;
        billingEnergiBruto.rplwbpPremium = billingEnergi.rplwbpPremium;
        billingEnergiBruto.rpwbpPremium = billingEnergi.rpwbpPremium;
        billingEnergiBruto.rpkwhPremium = billingEnergi.rpkwhPremium;
        billingEnergiBruto.rplwbpBtob = billingEnergi.rplwbpBtob;
        billingEnergiBruto.rpwbpBtob = billingEnergi.rpwbpBtob;
        billingEnergiBruto.rpkwhBtob = billingEnergi.rpkwhBtob;
        billingEnergiBruto.fillerTrf = billingEnergi.fillerTrf;
        billingEnergiBruto.trfbeban = billingEnergi.trfbeban;
        billingEnergiBruto.trfblok3 = billingEnergi.trfblok3;
        billingEnergiBruto.trflwbpPremium = billingEnergi.trflwbpPremium;
        billingEnergiBruto.trfwbpPremium = billingEnergi.trfwbpPremium;
        billingEnergiBruto.trflwbpBtob = billingEnergi.trflwbpBtob;
        billingEnergiBruto.trfwbpBtob = billingEnergi.trfwbpBtob;
        billingEnergiBruto.fillerTdl = billingEnergi.fillerTdl;
        billingEnergiBruto.faktorkTdl = billingEnergi.faktorkTdl;
        billingEnergiBruto.faktorpTdl = billingEnergi.faktorpTdl;
        billingEnergiBruto.lwbp3 = billingEnergi.lwbp3;
        billingEnergiBruto.wbp3 = billingEnergi.wbp3;
        billingEnergiBruto.blok33 = billingEnergi.blok33;
        billingEnergiBruto.kwhpakai3 = billingEnergi.kwhpakai3;
        billingEnergiBruto.bel = billingEnergi.bel;
        billingEnergiBruto.rplwbp3 = billingEnergi.rplwbp3;
        billingEnergiBruto.rpwbp3 = billingEnergi.rpwbp3;
        billingEnergiBruto.rpblok33 = billingEnergi.rpblok33;
        billingEnergiBruto.rpkwh3 = billingEnergi.rpkwh3;
        billingEnergiBruto.rphargaJual = billingEnergi.rphargaJual;
        billingEnergiBruto.rpkvarhJual = billingEnergi.rpkvarhJual;
        billingEnergiBruto.kwhlwbp1Ins = billingEnergi.kwhlwbp1Ins;
        billingEnergiBruto.kwhlwbp2Ins = billingEnergi.kwhlwbp2Ins;
        billingEnergiBruto.kwhwbpIns = billingEnergi.kwhwbpIns;
        billingEnergiBruto.pemkwhIns = billingEnergi.pemkwhIns;
        billingEnergiBruto.rplwbp1Ins = billingEnergi.rplwbp1Ins;
        billingEnergiBruto.rplwbp2Ins = billingEnergi.rplwbp2Ins;
        billingEnergiBruto.rpwbpIns = billingEnergi.rpwbpIns;
        billingEnergiBruto.rpkwhIns = billingEnergi.rpkwhIns;
        billingEnergiBruto.jmlpadam = billingEnergi.jmlpadam;
        billingEnergiBruto.kwhwbpPadam = billingEnergi.kwhwbpPadam;
        billingEnergiBruto.sfc = billingEnergi.sfc;
        billingEnergiBruto.hsd = billingEnergi.hsd;
        billingEnergiBruto.rpBhBakar = billingEnergi.rpBhBakar;
        billingEnergiBruto.rpTambahBiaya = billingEnergi.rpTambahBiaya;
        billingEnergiBruto.pemkwhLwbp1Ins = billingEnergi.pemkwhLwbp1Ins;
        billingEnergiBruto.pemkwhLwbp1Normal = billingEnergi.pemkwhLwbp1Normal;
        billingEnergiBruto.fkmkwhLwbp1 = billingEnergi.fkmkwhLwbp1;
        billingEnergiBruto.rpinsentifLwbp1 = billingEnergi.rpinsentifLwbp1;
        billingEnergiBruto.tipeskema = billingEnergi.tipeskema;
        billingEnergiBruto.skema4 = billingEnergi.skema4;
        billingEnergiBruto.prosenWbpIns = billingEnergi.prosenWbpIns;
        billingEnergiBruto.prosenLwbp1Ins = billingEnergi.prosenLwbp1Ins;
        billingEnergiBruto.kwhWbppadamIns = billingEnergi.kwhWbppadamIns;
        billingEnergiBruto.kwhlwbp1Insentif = billingEnergi.kwhlwbp1Insentif;
        billingEnergiBruto.fkwbpIns = billingEnergi.fkwbpIns;
        billingEnergiBruto.gensetWbp = billingEnergi.gensetWbp;
        billingEnergiBruto.trflwbp1Ins = billingEnergi.trflwbp1Ins;
        billingEnergiBruto.rpgensetKeLwbp1 = billingEnergi.rpgensetKeLwbp1;
        billingEnergiBruto.rplwbp1InsWbp = billingEnergi.rplwbp1InsWbp;
        billingEnergiBruto.kwhWbpPadamHari = billingEnergi.kwhWbpPadamHari;
        billingEnergiBruto.kwhWbpPadamBulan = billingEnergi.kwhWbpPadamBulan;
        billingEnergiBruto.trflwbp1Normal = billingEnergi.trflwbp1Normal;
        billingEnergiBruto.wbprataIns = billingEnergi.wbprataIns;
        billingEnergiBruto.lbwp1rataIns = billingEnergi.lbwp1rataIns;
        billingEnergiBruto.kwhlwbp1InsentifPremium = billingEnergi.kwhlwbp1InsentifPremium;
        billingEnergiBruto.rasioReguler = billingEnergi.rasioReguler;
        billingEnergiBruto.rasioPremium = billingEnergi.rasioPremium;
        billingEnergiBruto.pemkwhLwbp1InsPremium = billingEnergi.pemkwhLwbp1InsPremium;
        billingEnergiBruto.pemkwhLwbp1NormalPremium = billingEnergi.pemkwhLwbp1NormalPremium;
        billingEnergiBruto.trflwbp1NormalPremium = billingEnergi.trflwbp1NormalPremium;
        billingEnergiBruto.trflwbp1InsPremium = billingEnergi.trflwbp1InsPremium;
        billingEnergiBruto.rplwbp1InsPremium = billingEnergi.rplwbp1InsPremium;
        billingEnergiBruto.rplwbp1InsWbpPremium = billingEnergi.rplwbp1InsWbpPremium;
        billingEnergiBruto.rplwbp2InsPremium = billingEnergi.rplwbp2InsPremium;
        billingEnergiBruto.rpwbpInsPremium = billingEnergi.rpwbpInsPremium;
        billingEnergiBruto.rpinsentifLwbp1Premium = billingEnergi.rpinsentifLwbp1Premium;
        billingEnergiBruto.rpkwhInsPremium = billingEnergi.rpkwhInsPremium;
        billingEnergiBruto.trflwbpDasar = billingEnergi.trflwbpDasar;
        billingEnergiBruto.trfwbpDasar = billingEnergi.trfwbpDasar;
        billingEnergiBruto.trfkvarhDasar = billingEnergi.trfkvarhDasar;
        billingEnergiBruto.billId = billingEnergi.billId;
        billingEnergiBruto.slalwbpExport = billingEnergi.slalwbpExport;
        billingEnergiBruto.slawbpExport = billingEnergi.slawbpExport;
        billingEnergiBruto.slakvarhExport = billingEnergi.slakvarhExport;
        billingEnergiBruto.sahlwbpExport = billingEnergi.sahlwbpExport;
        billingEnergiBruto.sahwbpExport = billingEnergi.sahwbpExport;
        billingEnergiBruto.sahkvarhExport = billingEnergi.sahkvarhExport;

        // mapping from ouput energy
        billingEnergiBruto.fraksibeban = outBillEnergiBruto.getFraksibeban() == null ? null : outBillEnergiBruto.getFraksibeban();
        billingEnergiBruto.haribeban = outBillEnergiBruto.getHaribeban() == null ? null : outBillEnergiBruto.getHaribeban();
        billingEnergiBruto.haribebanBulan = outBillEnergiBruto.getHaribeban_bulan() == null ? null : outBillEnergiBruto.getHaribeban_bulan();
        billingEnergiBruto.fraksikwh = outBillEnergiBruto.getFraksikwh() == null ? null : outBillEnergiBruto.getFraksikwh();
        billingEnergiBruto.fraksiemin = outBillEnergiBruto.getFraksiemin() == null ? null : outBillEnergiBruto.getFraksiemin();
        billingEnergiBruto.haripakai = outBillEnergiBruto.getHaripakai() == null ? null : outBillEnergiBruto.getHaripakai();
        billingEnergiBruto.haripakaiBulan = outBillEnergiBruto.getHaripakai_bulan() == null ? null : outBillEnergiBruto.getHaripakai_bulan();
        billingEnergiBruto.kwhlwbpReal = outBillEnergiBruto.getKwhlwbp_real() == null ? null : outBillEnergiBruto.getKwhlwbp_real();
        billingEnergiBruto.kwhwbpReal = outBillEnergiBruto.getKwhwbp_real() == null ? null : outBillEnergiBruto.getKwhwbp_real();
        billingEnergiBruto.kwhblok3Real = outBillEnergiBruto.getKwhblok3_real() == null ? null : outBillEnergiBruto.getKwhblok3_real();
        billingEnergiBruto.pemkwhReal = outBillEnergiBruto.getPemkwh_real() == null ? null : outBillEnergiBruto.getPemkwh_real();
        billingEnergiBruto.kvarhpakaiReal = outBillEnergiBruto.getKvarhpakai_real() == null ? null : outBillEnergiBruto.getKvarhpakai_real();
        billingEnergiBruto.kvarhlebih = outBillEnergiBruto.getKvarhlebih() == null ? null : outBillEnergiBruto.getKvarhlebih();
        billingEnergiBruto.jamnyalaReal = outBillEnergiBruto.getJamnyala_real() == null ? null : outBillEnergiBruto.getJamnyala_real();
        billingEnergiBruto.kwhlwbpReg = outBillEnergiBruto.getKwhlwbp_reg() == null ? null : outBillEnergiBruto.getKwhlwbp_reg();
        billingEnergiBruto.kwhwbpReg = outBillEnergiBruto.getKwhwbp_reg() == null ? null : outBillEnergiBruto.getKwhwbp_reg();
        billingEnergiBruto.kwhblok3Reg = outBillEnergiBruto.getKwhblok3_reg() == null ? null : outBillEnergiBruto.getKwhblok3_reg();
        billingEnergiBruto.pemkwhReg = outBillEnergiBruto.getPemkwh_reg() == null ? null : outBillEnergiBruto.getPemkwh_reg();
        billingEnergiBruto.kwhminEminReg = outBillEnergiBruto.getKwhmin_emin_reg() == null ? null : outBillEnergiBruto.getKwhmin_emin_reg();
        billingEnergiBruto.eminReg = outBillEnergiBruto.getEmin_reg() == null ? null : outBillEnergiBruto.getEmin_reg();
        billingEnergiBruto.rprekmin = outBillEnergiBruto.getRprekmin() == null ? null : outBillEnergiBruto.getRprekmin();
        billingEnergiBruto.jnsbatasEminReg = outBillEnergiBruto.getJnsbatas_emin_reg() == null ? null : outBillEnergiBruto.getJnsbatas_emin_reg();
        billingEnergiBruto.kwhpakaimaks = outBillEnergiBruto.getKwhpakaimaks() == null ? null : outBillEnergiBruto.getKwhpakaimaks();
        billingEnergiBruto.kwhpakainormal = outBillEnergiBruto.getKwhpakainormal() == null ? null : outBillEnergiBruto.getKwhpakainormal();
        billingEnergiBruto.rplwbpReg = outBillEnergiBruto.getRplwbp_reg() == null ? null : outBillEnergiBruto.getRplwbp_reg();
        billingEnergiBruto.rpwbpReg = outBillEnergiBruto.getRpwbp_reg() == null ? null : outBillEnergiBruto.getRpwbp_reg();
        billingEnergiBruto.rpblok3Reg = outBillEnergiBruto.getRpblok3_reg() == null ? null : outBillEnergiBruto.getRpblok3_reg();
        billingEnergiBruto.rpkwhReg = outBillEnergiBruto.getRpkwh_reg() == null ? null : outBillEnergiBruto.getRpkwh_reg();
        billingEnergiBruto.rpkvarh = outBillEnergiBruto.getRpkvarh() == null ? null : outBillEnergiBruto.getRpkvarh();
        billingEnergiBruto.rpbeban = outBillEnergiBruto.getRpbeban() == null ? null : outBillEnergiBruto.getRpbeban();
        billingEnergiBruto.kwhlwbpRegReal = outBillEnergiBruto.getKwhlwbp_reg() == null ? null : outBillEnergiBruto.getKwhlwbp_reg(); //kwhlwbp
        billingEnergiBruto.trfwbpReg = outBillEnergiBruto.getTrfwbp_reg() == null ? null : outBillEnergiBruto.getTrfwbp_reg();
        billingEnergiBruto.trfwbpTdl = outBillEnergiBruto.getTrfwbp_tdl() == null ? null : outBillEnergiBruto.getTrfwbp_tdl();
        billingEnergiBruto.trflwbpTdl = outBillEnergiBruto.getTrflwbp_tdl() == null ? null : outBillEnergiBruto.getTrflwbp_tdl();
        billingEnergiBruto.trflwbpReg = outBillEnergiBruto.getTrflwbp_reg() == null ? null : outBillEnergiBruto.getTrflwbp_reg();
        billingEnergiBruto.trfkvarh = outBillEnergiBruto.getTrfkvarh() == null ? null : outBillEnergiBruto.getTrfkvarh();
        billingEnergiBruto.eminRmPlts = outBillEnergiBruto.getEmin_rm_plts() == null ? null : outBillEnergiBruto.getEmin_rm_plts();
        billingEnergiBruto.kwhlwbpPotOffset = outBillEnergiBruto.getKwhlwbp_pot_offset() == null ? null : outBillEnergiBruto.getKwhlwbp_pot_offset();
        billingEnergiBruto.blok3Import = outBillEnergiBruto.getBlok3_import() == null ? null : outBillEnergiBruto.getBlok3_import();
        billingEnergiBruto.blok3Export = outBillEnergiBruto.getBlok3_export() == null ? null : outBillEnergiBruto.getBlok3_export();
        billingEnergiBruto.pemkvarhExport = outBillEnergiBruto.getPemkvarh_export() == null ? null : outBillEnergiBruto.getPemkvarh_export();
        billingEnergiBruto.pemkvarhImport = outBillEnergiBruto.getPemkvarh_import() == null ? null : outBillEnergiBruto.getPemkvarh_import();
        billingEnergiBruto.kwhSaldoakhirPlts = outBillEnergiBruto.getKwh_saldoakhir_plts() == null ? BigDecimal.ZERO : outBillEnergiBruto.getKwh_saldoakhir_plts();
        billingEnergiBruto.kapasitasPltsAtap = outBillEnergiBruto.getKapasitas_plts_atap() == null ? null : outBillEnergiBruto.getKapasitas_plts_atap();
        billingEnergiBruto.kwhwbpExport = outBillEnergiBruto.getKwhwbp_export() == null ? null : outBillEnergiBruto.getKwhwbp_export();
        billingEnergiBruto.kwhwbpImport = outBillEnergiBruto.getKwhwbp_import() == null ? null : outBillEnergiBruto.getKwhwbp_import();
        billingEnergiBruto.kwhlwbpImport = outBillEnergiBruto.getKwhlwbp_import() == null ? null : outBillEnergiBruto.getKwhlwbp_import();
        billingEnergiBruto.kwhlwbpExport = outBillEnergiBruto.getKwhlwbp_export() == null ? null : outBillEnergiBruto.getKwhlwbp_export();
        billingEnergiBruto.kwhSaldoawalPlts = outBillEnergiBruto.getKwh_saldoawal_plts() == null ? null : outBillEnergiBruto.getKwh_saldoawal_plts();
        billingEnergiBruto.kwhTambahPlts = outBillEnergiBruto.getKwh_tambah_plts() == null ? null : outBillEnergiBruto.getKwh_tambah_plts();
        billingEnergiBruto.kwhKurangPlts = outBillEnergiBruto.getKwh_kurang_plts() == null ? null : outBillEnergiBruto.getKwh_kurang_plts();
        billingEnergiBruto.pemkwhExport = outBillEnergiBruto.getPemkwh_export() == null ? null : outBillEnergiBruto.getPemkwh_export();
        billingEnergiBruto.pemkwhImport = outBillEnergiBruto.getPemkwh_import() == null ? null : outBillEnergiBruto.getPemkwh_import();
        billingEnergiBruto.kwhlwbpOffset = outBillEnergiBruto.getKwhlwbp_offset() == null ? null : outBillEnergiBruto.getKwhlwbp_offset();
        billingEnergiBruto.kwhlwbpNet = outBillEnergiBruto.getKwhlwbp_net() == null ? null : outBillEnergiBruto.getKwhlwbp_net();
        billingEnergiBruto.kvarhpakaiReal = outBillEnergiBruto.getPemkvarh() == null ? null : outBillEnergiBruto.getPemkvarh();
        billingEnergiBruto.tglhitung = LocalDateTime.now();

        return billingEnergiBruto;
    }

    public static LogBillingEnergiBruto toLogEntity(BillingEnergiBruto beb) {
        LogBillingEnergiBruto logBruto = new LogBillingEnergiBruto();
        BeanUtil.copyProperties(beb, logBruto);
        logBruto.id = UUID.randomUUID();
        logBruto.idBruto = beb.id;
        return logBruto;
    }
}
