package com.iconplus.ap2t.generator.mapper;


import com.iconplus.ap2t.data.entity.master.MasterLebihBayarPasca;
import com.iconplus.ap2t.data.entity.master.MasterLebihBayarPascaLog;
import com.iconplus.ap2t.data.entity.master.MasterLebihBayarStimulus;
import com.iconplus.ap2t.data.entity.master.MasterLebihBayarStimulusLog;

import java.time.LocalDateTime;

public class MasterLBMapper {

    public static MasterLebihBayarPascaLog mapToEntityMasterLBPascaLog(MasterLebihBayarPasca mlbp, MasterLebihBayarPascaLog log, String userId) {
        log.idpel=mlbp.idpel;
        log.rpSaldo= mlbp.rpSaldo;
        log.kwhSaldo= mlbp.kwhSaldo;
        log.tglCatat=mlbp.tglCatat;
        log.ptgCatat=mlbp.ptgCatat;
        log.rpSaldoAwal= mlbp.rpSaldoAwal;
        log.unitupi= mlbp.unitupi;
        log.unitap= mlbp.unitap;
        log.unitup= mlbp.unitup;
        log.tglLog= LocalDateTime.now();
        log.logBy=userId;

        return log;
    }

    public static MasterLebihBayarStimulusLog mapToEntityMasterLBStimulusLog(MasterLebihBayarStimulus mlbp, MasterLebihBayarStimulusLog log, String userId) {
        log.idpel=mlbp.idpel;
        log.rpSaldo= mlbp.rpSaldo;
        log.kwhSaldo= mlbp.kwhSaldo;
        log.tglCatat=mlbp.tglCatat;
        log.ptgCatat=mlbp.ptgCatat;
        log.rpSaldoAwal= mlbp.rpSaldoAwal;
        log.unitupi= mlbp.unitupi;
        log.unitap= mlbp.unitap;
        log.unitup= mlbp.unitup;
        log.tglLog= LocalDateTime.now();
        log.logBy=userId;

        return log;
    }
}
