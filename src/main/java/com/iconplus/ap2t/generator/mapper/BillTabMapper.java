package com.iconplus.ap2t.generator.mapper;


import com.iconplus.ap2t.common.utility.Helper;
import com.iconplus.ap2t.data.entity.billing.BillTabKdProsesKlp;
import com.iconplus.ap2t.data.entity.billing.BillTabProses;
import com.iconplus.ap2t.data.entity.master.MasterKodeProses;

public class BillTabMapper {

    public static BillTabProses mapToEntityFromKlp(BillTabProses btp, BillTabKdProsesKlp kdKlp, String thblrek) {
        btp.thblrek = thblrek;
        btp.kdProsesKlp = kdKlp.kdProsesKlp;
        btp.unitap = kdKlp.unitap;
        btp.kdProses = kdKlp.kdProses;
        btp.kdKlp = kdKlp.kdKlp;
        btp.jnsMtr = kdKlp.jnsMtr;
        btp.klpAwal = kdKlp.klpAwal;
        btp.klpAkhir = kdKlp.klpAkhir;
        btp.tglBayarAwal = kdKlp.tglBayarAwal.trim().isEmpty() ? "01" : kdKlp.tglBayarAwal;
        if ((int) Helper.getOrDefault(kdKlp.tglBayarAkhir, 31) > Helper.getLastDay(thblrek)) {
            btp.tglBayarAkhir = String.valueOf(Helper.getLastDay(thblrek));
        } else {
            btp.tglBayarAkhir = Helper.getOrDefault(
                kdKlp.tglBayarAkhir, Helper.getLastDay(thblrek)
            ).toString();
        }
        btp.ketProsesKlp = kdKlp.ketProsesKlp;
        btp.polaRekening = kdKlp.polarekening;
        btp.noProsesKlp = kdKlp.noprosesklp;
        btp.thblBayar = thblrek;
        btp.aktif = "0";
        btp.suplaiDil = "0";
        btp.suplaiStand = "0";
        btp.hitung = "0";
        btp.sah = "0";
        return btp;
    }

    public static MasterKodeProses mapEntityBillTabUp(MasterKodeProses tabUp, BillTabProses tabProses) {
        tabUp.thblrek=tabProses.thblrek;
        tabUp.kdProsesKlp=tabProses.kdProsesKlp;
        tabUp.unitap=tabProses.unitap;
        tabUp.kdProses=tabProses.kdProses;
        tabUp.kdKlp=tabProses.kdKlp;
        tabUp.jnsMtr=tabProses.jnsMtr;
        tabUp.klpAwal=tabProses.klpAwal;
        tabUp.klpAkhir=tabProses.klpAkhir;
        tabUp.tglBayarAwal=tabProses.tglBayarAwal;
        tabUp.tglBayarAkhir=tabProses.tglBayarAkhir;
        tabUp.ketProsesKlp= tabProses.ketProsesKlp;
        tabUp.polaRekening=tabProses.polaRekening;
        tabUp.noProsesKlp=tabProses.noProsesKlp;
        tabUp.thblBayar=tabProses.thblBayar;
        tabUp.aktif = tabProses.aktif;
        tabUp.suplaiDil=tabProses.suplaiDil;
        tabUp.suplaiStand=tabProses.suplaiStand;
        tabUp.hitung=tabProses.hitung;
        tabUp.sah=tabProses.sah;
        tabUp.tglAktif=tabProses.tglAktif;
        tabUp.tglSuplaiDil=tabProses.tglSuplaiDil;
        tabUp.tglSuplaiStand=tabProses.tglSuplaiStand;
        tabUp.tglHitung=tabProses.tglHitung;
        tabUp.tglsah=tabProses.tglSah;
        tabUp.byAktif=tabProses.byAktif;
        tabUp.bySuplaiDil=tabProses.bySuplaiDil;
        tabUp.byHitung=tabProses.byHitung;
        tabUp.bySah=tabProses.bySah;
        tabUp.tglBuatSorek=tabProses.tglBuatSorek;
        tabUp.tglBuatRekap=tabProses.tglBuatRekap;
        tabUp.byBuatSorek=tabProses.byBuatSorek;
        tabUp.byBuatRekap=tabProses.byBuatRekap;
        tabUp.jmlRecSuplaiDil=tabProses.jmlRecSuplaiDil;
        tabUp.jmlRecSuplaiStand=tabProses.jmlRecSuplaiStand;
        tabUp.jmlrecHitung= tabProses.jmlRecHitung;
        tabUp.jmlrecsah=tabProses.jmlRecSah;
        tabUp.unitup=tabProses.unitup;

        return tabUp;
    }
}
