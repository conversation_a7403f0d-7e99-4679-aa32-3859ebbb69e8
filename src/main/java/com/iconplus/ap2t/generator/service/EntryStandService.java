package com.iconplus.ap2t.generator.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.iconplus.ap2t.common.exception.AppException;
import com.iconplus.ap2t.common.param.CustomerParam;
import com.iconplus.ap2t.common.param.InisialisasiBillingDTO;
import com.iconplus.ap2t.common.param.billing.EntryStandParam;
import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.common.utility.Constant;
import com.iconplus.ap2t.common.utility.Helper;
import com.iconplus.ap2t.data.entity.billing.Billing;
import com.iconplus.ap2t.data.entity.billing.BillingEnergi;
import com.iconplus.ap2t.data.entity.billing.Dpm;
import com.iconplus.ap2t.data.entity.billing.DpmPecahan;
import com.iconplus.ap2t.data.entity.log.LogDPMKoreksi;
import com.iconplus.ap2t.data.entity.log.LogJobHitungBilling;
import com.iconplus.ap2t.data.repository.billing.*;
import com.iconplus.ap2t.data.repository.log.LogDPMRepository;
import com.iconplus.ap2t.data.repository.log.LogJobHitungBillingRepository;
import com.iconplus.ap2t.generator.dto.BillingEnergiBulanLaluDTO;
import com.iconplus.ap2t.generator.mapper.BillingEnergiMapper;
import com.iconplus.ap2t.generator.mapper.BillingMapper;
import com.iconplus.ap2t.generator.mapper.DpmMapper;
import com.iconplus.ap2t.generator.proxy.EngineProxy;
import com.iconplus.ap2t.generator.response.SuplaiDILDTO;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.hibernate.reactive.mutiny.Mutiny;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Singleton
public class EntryStandService {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    @Inject
    private DpmRepository dpmRepository;
    @Inject
    private LogDPMRepository logDPMRepository;
    @Inject
    private CrmService crmService;
    @Inject
    @RestClient
    private EngineProxy engineProxy;
    @Inject
    private BillingServiceOptimize billingService;
    @Inject
    private BillingRepository billingRepository;
    @Inject
    private DpmPecahanRepository dpmPecahanRepository;

    @Inject
    private BillingEnergiRepository billingEnergiRepository;

    
    @Inject
    private LogJobHitungBillingRepository logJobHitungBillingRepository;

    

    @Inject
    Mutiny.SessionFactory sessionFactory;

     Logger LOG = LoggerFactory.getLogger(EntryStandService.class.getName());

     @WithTransaction
     public Uni<APIResponse<Dpm>> storeStand(EntryStandParam params) {
         return updateEntryStand(params)
             .onItem().transformToUni(updateEntryStand -> storeBillingProccess(updateEntryStand)
                 .onItem().transformToUni(result -> {
                     BillingEnergi billing = (BillingEnergi) result.getData();
                     CustomerParam param = new CustomerParam();
                     param.idpel = billing.idpel;
                     param.thblrek = billing.thblrek;
                     return storeDataToWorkFlowV2_new(param)
                     .onFailure().recoverWithItem(ex -> {
                         LOG.error("Workflow processing failed with error: {}", ex.getMessage(), ex);
                         return null; // Abaikan error dan lanjutkan
                     })
                     .replaceWith(APIResponse.ok(updateEntryStand)); // ✅ Kembalikan APIResponse yang sukses
             }))
             .onFailure().transform(ex -> {
                 LOG.error(ex.getMessage());
                 return new AppException(ex.getMessage());
             });
     }

     public Uni<LogDPMKoreksi> saveLogKoreksi(EntryStandParam param) {
         LogDPMKoreksi logDPM = DpmMapper.mapToLogDpmEntry(new LogDPMKoreksi(), param);

         return logDPMRepository.saveLogDPM(logDPM)
                 .onItem().invoke(saved -> LOG.info("LogDPM saved with ID: %s", saved))
                 .onFailure().invoke(ex -> LOG.error("Gagal menyimpan LogDPMKoreksi: {}", ex.getMessage(), ex));
     }
     @WithTransaction
     public Uni<APIResponse<Dpm>> EntryStand(EntryStandParam params) {
         return saveLogKoreksi(params)
                 .onItem().transformToUni(savedLog -> {
                     LOG.info("✅ LogDPM berhasil disimpan dengan ID: %s", savedLog);
                     return updateEntryStand(params);
                 })
                 .onItem().transform(updatedDPM -> {
                     LOG.info("✅ Entry Stand berhasil diperbarui untuk IDPEL: %s, THBL: %s",
                             updatedDPM.idpel, updatedDPM.thblrek);
                     return APIResponse.ok(updatedDPM);
                 })
                 .onFailure().transform(ex -> {
                     LOG.error("Gagal memproses EntryStand: %s", ex.getMessage(), ex);
                     return new AppException("Gagal memproses EntryStand: " + ex.getMessage());
                 });
     }     

    public Uni<Dpm> updateEntryStand(EntryStandParam params) {
        // LOG.info("UPDATE DPM : [{}, {}]", params.idpel, params.blth);
        return dpmRepository.getIdpelThbl(params.idpel, params.blth)
                .onItem().transformToUni(dpm -> {
                    if (dpm == null) {
                        throw new AppException("Failed to Store DPM: " + Constant.DATA_TIDAK_DITEMUKAN);
                    }
                    DpmMapper.mapToEntryStand(dpm, params);
                    return dpmRepository.updateDpm(dpm);
                });
    }
    public Uni<APIResponse<Dpm>> findDataEntryStand(String idpel, String thblrek) {
        return sessionFactory.withSession(session ->
            dpmRepository.findByIdpelThblrek(idpel, thblrek)
                .onItem().ifNull().failWith(new AppException(Constant.DATA_TIDAK_DITEMUKAN))
                .onItem().transform(APIResponse::ok)
        );
    }

    private Uni<APIResponse<?>> storeBillingProccess(Dpm dpm) {
        LOG.info("STORE BILLING PROCCESS: {}", dpm.idpel);
        return crmService.getSuplaiDilByIdpel(dpm.idpel, dpm.thblrek)
            .onItem().transformToUni(response -> {
                if (response.isSuccess() && response.getData() != null) {
                    ObjectMapper mapper = new ObjectMapper();
                    String jsonData;
                    SuplaiDILDTO dil;
                    try {
                        jsonData = mapper.writeValueAsString(response.getData());
                        dil = mapper.readValue(jsonData, SuplaiDILDTO.class);
                    } catch (JsonProcessingException e) {
                        throw new AppException("Error: failed to convert object Bill, " + e.getMessage());
                    }
                    return storeBill(dil, dpm);
                } else {
                    throw new AppException("--Failed to GET Suplai DIL: " + response.getMessage());
                }
            });
    }



    public Uni<Void> storeDataToWorkFlowV2_new(CustomerParam param) {
        LOG.info("-- PARAM CUSTOMER : {},{}", param.idpel, param.thblrek);
        return billingService.getInisialisasiBillingByIdpelThblrek(param)
            .onItem().transformToUni(dataInisialisasiBilling -> {
                if (dataInisialisasiBilling == null) {
                    LOG.error("-- data initialization is empty --");
                    return Uni.createFrom().voidItem();
                } else {
                    if (!dataInisialisasiBilling.getDataEnergi().isEmpty()) {
                        return saveLogHitungBilling(param, dataInisialisasiBilling);
                    } else {
                        LOG.warn("-- data energies is empty --");
                        return Uni.createFrom().voidItem();
                    }
                }
            });
    }

    private Uni<Void> saveLogHitungBilling(CustomerParam paramCust, InisialisasiBillingDTO data) {
        return logJobHitungBillingRepository.checkIfExist(paramCust.idpel, paramCust.thblrek).onItem()
            .transformToUni(logHitung -> {
                if (logHitung == null) {
                    LogJobHitungBilling log = new LogJobHitungBilling();
                    log.id = UUID.randomUUID();
                    log.idpel = paramCust.idpel;
                    log.thblrek = paramCust.thblrek;
                    log.payload = getPayload(data);
                    log.status_proses = 0;
                    log.message = "Inisialisasi log successfully saved";
                    log.tglmulaiproses = LocalDateTime.now();
                    log.tglselesaiproses = null;
                    log.unitap = data.unitap;
                    return log.persist();
                } else {
                    logHitung.payload = getPayload(data);
                    logHitung.status_proses = 0;
                    logHitung.message = "Inisialisasi log successfully saved";
                    logHitung.tglmulaiproses = LocalDateTime.now();
                    logHitung.tglselesaiproses = null;
                    return logHitung.persist();
                }
            }).invoke(() -> LOG.info("log initialization successfully saved")).replaceWithVoid();
    }

    public String getPayload(InisialisasiBillingDTO dataInisialisasi) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(dataInisialisasi); // Langsung konversi DTO ke JSON
        } catch (JsonProcessingException e) {
            LOG.error("Error converting DTO to JSON", e);
            return "{}"; // Kembalikan JSON kosong untuk menghindari error lebih lanjut
        }
    }

    private Uni<APIResponse<?>> storeBill(SuplaiDILDTO dil, Dpm dpm) {
        LOG.info("STORE BILL: {}", dil.idpel);
        Integer statusMutasi = Helper.getMutasiPecahan(dil.thblmut, dpm.thblrek, dil.jnsmut);
        LOG.info("STATUS MUTASI : {}", statusMutasi);
        return billingRepository.findByIdpelThblrek(dil.idpel, dpm.thblrek)
                .onItem().transformToUni(bill -> {
                    if (bill != null) {
                        if ("3".equals(bill.postingbilling)) {
                            throw new AppException("Data Billing sudah posting 3");
                        }

                        BillingMapper.mapToBilling(bill, dil, dpm, false);
                        return billingRepository.persistAndFlush(bill);
                    } else {
                        return billingRepository
                                .persistAndFlush(BillingMapper.mapToBilling(new Billing(), dil, dpm, true));
                    }
                })
                .onFailure().transform(ex -> {
                    throw new AppException("Failed to store Bill: " + ex.getMessage());
                }).onItem().transformToUni(savedBill -> {
                    LOG.info("STORE BILL ENERGI: {}", dil.idpel);
                    return billingEnergiRepository.findByIdpelThblrek(dpm.idpel, dpm.thblrek).onItem()
                            .transformToUni(
                                    existingBillingEnergi -> billingEnergiRepository
                                            .findByIdpelThblrekPrev(dpm.idpel, dpm.thblrek)
                                            .onItem().transformToUni(
                                                    prevBillingEnergi -> dpmPecahanRepository
                                                            .getDpmPecahanByIdpelThblrek(dpm.idpel, dpm.thblrek)
                                                            .onItem()
                                                            .transformToUni(lstDpmPecahan -> storeBillEnergiMutasi(
                                                                    lstDpmPecahan, dil, dpm, statusMutasi).onItem()
                                                                    .transformToUni(lstEnergiPecahan -> {
                                                                        BillingEnergiBulanLaluDTO dataPrev = new BillingEnergiBulanLaluDTO();
                                                                        if (prevBillingEnergi != null) {
                                                                            dataPrev.slakvarh_import = prevBillingEnergi.sahkvarhExport != null
                                                                                    ? prevBillingEnergi.sahkvarhExport
                                                                                    : BigDecimal.ZERO;

                                                                            dataPrev.slawbp_import = prevBillingEnergi.sahwbpExport != null
                                                                                    ? prevBillingEnergi.sahwbpExport
                                                                                    : BigDecimal.ZERO;

                                                                            dataPrev.slalwbp_import = prevBillingEnergi.sahlwbpExport != null
                                                                                    ? prevBillingEnergi.sahlwbpExport
                                                                                    : BigDecimal.ZERO;
                                                                        } else {
                                                                            dataPrev.slakvarh_import = BigDecimal.ZERO;
                                                                            dataPrev.slawbp_import = BigDecimal.ZERO;
                                                                            dataPrev.slalwbp_import = BigDecimal.ZERO;
                                                                        }

                                                                        BillingEnergi billingEnergi;

                                                                        if (statusMutasi == 1) {
                                                                            billingEnergi = BillingEnergiMapper
                                                                                    .mapToBillingEnergiPecahan(
                                                                                            Objects.requireNonNullElseGet(
                                                                                                    existingBillingEnergi,
                                                                                                    BillingEnergi::new),
                                                                                            dil,
                                                                                            lstDpmPecahan.getLast(),
                                                                                            lstDpmPecahan, dpm, false);
                                                                        } else {
                                                                            if (existingBillingEnergi != null) {
                                                                                billingEnergi = BillingEnergiMapper
                                                                                        .mapToBilling(
                                                                                                existingBillingEnergi,
                                                                                                dil, dpm, false,
                                                                                                dataPrev);
                                                                            } else {
                                                                                billingEnergi = BillingEnergiMapper
                                                                                        .mapToBilling(
                                                                                                new BillingEnergi(),
                                                                                                dil, dpm, true,
                                                                                                dataPrev);
                                                                            }
                                                                        }

                                                                        LOG.info("energy data has been created, {} {}",
                                                                                billingEnergi.id, billingEnergi.idpel);
                                                                        return billingEnergiRepository
                                                                                .persistAndFlush(billingEnergi);
                                                                    }).onFailure().transform(ex -> {
                                                                        ex.printStackTrace();
                                                                        throw new AppException(
                                                                                "Failed to store Bill Energi: "
                                                                                        + ex.getMessage());
                                                                    }).onItem().transform(APIResponse::ok))));
                });
    }
    
    public Uni<List<BillingEnergi>> storeBillEnergiMutasi(List<DpmPecahan> lstDpmPecahan, SuplaiDILDTO dil, Dpm dpm, Integer statusMutasi) {
        if (statusMutasi.equals(1) && !Helper.isMigrationOrMutationJorK(dil.thblmut, dpm.thblrek, dil.jnsmut, dil.tarifLm)) {
            return billingEnergiRepository.deleteEnergiByIdpelThblrek(dpm.idpel, dpm.thblrek).onItem().transformToUni(deleteEnergi ->
                Multi.createFrom().iterable(lstDpmPecahan).onItem().transformToUniAndConcatenate(dpmPecahan -> {
                    LOG.info("DataPecahan {}", dpmPecahan);
                    BillingEnergi billingEnergi = BillingEnergiMapper.mapToBillingEnergiPecahan(
                        new BillingEnergi(), dil, dpmPecahan,
                        lstDpmPecahan, dpm, true
                    );
                    LOG.info("Energi {} {}", billingEnergi.id, billingEnergi.idpel);
                    return billingEnergiRepository.persistAndFlush(billingEnergi);
                }).collect().asList()
            );
        }
        return Uni.createFrom().nullItem();
    }
      
}
