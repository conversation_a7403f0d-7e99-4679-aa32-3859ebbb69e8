package com.iconplus.ap2t.generator.service;

import com.iconplus.ap2t.common.param.billing.ClosingMutasiParam;
import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.data.repository.master.MasterKdProsesReposiroty;
import com.iconplus.ap2t.generator.proxy.Ap2tOldProxy;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * Date: 20/06/2025
 * Time: 11:06
 */
@ApplicationScoped
public class ClosingMutasiService {

    Logger LOG = LoggerFactory.getLogger(ClosingMutasiService.class.getName());

    @Inject
    @RestClient
    Ap2tOldProxy proxy;

    @Inject
    private MasterKdProsesReposiroty masterKdProsesReposiroty;

//    @WithTransaction
//    public Uni<APIResponse<?>> closingMutasi(ClosingMutasiParam param) {
//        LOG.info("Terima request closingMutasi, langsung kirim response...");
//
//        // Jalankan proses berat secara async (fire-and-forget)
//        startAsyncClosing(param);
//
//        // Langsung respon "Sedang diproses"
//        return Uni.createFrom().item(APIResponse.ok("Permintaan sedang diproses"));
//    }
//
//    public void startAsyncClosing(ClosingMutasiParam param) {
//        proxy.closingMutasi(param)
//                .subscribe().with(proxyResponse -> {
//                    APIResponse<?> responseEntity = proxyResponse.readEntity(APIResponse.class);
//
//                    if (!responseEntity.isSuccess()) {
//                        LOG.error("Proxy gagal: {}", responseEntity.getMessage());
//                        return;
//                    }
//
//                    // Buka session baru untuk akses DB di background thread
//                    Panache.withTransaction(() ->
//                            masterKdProsesReposiroty.findKdKlpByFirstResult(param.getUnitUp(), param.getThblrek())
//                                    .onItem().ifNotNull().transformToUni(closingData -> {
//                                        closingData.suplaiDil = "1";
//                                        closingData.tglSuplaiDil = LocalDateTime.now();
//                                        closingData.bySuplaiDil = param.getUserId();
//
//                                        return masterKdProsesReposiroty.persistAndFlush(closingData)
//                                                .invoke(() -> LOG.info("Update berhasil"))
//                                                .replaceWithVoid();
//                                    })
//                                    .onFailure().invoke(e -> LOG.error("Error saat update DB async", e))
//                    ).subscribe().with(
//                            unused -> {},
//                            err -> LOG.error("Gagal buka session async", err)
//                    );
//
//                }, err -> LOG.error("Gagal panggil proxy", err));
//    }

    @WithSession
    public Uni<APIResponse<?>> closingMutasi(ClosingMutasiParam param) {
        LOG.info("Processing closing mutasi for unitUp: {}, thblrek: {}, userId: {}",
                param.getUnitUp(), param.getThblrek(), param.getUserId());

        return proxy.closingMutasi(param)
                .onItem().transformToUni(proxyResponse -> {
                    APIResponse<?> responseEntity = proxyResponse.readEntity(APIResponse.class);

                    if (!responseEntity.isSuccess()) {
                        LOG.error("Failed in proxy closing mutasi: {}", responseEntity.getMessage());
                        return Uni.createFrom().item(responseEntity);
                    }

                    // Proses update database
                    return masterKdProsesReposiroty.findKdKlpByFirstResult(param.getUnitUp(), param.getThblrek(), param.getKdProsesKlp(), param.getUnitAp())
                            .onItem().transformToUni(closingData -> {
                                if (closingData == null) {
                                    LOG.warn("MasterKodeProses not found for unitUp: {}, thblrek: {},kdproseKlp: {}",
                                            param.getUnitUp(), param.getThblrek(),param.getKdProsesKlp());
                                    return Uni.createFrom().item(
                                            APIResponse.error("Data MasterKodeProses tidak ditemukan")
                                    );
                                }

                                // Update data
                                closingData.suplaiDil = "1";
                                closingData.tglSuplaiDil = LocalDateTime.now();
                                closingData.bySuplaiDil = param.getUserId();

                                return masterKdProsesReposiroty.persistAndFlush(closingData)
                                        .onItem().transform(updated -> {
                                            LOG.info("Successfully updated MasterKodeProses");
                                            return responseEntity; // Kembalikan response dari proxy
                                        });
                            });
                })
                .onFailure().recoverWithItem(failure -> {
                    LOG.error("Unexpected error in closing mutasi process", failure);
                    return APIResponse.error(failure.getMessage());
                });
    }


}
