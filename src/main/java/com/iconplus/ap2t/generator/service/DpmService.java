package com.iconplus.ap2t.generator.service;

import com.iconplus.ap2t.common.param.billing.CrmDpmParam;
import com.iconplus.ap2t.common.utility.Helper;
import com.iconplus.ap2t.data.entity.billing.Dpm;
import com.iconplus.ap2t.data.entity.log.LogBillingEnergi;
import com.iconplus.ap2t.data.repository.billing.*;
import com.iconplus.ap2t.data.repository.log.LogBillingEnergiBrutoRepository;
import com.iconplus.ap2t.data.repository.log.LogBillingEnergiRepository;
import com.iconplus.ap2t.data.repository.log.LogBillingRepository;
import com.iconplus.ap2t.generator.mapper.BillingEnergiBrutoMapper;
import com.iconplus.ap2t.generator.mapper.BillingEnergiMapper;
import com.iconplus.ap2t.generator.mapper.BillingMapper;
import com.iconplus.ap2t.generator.mapper.DpmMapper;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.hibernate.reactive.mutiny.Mutiny;

import java.math.BigDecimal;

@ApplicationScoped
public class DpmService {

    @Inject
    BillingRepository billingRepository;

    @Inject
    BillingEnergiRepository billingEnergiRepository;

    @Inject
    BillingEnergiBrutoRepository billingEnergiBrutoRepository;

    @Inject
    LogBillingRepository logBillingRepository;

    @Inject
    LogBillingEnergiRepository logBillingEnergiRepository;

    @Inject
    LogBillingEnergiBrutoRepository logBillingEnergiBrutoRepository;

    @Inject
    DpmRepository dpmRepository;

    @Inject
    Mutiny.SessionFactory sessionFactory;

    public Uni<Dpm> saveDpmFromCrm(CrmDpmParam req) {
        return sessionFactory.withTransaction(session ->
            dpmRepository.findByIdpelThblrek(req.getIdpel(), req.getThblrek())
                .onItem().transformToUni(dpm -> {
                    if (dpm == null) {
                        dpm = new Dpm();
                        dpm.id = req.getIdpel() + req.getThblrek();
                        dpm.idpel = req.getIdpel();
                        dpm.thblrek = req.getThblrek();
                        dpm.mutasi_ke = BigDecimal.ZERO;
                        return dpmRepository.persistAndFlush(DpmMapper.toEntity(dpm, req));
                    }

                    return session.merge(DpmMapper.toEntity(dpm, req));
                }).onItem().transformToUni(dpm ->
                    billingRepository.findByIdpelThblrek(req.getIdpel(), req.getThblrek())
                        .onItem().transformToUni(billing -> {
                            if (billing != null && Helper.isBongkar(req.getJenisMk())) {
                                return logBillingRepository.persist(BillingMapper.toLogEntity(billing))
                                    .onItem().transformToUni(logBilling ->
                                        billingEnergiRepository.findChild(req.getIdpel(), req.getThblrek())
                                            .onItem().ifNotNull().transformToMulti(bes -> Multi.createFrom().iterable(bes))
                                            .onItem().transformToUniAndConcatenate(be ->
                                                logBillingEnergiRepository.persist(BillingEnergiMapper.mapLogBillingEnergi(new LogBillingEnergi(), be))
                                                    .onItem().transformToUni(logBillingEnergi ->
                                                        billingEnergiBrutoRepository.findById(logBillingEnergi.billId)
                                                            .onItem().ifNotNull()
                                                            .transformToUni(beb ->
                                                                logBillingEnergiBrutoRepository.persist(BillingEnergiBrutoMapper.toLogEntity(beb))
                                                                    .onItem().transformToUni(lbeb -> beb.delete())
                                                            )
                                                    ).onItem().transformToUni(voids -> be.delete())
                                            ).collect().asList()
                                            .onItem().transformToUni(vod -> billing.delete())
                                            .onItem().transformToUni(vod -> session.flush())
                                    ).replaceWith(dpm);
                            }
                            return Uni.createFrom().item(dpm);
                        })
                )
        );
    }

}
