package com.iconplus.ap2t.generator.service;

import com.iconplus.ap2t.common.response.customer.CRMResponse;
import com.iconplus.ap2t.generator.proxy.CrmProxy;
import com.iconplus.ap2t.generator.response.SuplaiDILDTO;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */

@ApplicationScoped
public class CrmService {

    Logger LOG = LoggerFactory.getLogger(CrmService.class.getName());

    @Inject
    @RestClient
    private CrmProxy proxy;

    public Uni<CRMResponse<SuplaiDILDTO>> getSuplaiDilByIdpel(String idpel, String thblrek) {
        LOG.info("GET SUPLAI DIL [{}, {}]", idpel, thblrek);
        return proxy.getSuplaiDilByIdpel(idpel, thblrek)
                .onItem().transform(response -> response.readEntity(CRMResponse.class));
    }

    public Uni<CRMResponse<?>> getStandMeterByIdpel(String idpel) {
        LOG.info("GET STAND METER DIL [{}]", idpel);
        return proxy.getStandMeterByIdpel(idpel).onItem().transform(response -> {
            LOG.info("result crm integration : {}", response.getStatus());
            return response.readEntity(CRMResponse.class);
        });
    }
}
