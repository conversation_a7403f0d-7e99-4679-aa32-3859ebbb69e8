package com.iconplus.ap2t.generator.service;

import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.data.entity.master.MasterKodeProses;
import com.iconplus.ap2t.data.repository.master.MasterKdProsesReposiroty;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.List;

@Singleton
public class MasterKdProsesService {

    @Inject
    MasterKdProsesReposiroty reposiroty;

    @WithSession
    public Uni<APIResponse<List<MasterKodeProses>>> getKdKlpByUnitupAndBlth(String unitup, String blth) {
        return reposiroty.findKdKlpByUnitupAndBlth(unitup, blth).onItem().transform(APIResponse::ok)
            .onFailure().recoverWithItem(ex -> {
                ex.printStackTrace();
                return APIResponse.error(ex.getMessage());
            });
    }
}
