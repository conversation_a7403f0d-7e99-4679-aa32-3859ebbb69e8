package com.iconplus.ap2t.generator.service;


import com.iconplus.ap2t.common.exception.AppException;
import com.iconplus.ap2t.common.param.*;
import com.iconplus.ap2t.common.param.billing.*;
import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.common.utility.Constant;
import com.iconplus.ap2t.common.utility.Helper;
import com.iconplus.ap2t.data.entity.billing.*;
import com.iconplus.ap2t.data.entity.log.LogBillingEnergi;
import com.iconplus.ap2t.data.repository.billing.*;
import com.iconplus.ap2t.data.repository.log.LogBillingEnergiRepository;
import com.iconplus.ap2t.data.repository.master.*;
import com.iconplus.ap2t.generator.dto.*;
import com.iconplus.ap2t.generator.mapper.*;
import com.iconplus.ap2t.generator.param.KoreksiBillingParam;
import com.iconplus.ap2t.generator.param.KoreksiBillingPltsParam;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.hibernate.reactive.mutiny.Mutiny;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ApplicationScoped
public class BillingServiceOptimize {
    Logger LOG = LoggerFactory.getLogger(BillingServiceOptimize.class.getName());

    @Inject
    BillingRepository billingRepository;

    @Inject
    BillingEnergiRepository energiRepository;

    @Inject
    MasterTarifRepository masterTarifRepository;

    @Inject
    FjnRepository fjnRepository;

    @Inject
    FrtRepository frtRepository;

    @Inject
    PpjRepository ppjRepository;

    @Inject
    MasterTarifKdpt2Repository masterTarifKdpt2Repository;

    @Inject
    MasterBkRepository masterBkRepository;

    @Inject
    MasterPlgUapRepository masterPlgUapRepository;

    @Inject
    MasterPlgOperasiParalelRepository masterPlgOperasiParalelRepository;

    @Inject
    MasterTarifBptrafoRepository masterTarifBptrafoRepository;

    @Inject
    MasterPlgRecTaglisRepository masterPlgRecTaglisRepository;

    @Inject
    MasterSaldoTmpRepository masterSaldoTmpRepository;

    @Inject
    MasterDiskonRepository masterDiskonRepository;

    @Inject
    BillingEnergiRepository billingEnergiRepository;

    @Inject
    MasterPlgPksRepository masterPlgPksRepository;

    @Inject
    MasterDiskonStimulusRepository masterDiskonStimulusRepository;

    @Inject
    DilPltsAtapRepository dilPltsAtapRepository;

    @Inject
    DilPltsAtapNewRepository dilPltsAtapNewRepository;

    @Inject
    MasterPlgPltsAtapRepository masterPlgPltsAtapRepository;

    @Inject
    MasterDiskonEvRepository masterDiskonEvRepository;

    @Inject
    DilTahapRepository dilTahapRepository;

    @Inject
    DataTerimaKomporInduksiRepository dataTerimaKomporInduksiRepository;

    @Inject
    DataTerimaKomporPegawaiRepository dataTerimaKomporPegawaiRepository;

    @Inject
    MasterDppPpnRepository masterDppPpnRepository;

    @Inject
    BillingEnergiBrutoRepository billingEnergiBrutoRepository;

    @Inject
    BillDppPpnRepository billDppPpnRepository;

    @Inject
    FsmService fsmService;

    @Inject
    Mutiny.SessionFactory sessionFactory;

    @Inject
    LogBillingEnergiRepository logBillingEnergiRepository;
    @Inject
    Billing720Repository billing720Repository;

    @WithTransaction
    public Uni<Billing> save(Billing billing) {
        LOG.info("SAVE DATA BILLS : {}", billing.idpel);
        return billingRepository.persistAndFlush(billing);
    }

    public Uni<APIResponse<Billing>> findByIdpelThblrek(String idpel, String thblrek) {
        LOG.info("GET DATA BILLS BY IDPEL : [{} - {}]", idpel, thblrek);
        return sessionFactory.withSession(session -> billingRepository.findByIdpelThblrek(idpel, thblrek)
            .onItem().ifNotNull().transform(APIResponse::ok)
            .onItem().ifNull().failWith(new AppException(Constant.DATA_TIDAK_DITEMUKAN)));
    }

    @WithTransaction
    public Uni<APIResponse<?>> findDataBill(String idpel, String thblrek) {
        LOG.info("[Start Service findDataBill : {} - {}]", idpel, thblrek);
        return billingRepository.findByIdpelThblrek(idpel, thblrek)
            .onItem().ifNotNull().transformToUni(bill -> {
                Uni<BillingDTO> response = BillingMapper.mapFromEntity(bill);
                LOG.info("[End Service findDataBill]");
                return APIResponse.ok(response);
            }).onItem().ifNull().failWith(new AppException(Constant.DATA_TIDAK_DITEMUKAN));
    }


    public Uni<InisialisasiBillingDTO> getInisialisasiBillingByIdpelThblrek(CustomerParam param) {
        Instant startProcess = Instant.now();
        return billingRepository.findByIdpelThblrek(param.idpel, param.thblrek)
            .onItem().ifNotNull().transformToUni(item -> masterTarifRepository.findByKodedaya(item.tarif, item.kdpt, item.daya.longValue())
            .onItem().transformToUni(masterTarif -> masterTarifKdpt2Repository.getMasterTarifKdpt2(item.tarif, item.kdpt_2, item.daya, param.thblrek)
            .onItem().transformToUni(masterTarifKdpt2 -> masterBkRepository.getMasterBkByTarifDaya(item.tarif, item.daya, param.thblrek)
            .onItem().transformToUni(masterBk -> fjnRepository.getFjnValue(item.fjn)
            .onItem().transformToUni(fjn -> masterPlgUapRepository.getRpUap(param.idpel, param.thblrek)
            .onItem().transformToUni(rpuap -> masterPlgOperasiParalelRepository.getMasterPlgOperasiParalelByIdpel(param.idpel, param.thblrek)
            .onItem().transformToUni(masterPlgOperasi -> masterTarifBptrafoRepository.getMasterTarifBptrafo(item.unitupi, item.tarif, item.kdbpt)
            .onItem().transformToUni(masterTarifBptrafo -> masterPlgRecTaglisRepository.getMasterPlgRecTaglisByIdpel(item.idpel, param.thblrek)
            .onItem().transformToUni(masterPlgRec -> masterPlgPksRepository.getMasterPlgPksByIdpelThblrek(item.idpel, param.thblrek)
            .onItem().transformToUni(masterPlgPks -> masterDiskonRepository.getMasterDiskonByIdpelThbl(item.idpel, param.thblrek)
            .onItem().transformToUni(masterDiskon -> masterSaldoTmpRepository.getMasterSaldoTmpByIdpel(item.idpel)
            .onItem().transformToUni(masterSaldoTmp -> getDataEnergi(param.idpel, param.thblrek, item)
            .onItem().transformToUni(energies -> ppjRepository.getProsentasePPJ(item.tarif, item.pemda, item.unitupi, item.daya.longValue(), item.thblrek)
            .onItem().transformToUni(prosenPpj -> masterDiskonEvRepository.findByIdpelAndThbl(param.idpel, param.thblrek)
            .onItem().transformToUni(masterDiskonEv -> dataTerimaKomporInduksiRepository.findByIdpelAndThblrek(param.idpel, param.thblrek)
            .onItem().transformToUni(dataTerimaKomporInduksi -> dataTerimaKomporPegawaiRepository.findByIdpelAndThblrek(param.idpel, param.thblrek)
            .onItem().transformToUni(dataTerimaKomporPegawai -> frtRepository.getFrtValue(item.frt)
            .onItem().transformToUni(frt -> masterDppPpnRepository.findByThbl(param.thblrek)
            .onItem().transform(masterDppPpn -> {
                InisialisasiBillingDTO initial = new InisialisasiBillingDTO();
                initial.id = item.id;
                initial.idpel = item.idpel;
                initial.thblrek = item.thblrek;
                initial.kdprosesklp = item.kdprosesklp;
                initial.unitupi = item.unitupi;
                initial.unitap = item.unitap;
                initial.unitup = item.unitup;
                initial.jnsmut = item.jnsmut;
                initial.kdmut = item.kdmut;
                initial.thblmut = item.thblmut;
                initial.tglnyala = item.tglnyala.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                initial.tglrubah = item.tglrubah.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                initial.kdproses = item.kdproses;
                initial.nopel = item.nopel;
                initial.nama = item.nama;
                initial.tarif = item.tarif;
                initial.kdpt = item.kdpt;
                initial.kdpt2 = item.kdpt_2;
                initial.daya = item.daya;
                initial.faknpremium = item.faknpremium;
                initial.dayajbst = item.dayajbst;
                initial.kdbedajbst = item.kdbedajbst;
                initial.kddk = item.kddk;
                initial.kdbacameter = item.kdbacameter;
                initial.kdmeterai = item.kdmeterai;
                initial.lokettgk = item.lokettgk;
                initial.kogol = item.kogol;
                initial.subkogol = item.subkogol;
                initial.pemda = item.pemda;
                initial.kdppj = item.kdppj;
                initial.kdinkaso = item.kdinkaso;
                initial.kdklp = item.kdklp;
                initial.kdind = item.kdind;
                initial.kdam = item.kdam;
                initial.kdkvamaks = item.kdkvamaks;
                initial.maxDemand = item.maxDemand;
                initial.frt = item.frt;
                initial.fjn = item.fjn;
                initial.kdbpt = item.kdbpt;
                initial.dayabpt = item.dayabpt;
                initial.kddayabpt = item.kddayabpt;
                initial.kdpembmeter = item.kdpembmeter;
                initial.fakm = item.fakm;
                initial.fakmkvarh = item.fakmkvarh;
                initial.fakmkvam = item.fakmkvam;
                initial.rpsewatrafoDil = item.rpsewatrafoDil;
                initial.flagsewakap = item.flagsewakap;
                initial.faradkap = item.faradkap;
                initial.rpsewakap = item.rpsewakap;
                initial.kdangsa = item.kdangsa;
                initial.rpangsa = item.rpangsa;
                initial.lamaangsa = item.lamaangsa;
                initial.thblangs1a = item.thblangs1a;
                initial.angskea = item.angskea;
                initial.kdangsb = item.kdangsb;
                initial.rpangsb = item.rpangsb;
                initial.lamaangsb = item.lamaangsb;
                initial.thblangs1b = item.thblangs1b;
                initial.angskeb = item.angskeb;
                initial.kdangsc = item.kdangsc;
                initial.rpangsc = item.rpangsc;
                initial.lamaangsc = item.lamaangsc;
                initial.thblangs1c = item.thblangs1c;
                initial.angskec = item.angskec;
                initial.kdinvoice = item.kdinvoice;
                initial.jnsmutAde = item.jnsmutAde;
                initial.tahunKe = item.tahunKe;
                initial.flagppjangsa = item.flagppjangsa;
                initial.flagppjangsb = item.flagppjangsb;
                initial.flagppjangsc = item.flagppjangsc;
                initial.sahlwbp = item.sahlwbp;
                initial.sahwbp = item.sahwbp;
                initial.sahkvarh = item.sahkvarh;
                initial.slalwbp = item.slalwbp;
                initial.slawbp = item.slawbp;
                initial.slakvarh = item.slakvarh;
                initial.dayamaxWbp = item.dayamaxWbp;
                initial.tglbacalalu = item.tglbacalalu;
                initial.tglbacaakhir = item.tglbacaakhir;
                initial.faktorFjn = fjn != null ? new BigDecimal(fjn) : null;
                if (masterTarif != null && masterTarif.faktorFrt.equals("N")) {
                    initial.faktorFrt = BigDecimal.ONE;
                } else {
                    initial.faktorFrt = frt != null ? new BigDecimal(frt) : null;
                }
                initial.faktorFakm = item.fakm;
                initial.dayaMax = item.dayamaks;
                initial.prosenPpj = prosenPpj;
                initial.prosenPpn = new BigDecimal((Double.parseDouble(item.thblrek) > Double.parseDouble("202203") ? 11 : 10));

                if (item.kdpt != null && item.kdpt.equals("A")) {
            //                        initial.blok = masterPlgPks.get;
            //                        initial.batasBlok1 = masterPlgPks.batasBlok1;
            //                        initial.batasBlok2 = masterPlgPks.batasBlok2;
                    initial.biayaBeban = masterPlgPks.trfBeban;
                    initial.biayaPakai1 = masterPlgPks.trfLwbp;
                    initial.biayaPakai2 = masterPlgPks.trfWbp;
                    initial.biayaPakai3 = BigDecimal.ZERO;
                    initial.biayaKvar = masterPlgPks.trfKvarh;
                    if (masterPlgPks.ketKvarh != null && masterPlgPks.ketKvarh.equals("reguler")) {
                        initial.biayaKvar = masterTarif.bkVar;
                    }

                    initial.jenisBatas = masterPlgPks.jnsBatas.toLowerCase();
                    initial.statusEmin = String.valueOf(masterPlgPks.statusEmin);
                    if (initial.statusEmin.equals("0")) {
                        initial.emin = masterPlgPks.rm.doubleValue();
                    } else {
                        initial.emin = masterPlgPks.emin.doubleValue();
                    }

                    initial.jenisBatasEmin = masterPlgPks.jnsBatas.toLowerCase();
                    initial.metKwh = masterTarif.meterKwh;
                    initial.metKvarh = masterTarif.meterKvarh;
                    initial.metKvamaks = masterTarif.meterKavamaks;
                    initial.faktorKValue = masterPlgPks.faktorkValue;
                    //default 1 untuk faktor N dan Q
                    initial.faktorNValue = BigDecimal.valueOf(1.0);//masterPlgPks.getFaktornValue();
                    initial.faktorQValue = BigDecimal.valueOf(1.0);//masterPlgPks.faktorQValue;
                    initial.faktorPValue = masterPlgPks.faktorpValue;
                    initial.workflowName = masterPlgPks.workflowName;
                    initial.workflowVersion = masterPlgPks.workflowVersion;
                } else {
                    initial.blok = masterTarif.blok;
                    initial.batasBlok1 = masterTarif.batasBlok1;
                    initial.batasBlok2 = masterTarif.batasBlok2;
                    initial.biayaBeban = masterTarif.biayaBeban;
                    initial.biayaPakai1 = masterTarif.biayaPakai1;
                    initial.biayaPakai2 = masterTarif.biayaPakai2;
                    initial.biayaPakai3 = masterTarif.biayaPakai3;
                    initial.biayaKvar = masterTarif.bkVar;
                    initial.jenisBatas = masterTarif.jenisBatas;
                    initial.emin = masterTarif.emin;
                    initial.statusEmin = String.valueOf(masterTarif.statusEmin);
                    initial.jenisBatasEmin = masterTarif.jenisBatasEmin;
                    initial.metKwh = masterTarif.meterKwh;
                    initial.metKvarh = masterTarif.meterKvarh;
                    initial.metKvamaks = masterTarif.meterKavamaks;
                    initial.faktorKValue = masterTarif.faktorKValue;
                    initial.faktorNValue = masterTarif.faktorNValue;
                    initial.faktorPValue = masterTarif.faktorPValue;
                    initial.faktorQValue = masterTarif.faktorQValue;
                    initial.workflowName = masterTarif.workflowName;
                    initial.workflowVersion = masterTarif.workflowVersion;
                }

                initial.rpuap = rpuap;

                if (masterTarifKdpt2 != null) {
                    if (masterTarifKdpt2.keterangan != null && masterTarifKdpt2.keterangan.equals("TANPA METER")) {
                        initial.jamNyalaTanpaMeter = BigDecimal.valueOf(Integer.parseInt(masterTarifKdpt2.pembtrf_2));
                        initial.tglbacaakhir = item.thblrek + "01";
                    } else {
                        initial.jamNyalaTanpaMeter = BigDecimal.ZERO;
                    }
                    if (item.tarif.equals("C")) {
                        initial.faktorQValue = BigDecimal.valueOf(Double.parseDouble(masterTarifKdpt2.pembtrf_2));
                    }
                } else {
                    initial.jamNyalaTanpaMeter = BigDecimal.ZERO;
                }

                if (masterPlgOperasi != null) {
                    if (masterPlgOperasi.getKosphi() != null) {
                        initial.rpoperasiparalel = masterPlgOperasi.getEmin().multiply(initial.biayaPakai1)
                            .multiply(BigDecimal.valueOf(initial.daya).divide(new BigDecimal(1000)))
                            .divide(masterPlgOperasi.getKosphi(), RoundingMode.HALF_UP)
                            .setScale(0, RoundingMode.HALF_UP);
                    }
                } else {
                    initial.rpoperasiparalel = BigDecimal.ZERO;
                }

                initial.rptarifbptrafo = BigDecimal.ZERO;
                if (masterTarifBptrafo != null) {
                    if (masterTarifBptrafo.getKdbpt() != null) {
                        if (masterTarifBptrafo.getKdbpt().equalsIgnoreCase("b")) {
                            initial.rptarifbptrafo = masterTarifBptrafo.getRupiah();
                        }
                    }
                }

                initial.flagTypePembelianRec = 0;
                if (masterPlgRec != null) {
                    if (masterPlgRec.getTypePembelian().equals("PROSENTASE")) {
                        initial.flagTypePembelianRec = 2;
                    } else if (masterPlgRec.getTypePembelian().equals("FIXED")) {
                        initial.flagTypePembelianRec = 1;
                    }
                    initial.jmlunitRec = masterPlgRec.getJmlunitRec().intValue();
                    initial.hargaRec = masterPlgRec.getHargaRec();
                    initial.prosentaseRec = masterPlgRec.getProsentase();
                }
                initial.rptmp = BigDecimal.ZERO;
                if (masterDiskon != null) {
                    initial.rptmp = masterDiskon.getRpdiskon();
                }
                initial.rpsaldotmp = BigDecimal.ZERO;
                if (masterSaldoTmp != null) {
                    initial.rpsaldotmp = masterSaldoTmp.getRpSaldo();
                }
                if (masterBk != null) {
                    initial.jnsbk = masterBk.getJnsbk();
                    initial.statusBkMin = masterBk.getStatusBkMin();
                    initial.rpBk = masterBk.getRpBk();
                    initial.prosenBk = masterBk.getProsenBk();
                    initial.rpMinBk = masterBk.getRpMinBk();
                }

                if (!energies.isEmpty()) {
                    initial.setDataEnergi(energies);
                }

                initial.rpdiskonev = masterDiskonEv != null ? masterDiskonEv.getRpDiskon() : BigDecimal.ZERO;
                initial.pecahan = getMutasiPecahan(item.thblmut, item.thblrek, item.jnsmut);

                BillingKomporParam kompor = new BillingKomporParam();

                initial.flagKomporInduksi = dataTerimaKomporInduksi != null;
                KomporInduksiParam komporInduksiDto = new KomporInduksiParam();
                if (initial.flagKomporInduksi) {
                    komporInduksiDto.thblrek = dataTerimaKomporInduksi.thblrek;
                    komporInduksiDto.idpel = dataTerimaKomporInduksi.idpel;
                    komporInduksiDto.id_kompor = dataTerimaKomporInduksi.idKompor;
                    komporInduksiDto.kwhBaca = dataTerimaKomporInduksi.kwhBaca;
                    komporInduksiDto.kwhSubsidi = dataTerimaKomporInduksi.kwhSubsidi;
                    komporInduksiDto.kwhNonSubsidi = dataTerimaKomporInduksi.kwhNonSubsidi;
                    komporInduksiDto.rpSubsidi = dataTerimaKomporInduksi.rpSubsidi;
                    komporInduksiDto.rpNonSubsidi = dataTerimaKomporInduksi.rpNonSubsidi;
                    komporInduksiDto.rpTotal = dataTerimaKomporInduksi.rpTotal;
                }
                kompor.komporInduksi = komporInduksiDto;

                initial.flagKomporPegawai = dataTerimaKomporPegawai != null;
                KomporPegawaiParam komporPegawaiDto = new KomporPegawaiParam();
                if (initial.flagKomporPegawai) {
                    komporPegawaiDto.kwhSubsidi = dataTerimaKomporPegawai.kwhSubsidi;
                    komporPegawaiDto.kwhNonSubsidi = dataTerimaKomporPegawai.kwhNonSubsidi;
                    komporPegawaiDto.rpSubsidi = dataTerimaKomporPegawai.rpSubsidi;
                    komporPegawaiDto.rpNonSubsidi = dataTerimaKomporPegawai.rpNonSubsidi;
                }
                kompor.komporPegawai = komporPegawaiDto;

                initial.setKompor(kompor);;

                if(masterDppPpn != null) {
                    initial.flagProsenPpnNew = !masterDppPpn.valueA.equals(masterDppPpn.valueB) ? 1 : 0;
                    initial.pembilangPpn = masterDppPpn.valueA;
                    initial.penyebutPpn = masterDppPpn.valueB;
                    initial.prosenPpnNew = masterDppPpn.prosen;
                } else {
                    initial.flagProsenPpnNew = 0;
                }

                Instant endProcess = Instant.now();
                long duration = Duration.between(startProcess, endProcess).getNano() / 1_000_000;
                LOG.info("Waktu Inisialisasi : {} ms", duration);
                return initial;
            })
        ))))))))))))))))));
    }

    public Uni<List<DataEnergi>> getDataEnergi(String idpel, String thblrek, Billing billing) {
        return sessionFactory.withSession(session -> energiRepository.findChild(idpel, thblrek).onItem().transformToMulti(items -> Multi.createFrom().iterable(items)
            .onItem().transformToUniAndConcatenate(item -> masterTarifKdpt2Repository.getMasterTarifKdpt2(item.tarif, item.kdpt2, item.daya, item.thblrek)
                .onItem().transformToUni(masterTarifKdpt2 -> dilTahapRepository.getDilTahapBillingByIdpel(item.idpel)
                    .onItem().transformToUni(dilTahapBillingDTO -> masterTarifRepository.findMasterTarifByTarifDaya(item.tarif, item.kdpt, item.daya.longValue(), item.kdpt2, masterTarifKdpt2, dilTahapBillingDTO)
                        .onItem().transformToUni(masterTarif -> masterDiskonStimulusRepository.getMasterTarifDiskonStimulus(item.tarif, item.daya, item.thblrek)
                            .onItem().transformToUni(masterDiskonStimulus -> masterPlgPksRepository.getMasterPlgPksByIdpelThblrek(item.idpel, item.thblrek)
                                .onItem().transformToUni(masterPlgPks -> dilPltsAtapRepository.getDilPltsAtapByIdpel(item.idpel)
                                    .onItem().transformToUni(dilPltsAtap -> dilPltsAtapNewRepository.getDilPltsAtapNewByIdpel(item.idpel)
                                        .onItem().transformToUni(dilPltsAtapNew -> masterPlgPltsAtapRepository.getMasterPlgPltsAtapByIdpel(item.idpel)
                                            .onItem().transformToUni(masterPlgPltsAtap -> fjnRepository.getFjnValue(item.fjn)
                                                .onItem().transformToUni(fjn -> frtRepository.getFrtValue(item.frt).onItem().transform(frtValue -> {
                                                    LOG.info("Data Energi {} : {}", item.pecahKe, item.toString());
                                                    DataEnergi param = new DataEnergi();
                                                    param.id = item.id;
                                                    param.pecahKe = item.pecahKe;
                                                    param.kdmut = item.kdmut;
                                                    param.jnsmut = item.jnsmut;
                                                    param.thblmut = item.thblmut;
                                                    param.thblrek = item.thblrek;
                                                    param.tglrubah = item.tglrubah.toString();
                                                    param.idpel = item.idpel;
                                                    param.tglBacaLalu = item.tglbacalalu;
                                                    param.tglBacaAkhir = item.tglbacaakhir;
                                                    param.tglBacaBulanLalu = billing.tglbacalalu;
                                                    param.tglbacabulanIni = billing.tglbacaakhir;
                                                    param.sahlwbp = item.sahlwbp == null ? BigDecimal.ZERO : item.sahlwbp;
                                                    param.sahwbp = item.sahwbp == null ? BigDecimal.ZERO : item.sahwbp;
                                                    param.sahkvarh = item.sahkvarh == null ? BigDecimal.ZERO : item.sahkvarh;
                                                    param.slalwbp = item.slalwbp == null ? BigDecimal.ZERO : item.slalwbp;
                                                    param.slawbp = item.slawbp == null ? BigDecimal.ZERO : item.slawbp;
                                                    param.slakvarh = item.slakvarh == null ? BigDecimal.ZERO : item.slakvarh;
                                                    param.dayamaxWbp = item.dayamaxWbp;
                                                    param.tarif = item.tarif;
                                                    param.daya = item.daya.intValue();
                                                    param.fakm = item.fakm;
                                                    param.fakmkvarh = item.fakmkvarh;
                                                    param.fakmkvam = item.fakmkvam;
                                                    param.faktorFjn = fjn;
                                                    param.faktorFakm = item.fakm;
                                                    if (masterTarif != null && masterTarif.faktorFrt.equals("N")) {
                                                        param.faktorFrt = 1.0;
                                                    } else {
                                                        param.faktorFrt = item.frt != null ? frtValue : null;
                                                    }

                                                    if (item.kdpt != null && item.kdpt.equals("A")) {
                                                        LOG.info("Data PKS {}", masterPlgPks);
                                                        LOG.info("Data Master Tarif {}", masterTarif);
//                                                                        initial.blok = masterPlgPks.get;
//                                                                        initial.batasBlok1 = masterPlgPks.batasBlok1;
//                                                                        initial.batasBlok2 = masterPlgPks.batasBlok2;
                                                        param.biayaBeban = masterPlgPks.trfBeban;
                                                        param.biayaPakai1 = masterPlgPks.trfLwbp;
                                                        param.biayaPakai2 = masterPlgPks.trfWbp;
                                                        param.biayaPakai3 = BigDecimal.ZERO;
                                                        param.biayaKvar = masterPlgPks.trfKvarh;
                                                        if (masterPlgPks.ketKvarh != null && masterPlgPks.ketKvarh.equals("reguler")) {
                                                            param.biayaKvar = masterTarif.bkVar;
                                                        }

                                                        param.jenisBatas = masterPlgPks.jnsBatas.toLowerCase();
                                                        param.statusEmin = masterPlgPks.statusEmin;
                                                        if (param.statusEmin == 0) {
                                                            param.emin = masterPlgPks.rm.doubleValue();
                                                        } else {
                                                            param.emin = masterPlgPks.emin.doubleValue();
                                                        }

                                                        param.jenisBatasEmin = masterPlgPks.jnsBatas.toLowerCase();
                                                        param.metKwh = masterTarif.meterKwh;
                                                        param.metKvarh = masterTarif.meterKvarh;
                                                        param.metKvamaks = masterTarif.meterKavamaks;
                                                        param.faktorKValue = masterPlgPks.faktorkValue;
                                                        //default 1 untuk faktor N dan Q
                                                        param.faktorNValue = BigDecimal.valueOf(1.0);//masterPlgPks.getFaktornValue();
                                                        param.faktorQValue = BigDecimal.valueOf(1.0);//masterPlgPks.faktorQValue;
                                                        param.faktorPValue = masterPlgPks.faktorpValue;
                                                        param.workflowNameEnergi = masterPlgPks.workflowMutasi;
                                                        param.workflowVersionEnergi = masterPlgPks.workflowMutasiVersion;
                                                    } else {
                                                        param.blok = masterTarif.blok;
                                                        param.batasBlok1 = masterTarif.batasBlok1;
                                                        param.batasBlok2 = masterTarif.batasBlok2;
                                                        param.biayaBeban = masterTarif.biayaBeban;
                                                        param.biayaPakai1 = masterTarif.biayaPakai1;
                                                        param.biayaPakai2 = masterTarif.biayaPakai2;
                                                        param.biayaPakai3 = masterTarif.biayaPakai3;
                                                        param.biayaKvar = masterTarif.bkVar;
                                                        param.jenisBatas = masterTarif.jenisBatas;
                                                        param.emin = masterTarif.emin;
                                                        param.statusEmin = masterTarif.statusEmin;
                                                        param.jenisBatasEmin = masterTarif.jenisBatasEmin;
                                                        param.metKwh = masterTarif.meterKwh;
                                                        param.metKvarh = masterTarif.meterKvarh;
                                                        param.metKvamaks = masterTarif.meterKavamaks;
                                                        param.faktorKValue = masterTarif.faktorKValue;
                                                        param.faktorNValue = masterTarif.faktorNValue;
                                                        param.faktorPValue = masterTarif.faktorPValue;
                                                        param.faktorQValue = masterTarif.faktorQValue;
                                                        param.workflowNameEnergi = masterTarif.workflowMutasi;
                                                        param.workflowVersionEnergi = masterTarif.workflowMutasiVersion;
                                                    }

//                                                    param.blok = masterTarif.blok;
//                                                    param.dayaMax = masterTarif.dayaMax;
//                                                    param.tarif = masterTarif.tarif;
//                                                    param.batasBlok1 = masterTarif.batasBlok1;
//                                                    param.batasBlok2 = masterTarif.batasBlok2;
//                                                    param.biayaBeban = masterTarif.biayaBeban;
//                                                    param.biayaPakai1 = masterTarif.biayaPakai1;
//                                                    param.biayaPakai2 = masterTarif.biayaPakai2;
//                                                    param.biayaPakai3 = masterTarif.biayaPakai3;
//                                                    param.biayaKvar = masterTarif.bkVar;
//                                                    param.jenisBatas = masterTarif.jenisBatas;
//                                                    param.jenisBatasEmin = masterTarif.jenisBatasEmin;
//                                                    param.emin = masterTarif.emin;
//                                                    param.statusEmin = masterTarif.statusEmin;
//                                                    param.metKwh = masterTarif.meterKwh;
//                                                    param.metKvarh = masterTarif.meterKvarh;
//                                                    param.metKvamaks = masterTarif.meterKavamaks;
//                                                    param.faktorKValue = masterTarif.faktorKValue;
//                                                    param.faktorNValue = masterTarif.faktorNValue;
//                                                    param.faktorPValue = masterTarif.faktorPValue;
//                                                    param.workflowNameEnergi = masterTarif.workflowMutasi;
//                                                    param.workflowVersionEnergi = masterTarif.workflowMutasiVersion;

                                                    if (masterTarifKdpt2 != null) {
                                                        if (masterTarifKdpt2.keterangan != null && masterTarifKdpt2.keterangan.equals("TANPA METER")) {
                                                            param.jamNyalaTanpaMeter = new BigDecimal(masterTarifKdpt2.pembtrf_2);
                                                        } else {
                                                            param.jamNyalaTanpaMeter = BigDecimal.ZERO;
                                                        }
                                                        if (item.tarif.equals("C")) {
                                                            param.faktorQValue = BigDecimal.valueOf(Double.parseDouble(masterTarifKdpt2.pembtrf_2));
                                                        }

                                                    }
                                                    param.kdpt2 = item.kdpt2;
                                                    if (masterDiskonStimulus != null) {
                                                        param.flagDiskonStimulus = 1;
                                                        param.prosentaseDiskonStimulus = masterDiskonStimulus.prosentase;
                                                        param.workflowDiskon = masterDiskonStimulus.workflowDiskon;
                                                        param.workflowDiskonVersion = masterDiskonStimulus.workflowDiskonVersion;
                                                    } else {
                                                        param.flagDiskonStimulus = 0;
                                                        param.prosentaseDiskonStimulus = BigDecimal.ZERO;
                                                    }

                                                    param.flagPltsLama = 0;
                                                    param.flagPltsBaru = 0;
                                                    param.kwhSaldoPltsLama = BigDecimal.ZERO;
                                                    param.capacityChargePltsLama = BigDecimal.ZERO;
                                                    if (dilPltsAtap != null) {
                                                        param.flagPltsLama = 1;
                                                        param.kapasitasPltsAtapLama = dilPltsAtap.getKapasitasPltsAtap();
                                                        if (item.tarif != null && item.tarif.contains("I") && dilPltsAtap.getKapasitasPltsAtap() > 0) {
                                                            param.capacityChargePltsLama =
                                                                new BigDecimal(dilPltsAtap.getKapasitasPltsAtap())
                                                                    .divide(BigDecimal.valueOf(1000), 10, RoundingMode.HALF_UP) //
                                                                    .multiply(BigDecimal.valueOf(5)) // Perkalian dengan 5
                                                                    .multiply(param.biayaPakai1)
                                                                    .setScale(0, RoundingMode.HALF_UP);
                                                        }
                                                    }
                                                    if (masterPlgPltsAtap != null) {
                                                        param.kwhSaldoPltsLama = masterPlgPltsAtap.getKwhSaldo();
                                                    }

                                                    if (dilPltsAtapNew != null) {
                                                        param.flagPltsBaru = 1;
                                                        param.kapasitasPltsAtapBaru = dilPltsAtapNew.getKapasitasPltsAtap();
                                                    }

                                                    if((param.flagPltsLama.equals(1) || param.flagPltsBaru.equals(1)) && masterTarif != null) {
                                                        param.workflowPltsMutasiVersion = masterTarif.workflowPltsMutasiVersion;
                                                        param.workflowPltsMutasi = masterTarif.workflowPltsMutasi;
                                                    }

                                                    param.slalwbpExport = item.slalwbpExport;
                                                    param.slawbpExport = item.slawbpExport;
                                                    param.slakvarhExport = item.slakvarhExport;
                                                    param.sahlwbpExport = item.sahlwbpExport;
                                                    param.sahwbpExport = item.sahwbpExport;
                                                    param.sahkvarhExport = item.sahkvarhExport;
                                                    param.slalwbpPasang = item.slalwbpPasang;
                                                    param.sahlwbpCabut = item.sahlwbpCabut;
                                                    param.slawbpPasang = item.slawbpPasang;
                                                    param.sahwbpCabut = item.sahwbpCabut;
                                                    param.slakvarhPasang = item.slakvarhPasang;
                                                    param.sahkvarhCabut = item.sahkvarhCabut;
                                                    param.flagMutasiJ = Helper.isMutationJorK(item.thblrek, item.thblmut, item.jnsmut) ? 1 : 0;
                                                    param.workflowPlts = masterTarif.workflowPlts;
                                                    param.workflowPltsVersion = masterTarif.workflowPltsVersion;
                                                    param.fakmLm = item.fakmLm;
                                                    param.fakmkvarhLm = item.fakmkvarhLm;
                                                    param.frtLm = item.frtLm;
                                                    param.totalEnergies = items.size();
                                                    return param;
                                                })
                                            )
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
        )).collect().asList());
    }

    public Uni<List<DataEnergi>> getDataEnergiLama(String idpel, String thblrek) {
        return sessionFactory.withSession(session ->
            energiRepository.findChild(idpel, thblrek)
                .onItem().transformToMulti(items -> Multi.createFrom().iterable(items))
                .onItem().transformToUniAndConcatenate(item -> masterTarifRepository.findByKodedaya(item.tarif, item.kdpt, item.daya.longValue())
                    .onItem().transformToUni(masterTarif -> masterTarifKdpt2Repository.getMasterTarifKdpt2(item.tarif, item.kdpt2, item.daya, item.thblrek)
                        .onItem().transformToUni(masterTarifKdpt2 -> fjnRepository.getFjnValue(item.fjn)
                            .onItem().transformToUni(fjn ->
                                frtRepository.getFrtValue(item.frt).onItem().transform(frtValue -> {
                                    LOG.info("Data Energi {} : {}", item.pecahKe, item.toString());
                                    DataEnergi param = new DataEnergi();
                                    param.id = item.id;
                                    param.pecahKe = item.pecahKe;
                                    param.kdmut = item.kdmut;
                                    param.jnsmut = item.jnsmut;
                                    param.thblmut = item.thblmut;
                                    param.thblrek = item.thblrek;
                                    param.tglrubah = item.tglrubah.toString();
                                    param.idpel = item.idpel;
                                    param.tglBacaLalu = item.tglbacalalu;
                                    param.tglBacaAkhir = item.tglbacaakhir;
                                    param.sahlwbp = item.sahlwbp == null ? BigDecimal.ZERO : item.sahlwbp;
                                    param.sahwbp = item.sahwbp == null ? BigDecimal.ZERO : item.sahwbp;
                                    param.sahkvarh = item.sahkvarh == null ? BigDecimal.ZERO : item.sahkvarh;
                                    param.slalwbp = item.slalwbp == null ? BigDecimal.ZERO : item.slalwbp;
                                    param.slawbp = item.slawbp == null ? BigDecimal.ZERO : item.slawbp;
                                    param.slakvarh = item.slakvarh == null ? BigDecimal.ZERO : item.slakvarh;
                                    param.blok = masterTarif.blok;
                                    param.dayaMax = masterTarif.dayaMax;
                                    param.dayamaxWbp = item.dayamaxWbp;
                                    param.daya = item.daya.intValue();
                                    param.tarif = masterTarif.tarif;
                                    param.batasBlok1 = masterTarif.batasBlok1;
                                    param.batasBlok2 = masterTarif.batasBlok2;
                                    param.biayaBeban = masterTarif.biayaBeban;
                                    param.biayaPakai1 = masterTarif.biayaPakai1;
                                    param.biayaPakai2 = masterTarif.biayaPakai2;
                                    param.biayaPakai3 = masterTarif.biayaPakai3;
                                    param.biayaKvar = masterTarif.bkVar;
                                    param.jenisBatas = masterTarif.jenisBatas;
                                    param.jenisBatasEmin = masterTarif.jenisBatasEmin;
                                    param.emin = masterTarif.emin;
                                    param.fakm = item.fakm;
                                    param.statusEmin = masterTarif.statusEmin;
                                    if (masterTarifKdpt2 != null) {
                                        if (masterTarifKdpt2.keterangan != null && masterTarifKdpt2.keterangan.equals("TANPA METER")) {
                                            param.jamNyalaTanpaMeter = new BigDecimal(masterTarifKdpt2.pembtrf_2);
                                        } else {
                                            param.jamNyalaTanpaMeter = BigDecimal.ZERO;
                                        }
                                        if (item.tarif.equals("C")) {
                                            param.faktorQValue = BigDecimal.valueOf(Double.parseDouble(masterTarifKdpt2.pembtrf_2));
                                        }
                                    } else {
                                        param.jamNyalaTanpaMeter = BigDecimal.ZERO;
                                    }
                                    param.metKwh = masterTarif.meterKwh;
                                    param.faktorFjn = fjn;
                                    param.faktorFakm = item.fakm;
                                    param.metKvarh = masterTarif.meterKvarh;
                                    param.metKvamaks = masterTarif.meterKavamaks;
                                    param.faktorFrt = item.frt != null ? frtValue : null;
                                    param.faktorKValue = masterTarif.faktorKValue;
                                    param.faktorNValue = masterTarif.faktorNValue;
                                    param.faktorPValue = masterTarif.faktorPValue;
                                    param.workflowNameEnergi = masterTarif.workflowMutasi;
                                    param.workflowVersionEnergi = masterTarif.workflowMutasiVersion;
                                    return param;
                                }))))).collect().asList()
        );

    }

    public Uni<Void> addBilling(BillingParam req) {
        LOG.info("[Start Service addBilling]");
        return sessionFactory.withTransaction(session -> billingRepository.findByIdpelThblrek(req.idpel, req.thblrek)
            /* proses simpan data billing */
            .onItem().transformToUni(bill -> {
                if (bill != null) {
                    return billingRepository.persist(BillingMapper.mapFromDto(bill, req)).flatMap(e -> billingRepository.flush())
                        .onItem().invoke(() -> LOG.info("[End Service addBilling]")).replaceWithVoid();
                } else {
                    throw new AppException(Constant.DATA_TIDAK_DITEMUKAN);
                }
                /* proses simpan data billing energi */
            }).onItem().transformToUni(bill -> {
                if (!req.billingEnergiParamList.isEmpty()) {
                    for (BillingEnergiParam param : req.billingEnergiParamList) {
                        LOG.info("[Start Service addBillingEnergi]");
                        return energiRepository.findByIdAndIdpelAndThblrek(param.id, param.idpel, param.thblrek).onItem().transformToUni(energi -> {
                            return energiRepository.persist(BillingEnergiMapper.mapFromDto(energi, param)).flatMap(e -> energiRepository.flush())
                                .onItem().invoke(() -> LOG.info("[End Service addBillingEnergi]")).replaceWithVoid();
                        });
                    }
                }
                return Uni.createFrom().voidItem();
            }));
    }

    public Uni<APIResponse<KoreksiBillingDTO>> findDataKoreksiBill(String idpel, String thblrek) {
        LOG.info("[Start Service findDataKoreksiBill : {} - {}]", idpel, thblrek);

        return sessionFactory.withSession(session ->
                billingRepository.findByIdpelThblrekPostingBilling(idpel, thblrek)
                        .onItem().ifNull().failWith(new AppException(Constant.DATA_TIDAK_DITEMUKAN)) // Jika null langsung gagal
                        .onItem().transformToUni(billing -> {
                            if (billing == null) {
                                return Uni.createFrom().item(APIResponse.error(Constant.DATA_TIDAK_DITEMUKAN));
                            }
                            return energiRepository.findChild(idpel, thblrek)
                                    .onItem().ifNull().failWith(new AppException(Constant.DATA_TIDAK_DITEMUKAN)) // Jika child data kosong
                                    .onItem().transformToMulti(energiList ->
                                            Multi.createFrom().iterable(energiList)
                                                    .onItem().transformToUni(energi ->
                                                            KoreksiBillingMapper.mapFromEntity(billing, energi)
                                                    )
                                                    .merge()
                                    )
                                    .collect().asList()
                                    .onItem().transform(koreksiBillingList -> {
                                        KoreksiBillingDTO result = new KoreksiBillingDTO();
                                        result.idpel = billing.idpel;
                                        result.bulanRekening = billing.thblrek;
                                        result.tahun = billing.thblrek.substring(0, 4);
                                        result.nama = billing.nama;
                                        result.alamat = billing.alamat;
                                        result.unitup = billing.unitup;
                                        result.tarif = billing.tarif;
                                        result.daya = billing.daya;
                                        result.dlpd = billing.dlpd;
                                        result.tarifLama = billing.tarifLmPindahTrf;
                                        result.dayaLama = billing.dayaLmPindahTrf;
                                        result.frt = billing.frt;
                                        result.blthMutasi = billing.thblmut;
                                        result.jenisMK = billing.jnsmut;
                                        result.tglPerubahan = billing.tglrubah;
                                        result.tglBacaLalu = billing.tglbacalalu;
                                        result.tglBacaAkhir = billing.tglbacaakhir;
                                        result.lwbpLalu = billing.slalwbp;
                                        result.lwbpAkhir = billing.sahlwbp;
                                        result.kwhLwbp = billing.kwhlwbp;
                                        result.wbpLalu = billing.slawbp;
                                        result.wbpAkhir = billing.sahwbp;
                                        result.kwhWpb = billing.kwhwbp;
                                        result.kvarhLalu = billing.slakvarh;
                                        result.kvarhAkhir = billing.sahkvarh;
                                        result.kwhKvarh = billing.pemkvarh;
                                        result.kvamaxAkhir = billing.sahkvamaks;
                                        result.kwhKvamax = billing.dayamaks;
                                        result.kvawbpAkhir = billing.sahkvamaxWbp;
                                        result.kwhKvawbp = billing.dayamaxWbp;
                                        result.jamNyala = billing.jamnyala;
                                        result.dataBillEnergiList = koreksiBillingList.stream()
                                                .flatMap(dto -> dto.dataBillEnergiList.stream())
                                                .collect(Collectors.toList());
                                        result.uraianNilaiList = koreksiBillingList.getFirst().uraianNilaiList;
                                        LOG.info("[End Service findDataKoreksiBill]");
                                        return APIResponse.ok(result);
                                    });
                        })
        );
    }

    @WithTransaction
    public Uni<APIResponse<?>> saveKoreksiBilling(KoreksiBillingParam req) {
        LOG.info("[Start Service save Koreksi Billing]");

        CustomerParam param = new CustomerParam();
        param.idpel = req.idpel;
        param.thblrek = req.bulanRekening;
        LOG.info("IDPEL: {}", param.idpel);

        return billingRepository.findByIdpelThblrekPostingBilling(param.idpel, param.thblrek)
                .onItem().transformToUni(dataExist -> {
                    if (dataExist == null) {
                        return Uni.createFrom().item(APIResponse.error("Data billing tidak ditemukan"));
                    }
                    return energiRepository.findByIdpelThblrek(param.idpel, param.thblrek)
                            .onItem().transformToUni(billingEnergiExist -> {
                                if (billingEnergiExist == null) {
                                    return Uni.createFrom()
                                            .item(APIResponse.error("Data billing Energi tidak ditemukan"));
                                }
                                LogBillingEnergi logBillingEnergi = BillingEnergiMapper
                                        .mapLogBillingEnergi(new LogBillingEnergi(), billingEnergiExist);
                                return logBillingEnergiRepository.persist(logBillingEnergi)
                                        .onItem().transformToUni(savedLog -> updateKoreksiBilling(req)
                                                .onItem()
                                                .transformToUni(updateResult -> fsmService
                                                        .storeDataToWorkFlowV2_new(param)
                                                        .onFailure()
                                                        .invoke(ex -> LOG.error("Workflow processing failed: {}",
                                                                ex.getMessage()))
                                                        .replaceWith(APIResponse.ok("Data berhasil disimpan"))));
                            });
                });
    }
    
// Koreksi billing 720
public Uni<APIResponse<KoreksiBillingDTO>> findDataKoreksiBill720(String idpel, String thblrek) {
    LOG.info("[Start Service findDataKoreksiBill720 : {} - {}]", idpel, thblrek);

    return sessionFactory.withSession(session -> billingRepository.find720ByIdpelThblrek(idpel, thblrek)
            .onItem().ifNull().failWith(new AppException(Constant.DATA_TIDAK_DITEMUKAN)) // Jika null langsung gagal
            .onItem().transformToUni(billing -> {
                if (billing == null) {
                    return Uni.createFrom().item(APIResponse.error(Constant.DATA_TIDAK_DITEMUKAN));
                }
                return energiRepository.findChild(idpel, thblrek)
                        .onItem().ifNull().failWith(new AppException(Constant.DATA_TIDAK_DITEMUKAN)) // Jika child
                        // data kosong
                        .onItem().transformToMulti(energiList -> Multi.createFrom().iterable(energiList)
                                .onItem()
                                .transformToUni(energi -> KoreksiBillingMapper.mapFromEntity(billing, energi))
                                .merge())
                        .collect().asList()
                        .onItem().transform(koreksiBillingList -> {
                            KoreksiBillingDTO result = new KoreksiBillingDTO();
                            result.idpel = billing.idpel;
                            result.bulanRekening = billing.thblrek;
                            result.tahun = billing.thblrek.substring(0, 4);
                            result.nama = billing.nama;
                            result.alamat = billing.alamat;
                            result.unitup = billing.unitup;
                            result.tarif = billing.tarif;
                            result.daya = billing.daya;
                            result.dlpd = billing.dlpd;
                            result.tarifLama = billing.tarifLmPindahTrf;
                            result.dayaLama = billing.dayaLmPindahTrf;
                            result.frt = billing.frt;
                            result.blthMutasi = billing.thblmut;
                            result.jenisMK = billing.jnsmut;
                            result.tglPerubahan = billing.tglrubah;
                            result.tglBacaLalu = billing.tglbacalalu;
                            result.tglBacaAkhir = billing.tglbacaakhir;
                            result.lwbpLalu = billing.slalwbp;
                            result.lwbpAkhir = billing.sahlwbp;
                            result.kwhLwbp = billing.kwhlwbp;
                            result.wbpLalu = billing.slawbp;
                            result.wbpAkhir = billing.sahwbp;
                            result.kwhWpb = billing.kwhwbp;
                            result.kvarhLalu = billing.slakvarh;
                            result.kvarhAkhir = billing.sahkvarh;
                            result.kwhKvarh = billing.pemkvarh;
                            result.kvamaxAkhir = billing.sahkvamaks;
                            result.kwhKvamax = billing.dayamaks;
                            result.kvawbpAkhir = billing.sahkvamaxWbp;
                            result.kwhKvawbp = billing.dayamaxWbp;
                            result.jamNyala = billing.jamnyala;
                            result.dataBillEnergiList = koreksiBillingList.stream()
                                    .flatMap(dto -> dto.dataBillEnergiList.stream())
                                    .collect(Collectors.toList());
                            result.uraianNilaiList = koreksiBillingList.getFirst().uraianNilaiList;
                            LOG.info("[End Service findDataKoreksiBill]");
                            return APIResponse.ok(result);
                        });
            }));
}

@WithTransaction
public Uni<APIResponse<?>> saveKoreksiBilling720(KoreksiBillingParam req) {
    LOG.info("[Start Service save Koreksi Billing]");

    CustomerParam param = new CustomerParam();
    param.idpel = req.idpel;
    param.thblrek = req.bulanRekening;
    LOG.info("IDPEL: {}", param.idpel);

    return billingRepository.findByIdpelThblrekPostingBilling720(param.idpel, param.thblrek)
        .onItem().transformToUni(databill -> {
            if (databill == null) {
                return Uni.createFrom().item(APIResponse.error("Data billing tidak ditemukan"));
            }

            return energiRepository.findByIdpelThblrek(param.idpel, param.thblrek)
                .onItem().transformToUni(billingEnergiExist -> {
                    if (billingEnergiExist == null) {
                        return Uni.createFrom().item(APIResponse.error("Data billing Energi tidak ditemukan"));
                    }

                    LogBillingEnergi logBillingEnergi = BillingEnergiMapper
                        .mapLogBillingEnergi(new LogBillingEnergi(), billingEnergiExist);
                    // LOG.info("Mapped LogBillingEnergi:");
                    // LOG.info("  BACAAKHIR: {}", logBillingEnergi.tglbacaakhir);
                    // LOG.info("  BACALALU: {}", logBillingEnergi.tglbacalalu);  
                    return logBillingEnergiRepository.persist(logBillingEnergi)
                                    .onItem().transformToUni(savedLog -> {
                            
                              // Simpan Billing720
                            Billing720JN billing720 = BillingMapper
                                .mapBilling720(new Billing720JN(), databill);

                            return billing720Repository.persist(billing720)
                                .onItem().transformToUni(savedLog720 -> {
                                    // Update data koreksi
                                    return updateKoreksiBilling(req)
                                        .onItem().transformToUni(updateResult -> {
                                            // Panggil workflow
                                            return fsmService.storeDataToWorkFlowV2_new(param)
                                                .onFailure().invoke(ex -> LOG.error("Workflow processing failed: {}", ex.getMessage()))
                                                .replaceWith(APIResponse.ok("Data berhasil disimpan"));
                                        });
                                });
                        });
                });
        });
}
//END 720

    // Method untuk menyimpan koreksi billing PLTS
    public Uni<APIResponse<KoreksiBillingPltsDTO>> findDataKoreksiBillPlts(String idpel, String thblrek) {
        LOG.info("[Start Service findDataKoreksiBillPlts : {} - {}]", idpel, thblrek);

        return sessionFactory.withSession(session -> billingRepository.findByIdpelThblrek(idpel, thblrek)
                .onItem().ifNotNull().transformToUni(billing -> energiRepository.findChild(idpel, thblrek)
                        .onItem().ifNotNull().transformToMulti(energiList -> Multi.createFrom().iterable(energiList) // Ubah menjadi aliran
                                .onItem()
                                .transformToUni(energi -> KoreksiBillingPltsMapper.mapFromEntity(billing, energi) // Map setiap item
                                )
                                .merge() // Gabungkan menjadi satu aliran
                        )
                        .collect().asList() // Kumpulkan kembali sebagai list
                        .onItem().transform(koreksiBillingPltsList -> {
                            KoreksiBillingPltsDTO result = new KoreksiBillingPltsDTO();
                            result.idpel = billing.idpel;
                            result.bulanRekening = billing.thblrek;
                            result.tahun = billing.thblrek.substring(0, 4);
                            result.nama = billing.nama;
                            result.alamat = billing.alamat;
                            result.unitup = billing.unitup;
                            result.tarif = billing.tarif;
                            result.daya = billing.daya;
                            result.dlpd = billing.dlpd;
                            result.tarifLama = billing.tarifLmPindahTrf;
                            result.dayaLama = billing.dayaLmPindahTrf;
                            result.frt = billing.frt;
                            result.blthMutasi = billing.thblmut;
                            result.jenisMK = billing.jnsmut;
                            result.tglPerubahan = billing.tglrubah;
                            result.tglBacaLalu = billing.tglbacalalu;
                            result.tglBacaAkhir = billing.tglbacaakhir;
                            result.lwbpLalu = billing.slalwbp;
                            result.lwbpAkhir = billing.sahlwbp;
                            result.kwhLwbp = billing.kwhlwbp;
                            result.wbpLalu = billing.slawbp;
                            result.wbpAkhir = billing.sahwbp;
                            result.kwhWpb = billing.kwhwbp;
                            result.kvarhLalu = billing.slakvarh;
                            result.kvarhAkhir = billing.sahkvarh;
                            result.kwhKvarh = billing.pemkvarh;
                            result.kvamaxAkhir = billing.sahkvamaks;
                            result.kwhKvamax = billing.dayamaks;
                            result.kvawbpAkhir = billing.sahkvamaxWbp;
                            result.kwhKvawbp = billing.dayamaxWbp;
                            result.jamNyala = billing.jamnyala;
                            result.dataBillEnergiPltsList = koreksiBillingPltsList.stream()
                                    .flatMap(dto -> dto.dataBillEnergiPltsList.stream()) // Gabungkan semua data billing energi
                                    .collect(Collectors.toList());
                            result.uraianNilaiPltsList = koreksiBillingPltsList.getFirst().uraianNilaiPltsList; // Ambil dari salah satu
                            LOG.info("[End Service findDataKoreksiBill]");
                            return APIResponse.ok(result);
                        }))
                .onItem().ifNull().failWith(new AppException(Constant.DATA_TIDAK_DITEMUKAN)));
    }
    
    @WithTransaction
    public Uni<APIResponse<?>> saveKoreksiBillingPlts(KoreksiBillingPltsParam req) {
        LOG.info("[Start Service save Koreksi Billing PLTS]");

        CustomerParam param = new CustomerParam();
        param.idpel = req.idpel;
        param.thblrek = req.bulanRekening;
        LOG.info("IDPEL: {}", param.idpel);

        return billingRepository.findByIdpelThblrekPostingBilling(param.idpel, param.thblrek)
                .onItem().transformToUni(dataExist -> {
                    if (dataExist == null) {
                        return Uni.createFrom().item(APIResponse.error("Data billing tidak ditemukan"));
                    }
                    return energiRepository.findByIdpelThblrek(param.idpel, param.thblrek)
                            .onItem().transformToUni(billingEnergiExist -> {
                                if (billingEnergiExist == null) {
                                    return Uni.createFrom().item(APIResponse.error("Data billing Energi tidak ditemukan"));
                                }
                                LogBillingEnergi logBillingEnergi = BillingEnergiMapper.mapLogBillingEnergi(new LogBillingEnergi(), billingEnergiExist);
                                return logBillingEnergiRepository.persist(logBillingEnergi)
                                        .onItem().transformToUni(savedLog ->
                                        updateKoreksiBillingPlts(req)
                                                        .onItem().transformToUni(updateResult ->
                                                                fsmService.storeDataToWorkFlowV2_new(param)
                                                                        .onFailure().invoke(ex ->
                                                                                LOG.error("Workflow processing failed: {}", ex.getMessage())
                                                                        )
                                                                        .replaceWith(APIResponse.ok("Data berhasil disimpan"))
                                                        )
                                        );
                            });
                });
    }

    public Uni<BillingEnergi> updateKoreksiBillingPlts(KoreksiBillingPltsParam params) {
        LOG.info("UPDATE Koreksi Billing PLTS : [{}, {}]", params.idpel, params.bulanRekening);
        return sessionFactory.withTransaction(session ->
            billingRepository.findByIdpelThblrek(params.idpel, params.bulanRekening)
                .onItem().ifNotNull().transformToUni(billingExisting -> {
                    // Map existing billing dengan data baru
                    Billing billing = BillingMapper.mapToEntityForKoreksiBillingPlts(billingExisting, params.getDataBillEnergiPltsList(), params.getUserId());

                    // Simpan billing
                    return billingRepository.persistAndFlush(billing)
                        .onItem().transformToUni(savedBilling ->
                            energiRepository.findChildKoreksi(params.idpel, params.bulanRekening)
                                .onItem().transformToMulti(energiList ->
                                    Multi.createFrom().iterable(energiList) // Iterasi list energi
                                )
                                .onItem().transformToUniAndConcatenate(existingEnergi -> {
                                    // Cari data yang cocok untuk update
                                    KoreksiBillingPltsDTO.DataBillEnergiPltsDTO dataBillEnergiPltsDTO =
                                        params.getDataBillEnergiPltsList().stream()
                                            .filter(dataEnergi -> dataEnergi.pecahKe == existingEnergi.pecahKe)
                                            .findFirst()
                                            .orElseThrow(() -> new AppException("Data energi tidak ditemukan"));
                                    // Map data baru ke entity BillingEnergi
                                    BillingEnergi updatedEnergi = KoreksiBillingPltsMapper.mapUpdateToBilling2(dataBillEnergiPltsDTO, existingEnergi);

                                    // Simpan perubahan BillingEnergi
                                    return energiRepository.persistAndFlush(updatedEnergi);
                                })
                                .collect().last() // Ambil BillingEnergi terakhir yang berhasil diproses
                        );
                })
                .onItem().ifNull().failWith(new AppException("Data billing tidak ditemukan"))
        );
    }
    //End of Koreksi Billing PLTS

    public Uni<BillingEnergi> updateKoreksiBilling(KoreksiBillingParam params) {
        LOG.info("UPDATE Koreksi Billing : [{}, {}]", params.idpel, params.bulanRekening);

        return sessionFactory.withTransaction(session ->
            billingRepository.findByIdpelThblrek(params.idpel, params.bulanRekening)
                .onItem().ifNotNull().transformToUni(billingExisting -> {
                    // Map existing billing dengan data baru
                    Billing billing = BillingMapper.mapToEntityForKoreksiBilling(billingExisting, params.getDataBillEnergiList(), params.getUserId());

                    // Simpan billing
                    return billingRepository.persistAndFlush(billing)
                        .onItem().transformToUni(savedBilling ->
                            energiRepository.findChildKoreksi(params.idpel, params.bulanRekening)
                                .onItem().transformToMulti(energiList ->
                                    Multi.createFrom().iterable(energiList) // Iterasi list energi
                                )
                                .onItem().transformToUniAndConcatenate(existingEnergi -> {
                                    // Cari data yang cocok untuk update
                                    KoreksiBillingDTO.DataBillEnergiDTO dataBillEnergiDTO =
                                        params.getDataBillEnergiList().stream()
                                            .filter(dataEnergi -> dataEnergi.pecahKe == existingEnergi.pecahKe)
                                            .findFirst()
                                            .orElseThrow(() -> new AppException("Data energi tidak ditemukan"));

                                    // Map data baru ke entity BillingEnergi
                                    BillingEnergi updatedEnergi = KoreksiBillingMapper.mapUpdateToBilling2(dataBillEnergiDTO, existingEnergi);

                                    // Simpan perubahan BillingEnergi
                                    return energiRepository.persistAndFlush(updatedEnergi);
                                })
                                .collect().last() // Ambil BillingEnergi terakhir yang berhasil diproses
                        );
                })
                .onItem().ifNull().failWith(new AppException("Data billing tidak ditemukan"))
        );
    }

    public Uni<Billing> hitungDLPD(InisialisasiBillingDTO inisialisasiBillingDTO, Billing billing, List<OutputEnergiParam> lstOutputEnergiParam) {
        LOG.info("start hitung dlpd : {}", billing);
        return billingRepository.getRataRataKwhAndKvarh(billing.thblrek, billing.idpel)
            .onItem().transform(kwhPakaiRata2BulanDTO -> {
                LOG.info("dlpd 1 : {}", kwhPakaiRata2BulanDTO);
                DLPDBillingDTO dlpdDTO = new DLPDBillingDTO();
                int lanjut = 0;
                if (billing.pemkwh.multiply(BigDecimal.valueOf(1000)).divide(BigDecimal.valueOf(billing.daya),
                        2, RoundingMode.HALF_UP).compareTo(BigDecimal.valueOf(720)) > 0) {
                    dlpdDTO.setDlpd(10);
                    lanjut = 1;
                }
                BigDecimal slalwbp = Optional.ofNullable(billing.slalwbp).orElse(BigDecimal.ZERO);
                BigDecimal sahlwbp = Optional.ofNullable(billing.sahlwbp).orElse(BigDecimal.ZERO);
                BigDecimal slawbp = Optional.ofNullable(billing.slawbp).orElse(BigDecimal.ZERO);
                BigDecimal sahwbp = Optional.ofNullable(billing.sahwbp).orElse(BigDecimal.ZERO);
                BigDecimal slakvarh = Optional.ofNullable(billing.slakvarh).orElse(BigDecimal.ZERO);
                BigDecimal sahkvarh = Optional.ofNullable(billing.sahkvarh).orElse(BigDecimal.ZERO);
                BigDecimal sahlwbpCabut = Optional.ofNullable(billing.sahlwbpCabut).orElse(BigDecimal.ZERO);
                BigDecimal slalwbpPasang = Optional.ofNullable(billing.slalwbpPasang).orElse(BigDecimal.ZERO);
                BigDecimal pemkwhReal = lstOutputEnergiParam.stream()
                        .map(OutputEnergiParam::getPemkwh_real)
                        .filter(kwh -> kwh != null)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                if (inisialisasiBillingDTO.metKwh.equals("T") && sahlwbp.compareTo(slalwbp) < 0 && lanjut == 0) {
                    if ((billing.thblmut.equals(billing.thblrek)) &&
                        (billing.jnsmut.toLowerCase().contains("d") ||
                            billing.jnsmut.toLowerCase().contains("e") ||
                            billing.jnsmut.toLowerCase().contains("k") ||
                            billing.jnsmut.toLowerCase().contains("j"))) {
                        if (sahlwbpCabut.compareTo(slalwbp) < 0 || Optional.of(sahlwbp).orElse(BigDecimal.ZERO).compareTo(slalwbpPasang) < 0) {
                            dlpdDTO.setDlpd(9);
                            lanjut = 1;
                        }
                    } else {
                        dlpdDTO.setDlpd(9);
                        lanjut = 1;
                    }
                }

                if (inisialisasiBillingDTO.metKwh.equals("G") &&
                    (Optional.of(sahlwbp).orElse(BigDecimal.ZERO).compareTo(slalwbp) < 0 ||
                        sahwbp.compareTo(slawbp) < 0 ||
                        sahkvarh.compareTo(slakvarh) < 0
                    ) && lanjut == 0) {
                    if ((billing.thblmut.equals(billing.thblrek)) &&
                        (billing.jnsmut.toLowerCase().contains("d") ||
                            billing.jnsmut.toLowerCase().contains("e") ||
                            billing.jnsmut.toLowerCase().contains("k") ||
                            billing.jnsmut.toLowerCase().contains("j"))) {
                        if (sahlwbpCabut.compareTo(slalwbp) < 0 || Optional.of(sahlwbp).orElse(BigDecimal.ZERO).compareTo(slalwbpPasang) < 0 ||
                            billing.sahwbpCabut.compareTo(slawbp) < 0 || sahwbp.compareTo(billing.slawbpPasang) < 0 ||
                            billing.sahkvarhCabut.compareTo(slakvarh) < 0 || sahkvarh.compareTo(billing.slakvarhPasang) < 0) {
                            dlpdDTO.setDlpd(9);
                            lanjut = 1;
                        }
                    } else {
                        dlpdDTO.setDlpd(9);
                        lanjut = 1;
                    }
                }

                if (pemkwhReal.compareTo(BigDecimal.ZERO) == 0 && lanjut == 0) {
                    dlpdDTO.setDlpd(8);
                    lanjut = 1;
                }

                if (pemkwhReal.multiply(BigDecimal.valueOf(1000)).divide(BigDecimal.valueOf(billing.daya), 2, RoundingMode.HALF_UP).compareTo(BigDecimal.valueOf(40)) < 0 && lanjut == 0) {
                    dlpdDTO.setDlpd(3);
                    lanjut = 1;
                }

                if ((billing.thblmut.equals(billing.thblrek)) &&
                    (billing.jnsmut.toLowerCase().contains("d") ||
                        billing.jnsmut.toLowerCase().contains("e") ||
                        billing.jnsmut.toLowerCase().contains("k") ||
                        billing.jnsmut.toLowerCase().contains("j")) && lanjut == 0) {
                    dlpdDTO.setDlpd(7);
                    lanjut = 1;
                }

                if (billing.rpptl.compareTo(BigDecimal.ZERO) == 0 && lanjut == 0) {
                    dlpdDTO.setDlpd(19);
                    lanjut = 1;
                }

                if (lanjut == 0) {
                    dlpdDTO.setDlpd(11);
                    lanjut = 1;
                }
                LOG.info("dlpd 2 : {}", dlpdDTO);
                if (inisialisasiBillingDTO.metKwh.equals("G")) {
                    if (billing.kwhlwbp != null && billing.kwhlwbp.compareTo(kwhPakaiRata2BulanDTO.getLwbppakaiRata().multiply(BigDecimal.valueOf(3))) > 0 ||
                        billing.kwhwbp.compareTo(kwhPakaiRata2BulanDTO.getWbppakaiRata().multiply(BigDecimal.valueOf(3))) > 0 ||
                        billing.pemkvarh.compareTo(kwhPakaiRata2BulanDTO.getKvarhpakaiRata().multiply(BigDecimal.valueOf(3))) > 0) {
                        dlpdDTO.setDlpd3bln(" > 50% RATA2 3 BULAN");
                    } else {
                        dlpdDTO.setDlpd3bln(" <= 50% RATA2 3 BULAN");
                    }
                }

                if (inisialisasiBillingDTO.metKwh.equals("T")) {
                    if (billing.kwhlwbp != null && billing.kwhlwbp.compareTo(kwhPakaiRata2BulanDTO.getLwbppakaiRata().multiply(BigDecimal.valueOf(3))) > 0) {
                        dlpdDTO.setDlpd3bln(" > 50% RATA2 3 BULAN");
                    } else {
                        dlpdDTO.setDlpd3bln(" <= 50% RATA2 3 BULAN");
                    }
                }

                if (inisialisasiBillingDTO.metKwh.equals("G")) {
                    if (billing.pemkwh != null && billing.pemkwh.compareTo(billing.pemkvarh) > 0) {
                        dlpdDTO.setDlpdKvarh("KVARH PAKAI < KWH PAKAI");
                    } else if (billing.pemkwh != null && billing.pemkwh.multiply(new BigDecimal("2")).compareTo(billing.pemkvarh) < 0) {
                        dlpdDTO.setDlpdKvarh("KVARH PAKAI > 2 * KWH PAKAI");
                    } else {
                        dlpdDTO.setDlpdKvarh("KVARH PAKAI >= KWH PAKAI");
                    }
                } else {
                    dlpdDTO.setDlpdKvarh("TANPA KVARH");
                }

                if (inisialisasiBillingDTO.metKwh.equals("G")) {
                    if (billing.pemkwh.compareTo(billing.pemkvarh) > 0) {
                        dlpdDTO.setDlpdKvarh("KVARH PAKAI < KWH PAKAI");
                    } else if (billing.pemkwh.multiply(new BigDecimal("2")).compareTo(billing.pemkvarh) < 0) {
                        dlpdDTO.setDlpdKvarh("KVARH PAKAI > 2 * KWH PAKAI");
                    } else {
                        dlpdDTO.setDlpdKvarh("KVARH PAKAI >= KWH PAKAI");
                    }
                } else {
                    dlpdDTO.setDlpdKvarh("TANPA KVARH");
                }

                dlpdDTO.setDlpdFkm("FAKTOR KALI METER = FAKTOR KALI KVARH");
                if (inisialisasiBillingDTO.metKwh.equals("G")) {
                    if (!Objects.equals(billing.fakm, billing.fakmkvarh)) {
                        dlpdDTO.setDlpdFkm("FAKTOR KALI METER <> FAKTOR KALI KVARH");
                    }
                }

                dlpdDTO.setDlpdJnsmutasi("TANPA PECAHAN");
                if (billing.jnsmut.contains("A") && billing.thblmut.equals(billing.thblrek)) {
                    dlpdDTO.setDlpdJnsmutasi("PASANG BARU");
                } else if (billing.jnsmut.contains("E") && billing.thblmut.equals(billing.thblrek)) {
                    dlpdDTO.setDlpdJnsmutasi("MUTASI TARIF");
                } else if (billing.jnsmut.contains("D") && billing.thblmut.equals(billing.thblrek)) {
                    dlpdDTO.setDlpdJnsmutasi("MUTASI DAYA");
                } else if (billing.jnsmut.contains("J") && billing.thblmut.equals(billing.thblrek)) {
                    dlpdDTO.setDlpdJnsmutasi("GANTI METER/FAKTOR KALI");
                } else if (billing.jnsmut.contains("K") && billing.thblmut.equals(billing.thblrek)) {
                    dlpdDTO.setDlpdJnsmutasi("GANTI METER/FAKTOR KALI");
                } else if (billing.jnsmut.contains("P") && billing.thblmut.equals(billing.thblrek)) {
                    dlpdDTO.setDlpdJnsmutasi("PASANG KEMBALI");
                }

                try {
                    Date tglBacaAdkhir = new SimpleDateFormat("yyyyMMdd").parse(billing.tglbacaakhir);
                    Date tglPertamaBulanRekening = new SimpleDateFormat("yyyyMMdd").parse(billing.thblrek + "01");
                    if (tglBacaAdkhir.after(tglPertamaBulanRekening)) {
                        dlpdDTO.setDlpdTglbaca("TANGGAL BACA > TANGGAL 1 BULAN REKENING");
                    } else {
                        dlpdDTO.setDlpdTglbaca("TANGGAL BACA <= TANGGAL 1 BULAN REKENING");
                    }
                } catch (ParseException e) {
                    e.printStackTrace();
                    throw new AppException("Error Parse tglbacaakhir :" + billing.tglbacaakhir);
                }

                LOG.info("data dlpd : {}", dlpdDTO);

                Billing billingDlpd = billing;
                billingDlpd.dlpd = new BigDecimal(dlpdDTO.getDlpd());
                if(dlpdDTO.getDlpd().equals(10)) {
                    billingDlpd.postingbilling = "1";
                    billingDlpd.msg = "Pemakaian 720 Jam Nyala ke atas, silahkan melakukan koreksi pada menu Koreksi Stand ini";
                }
                billingDlpd.dlpd3bln = dlpdDTO.getDlpd3bln();
                billingDlpd.dlpdFkm = dlpdDTO.getDlpdFkm();
                billingDlpd.dlpdJnsmutasi = dlpdDTO.getDlpdJnsmutasi();
                billingDlpd.dlpdKvarh = dlpdDTO.getDlpdKvarh();
                billingDlpd.dlpdTglbaca = dlpdDTO.getDlpdTglbaca();
                return billingDlpd;
            }).onFailure().transform(ex -> {
                ex.printStackTrace();
                throw new AppException(" Pemakaian rata2 error: " + ex.getMessage());
            });
    }

    public Uni<APIResponse<?>> saveBilling(OutputBillingParam param) {
        LOG.info("Waktu Mulai : {}, Selesai : {}", param.tglmulaihitung, param.tglselesaihitung);
        InisialisasiBillingDTO inputBilling = param.getInput();
        Output output = param.getOutput();
        Instant startTime = Instant.now();
        return sessionFactory.withTransaction(session ->
                billingRepository.findByIdpelThblrek(inputBilling.idpel, inputBilling.thblrek)
                    .onItem().transformToUni(bill ->
                        hitungDLPD(inputBilling, BillingMapper.OutputToEntity(bill, output, param.tglmulaihitung, param.tglselesaihitung), null)
                            .onItem().transformToUni(billing -> {
                                LOG.info("Hasil Hitung DLPD disimpan");
                                return billingRepository.persist(billing)
                                    .onItem().transformToUni(savedBill -> billingEnergiRepository.findByIdpelThblrek(billing.idpel, billing.thblrek)
                                        .onItem().transformToUni(billEnergi -> billingEnergiRepository
                                            .persist(BillingEnergiMapper.OutputToEntity(billEnergi, inputBilling, output))
                                            .onItem().transform(saveBill -> {
//                                                                Instant elapsed = Instant.now();
//                                                                long duration = Duration.between(startTime,elapsed).getNano()/1_000_000;
                                                String message = "Simpan Data Billing IDPEL : " + billing.idpel + " Sukses";
                                                LOG.info(message);
                                                return APIResponse.ok(message);
                                            }))).onFailure().invoke((e) -> {
                                        e.printStackTrace();
                                        LOG.error("Error : {}", e.getMessage());
                                        e.printStackTrace();
                                    });
                            }))
                    .onFailure()
                    .recoverWithItem(new APIResponse(500, false, "Gagal Mengupdate data billing", null))
        );
    }
    public Uni<APIResponse<?>> saveBillingMutasi(OutputBillingMutasiParam param) {
        InisialisasiBillingDTO inputBilling = param.getInput();
        Output output = param.getOutput();

        return sessionFactory.withTransaction(transac -> billingRepository.findByIdpelThblrek(inputBilling.idpel, inputBilling.thblrek)
            .onItem().transformToUni(bill -> hitungDLPD(inputBilling, BillingMapper.OutputToEntity(bill, output, param.tglmulaihitung, param.tglselesaihitung), param.getOutput_energi()))
            .onItem().transformToUni(billingdlpd -> billingRepository.persistAndFlush(billingdlpd))
            .onItem().transformToUni(savedBill -> billingEnergiRepository.findChild(inputBilling.idpel, inputBilling.thblrek))
            .onItem().transformToMulti(lstBillingEnergi -> Multi.createFrom().iterable(lstBillingEnergi))
            .onItem().transformToUniAndConcatenate(billingEnergi -> {
                OutputEnergiParam outputEnergiParam = param.getOutput_energi().stream()
                    .filter(energi -> energi.getId().equals(billingEnergi.id))
                    .findFirst()
                    .orElseThrow(() -> new AppException("Failed to store Bill Energi Mutasi: " + billingEnergi.id + "=="));
                return transac.merge(BillingEnergiMapper.OutputToEntityMutasi(billingEnergi, inputBilling, outputEnergiParam))
                    .onItem().transformToUni(billingEnergi1 ->
                        billDppPpnRepository.findByIdpelAndThblrek(inputBilling.idpel, inputBilling.thblrek)
                            .onItem().transformToUni(billDppPpn -> {
                                BillDppPpnParam request = new BillDppPpnParam();
                                request.idpel = inputBilling.idpel;
                                request.thblrek = inputBilling.thblrek;
                                request.unitupi = inputBilling.unitupi;
                                request.unitap = inputBilling.unitap;
                                request.unitup = inputBilling.unitup;

                                if (billDppPpn == null) {
                                    billDppPpn = new BillDppPpn();
                                    return billDppPpnRepository.persistAndFlush(BillDppPpnMapper.mapToEntity(billDppPpn, output, request));
                                }

                                return transac.merge(BillDppPpnMapper.mapToEntity(billDppPpn, output, request));
                            })
                    );
            }).collect().asList()
            .onFailure().transform(ex -> {
                throw new AppException("Failed to store Bill WORKFLOW Energi Hitung: " + ex.getMessage());
            }).chain(be ->
                Multi.createFrom().iterable(param.getOutput_energi_bruto())
                    .onItem().transformToUniAndConcatenate(brutoParam ->
                        billingEnergiRepository.findById(brutoParam.getId())
                            .onItem().transformToUni(billEnergi ->
                                billingEnergiBrutoRepository.findById(billEnergi.id)
                                    .onItem().ifNotNull().transformToUni(beb ->
                                        transac.merge(BillingEnergiBrutoMapper.OutputToEntityMutasi(
                                            beb, billEnergi, brutoParam
                                        ))
                                    ).onItem().ifNull().switchTo(() ->
                                        BillingEnergiBrutoMapper.OutputToEntityMutasi(
                                            new BillingEnergiBruto(), billEnergi, brutoParam
                                        ).persist()
                                    )
                            )
                    ).collect().asList().replaceWith(be)
            ).onFailure().transform(ex -> {
                throw new AppException("Failed to store Bill WORKFLOW Energi Bruto Hitung: " + ex.getMessage());
            }).onItem().transform(savedBillEnergi -> APIResponse.ok("Simpan Data Billing"))
        );
    }

    public Integer getMutasiPecahan(String thblmut, String thblrek, String jnsmut) {
        if ((thblmut.equals(thblrek)) &&
            (jnsmut.toLowerCase().contains("d") ||
                jnsmut.toLowerCase().contains("e") ||
                jnsmut.toLowerCase().contains("k") ||
                jnsmut.toLowerCase().contains("j"))) {
            return 1;
        }
        return 0;
    }


}
