package com.iconplus.ap2t.generator.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iconplus.ap2t.common.exception.AppException;
import com.iconplus.ap2t.common.param.CustomerParam;
import com.iconplus.ap2t.common.param.DataEnergi;
import com.iconplus.ap2t.common.param.InisialisasiBillingDTO;
import com.iconplus.ap2t.common.param.billing.MonitoringDlpdParam;
import com.iconplus.ap2t.common.param.billing.VerifDlpdParam;
import com.iconplus.ap2t.common.param.billing.VerifikasiDlpdParam;
import com.iconplus.ap2t.common.param.master.UnitParam;
import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.common.response.billing.MonitoringDpmDetilDTO;
import com.iconplus.ap2t.common.response.billing.MonitoringDpmRekapDTO;
import com.iconplus.ap2t.common.response.billing.VerifDlpdResponse;
import com.iconplus.ap2t.common.response.billing.VerifikasiDlpdResponse;
import com.iconplus.ap2t.common.utility.Constant;
import com.iconplus.ap2t.common.utility.ExcelExportUtil;
import com.iconplus.ap2t.data.entity.billing.Billing;
import com.iconplus.ap2t.data.param.Pagination;
import com.iconplus.ap2t.data.repository.billing.*;
import com.iconplus.ap2t.data.repository.master.MasterTarifKdpt2Repository;
import com.iconplus.ap2t.data.repository.master.MasterTarifRepository;
import com.iconplus.ap2t.generator.dto.BillingDTO;
import com.iconplus.ap2t.generator.mapper.BillingMapper;
import io.quarkus.hibernate.reactive.panache.PanacheQuery;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.quarkus.panache.common.Page;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import io.vertx.mutiny.pgclient.PgPool;
import io.vertx.mutiny.sqlclient.Row;
import io.vertx.mutiny.sqlclient.Tuple;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.BadRequestException;
import org.hibernate.reactive.mutiny.Mutiny;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ApplicationScoped
public class BillingService {
    Logger LOG = LoggerFactory.getLogger(BillingService.class.getName());
    @Inject
    PgPool client;
    @Inject
    BillingRepository billingRepository;

    @Inject
    BillingEnergiRepository energiRepository;

    @Inject
    MasterTarifRepository masterTarifRepository;

    @Inject
    FjnRepository fjnRepository;

    @Inject
    FrtRepository frtRepository;

    @Inject
    PpjRepository ppjRepository;

    @Inject
    MasterTarifKdpt2Repository masterTarifKdpt2Repository;

    @Inject
    BillingEnergiRepository billingEnergiRepository;

    @Inject
    BillingEnergiBrutoRepository billingEnergiBrutoRepository;

    @Inject
    BillDppPpnRepository billDppPpnRepository;

    @Inject
    FsmService fsmService;

    @Inject
    DpmRepository dpmRepository;

    @Inject
    Mutiny.SessionFactory sessionFactory;
    @Inject
    ExcelExportUtil excelExportUtil;
    @Inject
    ObjectMapper objectMapper;
     

    @WithTransaction
    public Uni<APIResponse<?>> findDataBill(String idpel, String thblrek) {
        LOG.info("[Start Service findDataBill : {} - {}]", idpel, thblrek);
        return billingRepository.findByIdpelThblrek(idpel, thblrek)
            .onItem().ifNotNull().transformToUni(bill -> {
                Uni<BillingDTO> response = BillingMapper.mapFromEntity(bill);
                LOG.info("[End Service findDataBill]");
                return APIResponse.ok(response);
            }).onItem().ifNull().failWith(new AppException(Constant.DATA_TIDAK_DITEMUKAN));
    }

    @Deprecated
    public Uni<InisialisasiBillingDTO> getInisialisasiBillingByIdpelThblrek(CustomerParam param) {
        Instant startProcess = Instant.now();
        return billingRepository.findByIdpelThblrek(param.idpel, param.thblrek)
            .onItem().ifNotNull().transformToUni(item -> masterTarifRepository.findByKodedaya(item.tarif, item.kdpt, item.daya.longValue())
                .onItem().transformToUni(masterTarif -> masterTarifKdpt2Repository.getMasterTarifKdpt2(item.tarif, item.kdpt_2, item.daya, param.thblrek)
                    .onItem().transformToUni(masterTarifKdpt2 -> fjnRepository.getFjnValue(item.fjn)
                        .onItem().transformToUni(fjn -> getDataEnergi(param.idpel, param.thblrek, item)
                            .onItem().transformToUni(energies -> ppjRepository.getProsentasePPJ(item.tarif, item.pemda, item.unitupi, item.daya.longValue(), item.thblrek)
                                .onItem().transformToUni(prosenPpj -> frtRepository.getFrtValue(item.frt)
                                    .onItem().transform(frt ->
                                    {
                                        InisialisasiBillingDTO initial = new InisialisasiBillingDTO();
                                        initial.id = item.id;
                                        initial.idpel = item.idpel;
                                        initial.thblrek = item.thblrek;
                                        initial.kdprosesklp = item.kdprosesklp;
                                        initial.unitupi = item.unitupi;
                                        initial.unitap = item.unitap;
                                        initial.unitup = item.unitup;
                                        initial.jnsmut = item.jnsmut;
                                        initial.kdmut = item.kdmut;
                                        initial.thblmut = item.thblmut;
                                        initial.tglnyala = item.tglnyala.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                                        initial.tglrubah = item.tglrubah.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                                        initial.kdproses = item.kdproses;
                                        initial.nopel = item.nopel;
                                        initial.nama = item.nama;
                                        initial.tarif = item.tarif;
                                        initial.kdpt = item.kdpt;
                                        initial.kdpt2 = item.kdpt_2;
                                        initial.daya = item.daya;
                                        initial.faknpremium = item.faknpremium;
                                        initial.dayajbst = item.dayajbst;
                                        initial.kdbedajbst = item.kdbedajbst;
                                        initial.kddk = item.kddk;
                                        initial.kdbacameter = item.kdbacameter;
                                        initial.kdmeterai = item.kdmeterai;
                                        initial.lokettgk = item.lokettgk;
                                        initial.kogol = item.kogol;
                                        initial.subkogol = item.subkogol;
                                        initial.pemda = item.pemda;
                                        initial.kdppj = item.kdppj;
                                        initial.kdinkaso = item.kdinkaso;
                                        initial.kdklp = item.kdklp;
                                        initial.kdind = item.kdind;
                                        initial.kdam = item.kdam;
                                        initial.kdkvamaks = item.kdkvamaks;
                                        initial.maxDemand = item.maxDemand;
                                        initial.frt = item.frt;
                                        initial.fjn = item.fjn;
                                        initial.kdbpt = item.kdbpt;
                                        initial.dayabpt = item.dayabpt;
                                        initial.kddayabpt = item.kddayabpt;
                                        initial.kdpembmeter = item.kdpembmeter;
                                        initial.fakm = item.fakm;
                                        initial.fakmkvarh = item.fakmkvarh;
                                        initial.fakmkvam = item.fakmkvam;
                                        initial.rpsewatrafoDil = item.rpsewatrafoDil;
                                        initial.flagsewakap = item.flagsewakap;
                                        initial.faradkap = item.faradkap;
                                        initial.rpsewakap = item.rpsewakap;
                                        initial.kdangsa = item.kdangsa;
                                        initial.rpangsa = item.rpangsa;
                                        initial.lamaangsa = item.lamaangsa;
                                        initial.thblangs1a = item.thblangs1a;
                                        initial.angskea = item.angskea;
                                        initial.kdangsb = item.kdangsb;
                                        initial.rpangsb = item.rpangsb;
                                        initial.lamaangsb = item.lamaangsb;
                                        initial.thblangs1b = item.thblangs1b;
                                        initial.angskeb = item.angskeb;
                                        initial.kdangsc = item.kdangsc;
                                        initial.rpangsc = item.rpangsc;
                                        initial.lamaangsc = item.lamaangsc;
                                        initial.thblangs1c = item.thblangs1c;
                                        initial.angskec = item.angskec;
                                        initial.kdinvoice = item.kdinvoice;
                                        initial.jnsmutAde = item.jnsmutAde;
                                        initial.tahunKe = item.tahunKe;
                                        initial.flagppjangsa = item.flagppjangsa;
                                        initial.flagppjangsb = item.flagppjangsb;
                                        initial.flagppjangsc = item.flagppjangsc;
                                        initial.sahlwbp = item.sahlwbp;
                                        initial.sahwbp = item.sahwbp;
                                        initial.sahkvarh = item.sahkvarh;
                                        initial.slalwbp = item.slalwbp;
                                        initial.slawbp = item.slawbp;
                                        initial.slakvarh = item.slakvarh;
                                        initial.dayamaxWbp = item.dayamaxWbp;
                                        initial.tglbacalalu = item.tglbacalalu;
                                        initial.tglbacaakhir = item.tglbacaakhir;
                                        initial.faktorFjn = fjn != null ? new BigDecimal(fjn) : null;
                                        initial.faktorFrt = frt != null ? new BigDecimal(frt) : null;
                                        initial.faktorFakm = item.fakm;
                                        initial.dayaMax = item.dayamaks;
                                        if (masterTarifKdpt2 != null) {
                                            if (masterTarifKdpt2.keterangan.equals("TANPA METER")) {
                                                initial.jamNyalaTanpaMeter = new BigDecimal(masterTarifKdpt2.pembtrf_2);
                                            } else {
                                                initial.jamNyalaTanpaMeter = BigDecimal.ZERO;
                                            }
                                            if (item.tarif.equals("C")) {
                                                initial.faktorQValue = BigDecimal.valueOf(Double.parseDouble(masterTarifKdpt2.pembtrf_2));
                                            } else {
                                                initial.faktorQValue = masterTarif.faktorQValue;
                                            }
                                        } else {
                                            initial.jamNyalaTanpaMeter = BigDecimal.ZERO;
                                            initial.faktorQValue = masterTarif.faktorQValue;
                                        }

                                        initial.blok = masterTarif.blok;
                                        initial.batasBlok1 = masterTarif.batasBlok1;
                                        initial.batasBlok2 = masterTarif.batasBlok2;
                                        initial.biayaBeban = masterTarif.biayaBeban;
                                        initial.biayaPakai1 = masterTarif.biayaPakai1;
                                        initial.biayaPakai2 = masterTarif.biayaPakai2;
                                        initial.biayaPakai3 = masterTarif.biayaPakai3;
                                        initial.biayaKvar = masterTarif.bkVar;
                                        initial.prosenPpj = prosenPpj;
                                        initial.prosenPpn = new BigDecimal((Double.parseDouble(item.thblrek) > Double.parseDouble("202203") ? 11 : 10));
                                        initial.jenisBatas = masterTarif.jenisBatas;
                                        initial.emin = masterTarif.emin;
                                        initial.statusEmin = item.statusemin;
                                        initial.jenisBatasEmin = masterTarif.jenisBatasEmin;
                                        initial.metKwh = masterTarif.meterKwh;
                                        initial.metKvarh = masterTarif.meterKvarh;
                                        initial.metKvamaks = masterTarif.meterKavamaks;
                                        initial.faktorKValue = masterTarif.faktorKValue;
                                        initial.faktorNValue = masterTarif.faktorNValue;
                                        initial.faktorPValue = masterTarif.faktorPValue;
                                        initial.workflowName = masterTarif.workflowName;
                                        initial.workflowVersion = masterTarif.workflowVersion;
                                        if (energies.size() > 0) {
                                            initial.setDataEnergi(energies);
                                        }
                                        Instant endProcess = Instant.now();
                                        long duration = Duration.between(startProcess, endProcess).getNano() / 1_000_000;
                                        LOG.info("Waktu Inisialisasi : {} ms", duration);
                                        return initial;
                                    })))))));
    }

    @Deprecated
    public Uni<List<DataEnergi>> getDataEnergi(String idpel, String thblrek, Billing billing) {
        return energiRepository.findChild(idpel, thblrek)
            .onItem().transformToMulti(items -> Multi.createFrom().iterable(items))
            .onItem().transformToUniAndConcatenate(item -> masterTarifRepository.findByKodedaya(item.tarif, item.kdpt, item.daya.longValue())
                .onItem().transformToUni(masterTarif -> masterTarifKdpt2Repository.getMasterTarifKdpt2(item.tarif, item.kdpt2, item.daya, item.thblrek)
                    .onItem().transformToUni(masterTarifKdpt2 -> fjnRepository.getFjnValue(item.fjn)
                        .onItem().transformToUni(fjn ->
                            frtRepository.getFrtValue(item.frt).onItem().transform(frtValue -> {
                                LOG.info("Data Energi {} : {}", item.pecahKe, item.toString());
                                DataEnergi param = new DataEnergi();
                                param.id = item.id;
                                param.pecahKe = item.pecahKe;
                                param.kdmut = item.kdmut;
                                param.jnsmut = item.jnsmut;
                                param.thblmut = item.thblmut;
                                param.thblrek = item.thblrek;
                                param.tglrubah = item.tglrubah.toString();
                                param.idpel = item.idpel;
                                param.tglBacaLalu = item.tglbacalalu;
                                param.tglBacaAkhir = item.tglbacaakhir;
                                param.tglBacaBulanLalu = billing.tglbacalalu;
                                param.tglbacabulanIni = billing.tglbacaakhir;
                                param.sahlwbp = item.sahlwbp == null ? BigDecimal.ZERO : item.sahlwbp;
                                param.sahwbp = item.sahwbp == null ? BigDecimal.ZERO : item.sahwbp;
                                param.sahkvarh = item.sahkvarh == null ? BigDecimal.ZERO : item.sahkvarh;
                                param.slalwbp = item.slalwbp == null ? BigDecimal.ZERO : item.slalwbp;
                                param.slawbp = item.slawbp == null ? BigDecimal.ZERO : item.slawbp;
                                param.slakvarh = item.slakvarh == null ? BigDecimal.ZERO : item.slakvarh;
                                param.blok = masterTarif.blok;
                                param.dayaMax = masterTarif.dayaMax;
                                param.dayamaxWbp = item.dayamaxWbp;
                                param.daya = item.daya.intValue();
                                param.tarif = masterTarif.tarif;
                                param.batasBlok1 = masterTarif.batasBlok1;
                                param.batasBlok2 = masterTarif.batasBlok2;
                                param.biayaBeban = masterTarif.biayaBeban;
                                param.biayaPakai1 = masterTarif.biayaPakai1;
                                param.biayaPakai2 = masterTarif.biayaPakai2;
                                param.biayaPakai3 = masterTarif.biayaPakai3;
                                param.biayaKvar = masterTarif.bkVar;
                                param.jenisBatas = masterTarif.jenisBatas;
                                param.jenisBatasEmin = masterTarif.jenisBatasEmin;
                                param.emin = masterTarif.emin;
                                param.fakm = item.fakm;
                                param.statusEmin = masterTarif.statusEmin;
                                if (masterTarifKdpt2 != null) {
                                    if (masterTarifKdpt2.keterangan.equals("TANPA METER")) {
                                        param.jamNyalaTanpaMeter = new BigDecimal(masterTarifKdpt2.pembtrf_2);
                                    } else {
                                        param.jamNyalaTanpaMeter = BigDecimal.ZERO;
                                    }
                                    if (item.tarif.equals("C")) {
                                        param.faktorQValue = BigDecimal.valueOf(Double.parseDouble(masterTarifKdpt2.pembtrf_2));
                                    } else {
                                        param.faktorQValue = masterTarif.faktorQValue;
                                    }
                                } else {
                                    param.jamNyalaTanpaMeter = BigDecimal.ZERO;
                                    param.faktorQValue = masterTarif.faktorQValue;
                                }
                                param.metKwh = masterTarif.meterKwh;
                                param.faktorFjn = fjn;
                                param.faktorFakm = item.fakm;
                                param.metKvarh = masterTarif.meterKvarh;
                                param.metKvamaks = masterTarif.meterKavamaks;
                                param.faktorFrt = item.frt != null ? frtValue : null;
                                param.faktorKValue = masterTarif.faktorKValue;
                                param.faktorNValue = masterTarif.faktorNValue;
                                param.faktorPValue = masterTarif.faktorPValue;
                                param.workflowNameEnergi = masterTarif.workflowMutasi;
                                param.workflowVersionEnergi = masterTarif.workflowMutasiVersion;
                                return param;
                            }))))).collect().asList();
    }

//    public Uni<APIResponse<?>> saveBillingMutasi(OutputBillingMutasiParam param) {
//        InisialisasiBillingDTO inputBilling = param.getInput();
//        Output output = param.getOutput();
//
//        return sessionFactory.withTransaction(transac -> billingRepository.findByIdpelThblrek(inputBilling.idpel, inputBilling.thblrek)
//            .onItem().transformToUni(bill -> billingServiceOptimize.hitungDLPD(inputBilling, BillingMapper.OutputToEntity(bill, output, param.tglmulaihitung, param.tglselesaihitung)))
//            .onItem().transformToUni(billingdlpd -> billingRepository.persistAndFlush(billingdlpd))
//            .onItem().transformToUni(savedBill -> billingEnergiRepository.findChild(inputBilling.idpel, inputBilling.thblrek))
//            .onItem().transformToMulti(lstBillingEnergi -> Multi.createFrom().iterable(lstBillingEnergi))
//            .onItem().transformToUniAndConcatenate(billingEnergi -> {
//                OutputEnergiParam outputEnergiParam = param.getOutput_energi().stream()
//                    .filter(energi -> energi.getId().equals(billingEnergi.id))
//                    .findFirst()
//                    .orElseThrow(() -> new AppException("Failed to store Bill Energi Mutasi: " + billingEnergi.id + "=="));
//                return transac.merge(BillingEnergiMapper.OutputToEntityMutasi(billingEnergi, inputBilling, outputEnergiParam))
//                    .onItem().transformToUni(billingEnergi1 ->
//                        billDppPpnRepository.findByIdpelAndThblrek(inputBilling.idpel, inputBilling.thblrek)
//                            .onItem().transformToUni(billDppPpn -> {
//                                BillDppPpnParam request = new BillDppPpnParam();
//                                request.idpel = inputBilling.idpel;
//                                request.thblrek = inputBilling.thblrek;
//                                request.unitupi = inputBilling.unitupi;
//                                request.unitap = inputBilling.unitap;
//                                request.unitup = inputBilling.unitup;
//
//                                if (billDppPpn == null) {
//                                    billDppPpn = new BillDppPpn();
//                                    return billDppPpnRepository.persistAndFlush(BillDppPpnMapper.mapToEntity(billDppPpn, output, request));
//                                }
//
//                                return transac.merge(BillDppPpnMapper.mapToEntity(billDppPpn, output, request));
//                            })
//                    );
//            }).collect().asList()
//            .onFailure().transform(ex -> {
//                throw new AppException("Failed to store Bill WORKFLOW Energi Hitung: " + ex.getMessage());
//            }).chain(be ->
//                Multi.createFrom().iterable(param.getOutput_energi_bruto())
//                    .onItem().transformToUniAndConcatenate(brutoParam ->
//                        billingEnergiRepository.findById(brutoParam.getId())
//                            .onItem().transformToUni(billEnergi ->
//                                billingEnergiBrutoRepository.findById(billEnergi.id)
//                                    .onItem().ifNotNull().transformToUni(beb ->
//                                        transac.merge(BillingEnergiBrutoMapper.OutputToEntityMutasi(
//                                            beb, billEnergi, brutoParam
//                                        ))
//                                    ).onItem().ifNull().switchTo(() ->
//                                        BillingEnergiBrutoMapper.OutputToEntityMutasi(
//                                            new BillingEnergiBruto(), billEnergi, brutoParam
//                                        ).persistAndFlush()
//                                    )
//                            )
//                    ).collect().asList().replaceWith(be)
//            ).onFailure().transform(ex -> {
//                throw new AppException("Failed to store Bill WORKFLOW Energi Bruto Hitung: " + ex.getMessage());
//            }).onItem().transform(savedBillEnergi -> APIResponse.ok("Simpan Data Billing"))
//        );
//    }

    @WithSession
    public Uni<Pagination<List<Billing>>> getDataVerifikasiDlpd(MonitoringDlpdParam request, Page page) {
        PanacheQuery<Billing> query = billingRepository.findForMonitoringVerifikasi(
            request.unit, request.kdProsesKlp, request.thblrek, request.dlpd, page
        );

        return query.list().onItem().transformToUni(data ->
            query.count().onItem().transformToUni(totalItem ->
                query.pageCount().onItem().transform(pageCount ->
                    Pagination.of(totalItem, pageCount, page, data)
                )
            )
        );
    }

    @WithTransaction
    public Uni<List<VerifikasiDlpdResponse>> setVerifikasiDldp(VerifikasiDlpdParam request) {
        if (!request.isApprove && request.reason.isEmpty()) {
            throw new BadRequestException("alasan verifikasi wajib diisi");
        }

        if (request.isById) {
            return Multi.createFrom().iterable(request.listIdDlpd).onItem().transformToUniAndConcatenate(id -> {
                VerifikasiDlpdResponse res = new VerifikasiDlpdResponse();
                return billingRepository.findByIdForDlpd(id).onItem().ifNotNull().transformToUni(bill ->
                    billingRepository.persistAndFlush(BillingMapper.mapToEntityForVerifikasiDlpd(bill, request)).onItem().transform(item -> {
                        res.idpel = item.idpel;
                        res.kdProsesKlp = item.kdprosesklp;
                        res.thblrek = item.thblrek;
                        res.success = true;
                        res.message = "success";
                        return res;
                    }).onFailure().recoverWithItem(ex -> {
                        ex.printStackTrace();
                        res.success = false;
                        res.message = ex.getMessage();
                        return res;
                    })
                ).onItem().ifNull().continueWith(() -> {
                    res.success = false;
                    res.message = "dlpd tidak ditemukan";
                    return res;
                }).onFailure().recoverWithItem(ex -> {
                    ex.printStackTrace();
                    res.success = false;
                    res.message = ex.getMessage();
                    return res;
                });
            }).collect().asList();
        } else {
            List<VerifikasiDlpdResponse> responses = new ArrayList<>();
            VerifikasiDlpdResponse res = new VerifikasiDlpdResponse();
            return billingRepository.setFlagVerifikasiDlpd(request).onItem().transformToUni(updatedCount -> {
                res.kdProsesKlp = request.byKdProses.kdProsesKlp;
                res.thblrek = request.byKdProses.thblrek;
                res.idpel = updatedCount.toString();
                res.success = updatedCount > 0;
                res.message = updatedCount > 0 ? updatedCount + " data berhasil verifikasi" : "tidak ada yang di-update";
                responses.add(res);

                return Uni.createFrom().item(responses);
            }).onFailure().recoverWithItem(ex -> {
                ex.printStackTrace();

                res.kdProsesKlp = request.byKdProses.kdProsesKlp;
                res.thblrek = request.byKdProses.thblrek;
                res.success = false;
                res.message = ex.getMessage();
                responses.add(res);

                return responses;
            });
        }
    }

    @WithTransaction
    public Uni<List<VerifDlpdResponse>> setVerifikasiDldp(VerifDlpdParam req) {
        if (!req.isApprove && req.reason.isEmpty()) {
            throw new BadRequestException("alasan verifikasi wajib diisi");
        }

        return Multi.createFrom().iterable(req.dlpd).onItem().transformToUniAndConcatenate(dlpd -> {
            VerifDlpdResponse res = new VerifDlpdResponse();
            return billingRepository.setFlagVerifikasiDlpd(
                req.userId, req.isApprove, req.reason, dlpd.unitup,
                dlpd.dlpd, dlpd.kdProsesKlp, dlpd.thblrek
            ).onItem().transformToUni(updatedCount -> {
                res.kdProsesKlp = dlpd.kdProsesKlp;
                res.thblrek = dlpd.thblrek;
                res.dlpd = dlpd.dlpd;
                res.success = updatedCount > 0;
                res.message = updatedCount > 0 ? updatedCount + " data dlpd berhasil verifikasi" : "tidak ada yang di-update";

                return Uni.createFrom().item(res);
            }).onFailure().recoverWithItem(ex -> {
                ex.printStackTrace();

                res.kdProsesKlp = dlpd.kdProsesKlp;
                res.thblrek = dlpd.thblrek;
                res.dlpd = dlpd.dlpd;
                res.success = false;
                res.message = ex.getMessage();

                return res;
            });
        }).collect().asList();
    }

    public Uni<Pagination<List<MonitoringDpmDetilDTO>>> getMonitoringDPMDetil(String unitup, String thblrek, Page page) {
        Uni<List<MonitoringDpmDetilDTO>> dataUni = dpmRepository.getMonitoringDPMDetil(
                unitup, thblrek, page.index, page.size
        );

        Uni<Long> countUni = dpmRepository.getCountMonitoringDPMDetil(unitup, thblrek
        );

        return dataUni
                .flatMap(data -> countUni
                        .map(totalItem -> {
                            int pageCount = (int) Math.ceil((double) totalItem / page.size);
                            return Pagination.of(totalItem, pageCount, page, data);
                        })
                );
    }

    public Uni<Pagination<List<MonitoringDpmRekapDTO>>> getMonitoringDPMRekap(String unitupi, String unitap, String unitup, String thblrek, Page page) {
        Uni<List<MonitoringDpmRekapDTO>> dataUni = dpmRepository.getMonitoringDPMRekap(
                unitupi, unitap, unitup, thblrek, page.index, page.size
        );

        Uni<Long> countUni = dpmRepository.getCountMonitoringDPMRekap(unitupi, unitap, unitup, thblrek
        );

        return dataUni
                .flatMap(data -> countUni
                        .map(totalItem -> {
                            int pageCount = (int) Math.ceil((double) totalItem / page.size);
                            return Pagination.of(totalItem, pageCount, page, data);
                        })
                );
    }

    public Uni<Pagination<List<Map<String, Object>>>> getMonitoringDPMRekap2(String unitupi, String unitap, String unitup, String thblrek, Page page) {
        Uni<List<Map<String, Object>>> dataUni = dpmRepository.getMonitoringDPMRekap2(
                unitupi, unitap, unitup, thblrek, page.index, page.size
        );

        Uni<Long> countUni = dpmRepository.getCountMonitoringDPMRekap(unitupi, unitap, unitup, thblrek
        );

        return dataUni
                .flatMap(data -> countUni
                        .map(totalItem -> {
                            int pageCount = (int) Math.ceil((double) totalItem / page.size);
                            return Pagination.of(totalItem, pageCount, page, data);
                        })
                );
    }

    @WithSession
    public Uni<Pagination<List<Map<String, Object>>>> getDataVerifikasiDlpdKolektif(MonitoringDlpdParam request, Page page) {
        Uni<List<Map<String, Object>>> dataUni = billingRepository.findForMonitoringVerifikasiDlpdKolektif(
                request.unit, request.kdProsesKlp, request.thblrek, request.dlpd, page.index, page.size
        );

        Uni<Long> countUni = billingRepository.getCountForMonitoringVerifikasiDlpdKolektif(request.unit, request.kdProsesKlp, request.thblrek, request.dlpd);

        return dataUni
                .flatMap(data -> countUni
                        .map(totalItem -> {
                            int pageCount = (int) Math.ceil((double) totalItem / page.size);
                            return Pagination.of(totalItem, pageCount, page, data);
                        })
                );
    }

        public byte[] generateMonitoringDPMRekapExcel(String unitupi, String unitap, String unitup, String thblrek, Page page) {
        LOG.info("Mulai generateMonitoringDPMRekapExcel untuk THBLREK: {}, Page: {}", thblrek, page);

        try {
            LOG.debug("Mengambil data Monitoring DPM Rekap dari repository...");
            List<MonitoringDpmRekapDTO> dtoList = dpmRepository
                    .getMonitoringDPMRekap(unitupi, unitap, unitup, thblrek, page.index, page.size)
                    .await().indefinitely();

            LOG.info("Jumlah data yang diambil: {}", dtoList.size());

            LOG.debug("Mengonversi DTO ke Map untuk pembuatan Excel...");
            List<Map<String, Object>> data = dtoList.stream()
                    .map(dto -> objectMapper.convertValue(dto, new TypeReference<Map<String, Object>>() {
            }))
                    .peek(map -> LOG.trace("Data baris: {}", map))
                    .collect(Collectors.toList());

            LOG.debug("Menghasilkan file Excel...");
            byte[] excelBytes = excelExportUtil.generateExcel(data, "Monitoring DPM Rekap");

            LOG.info("Excel berhasil dibuat. Ukuran file: {} bytes", excelBytes.length);
            return excelBytes;

        } catch (Exception e) {
            LOG.error("Gagal menghasilkan Excel Monitoring DPM Rekap", e);
            throw new RuntimeException("Gagal menghasilkan file Excel", e);
        }
    }

    public byte[] generateMonitoringDPMDetailExcel(String unitupi, String unitap, String unitup, String thblrek, Page page) {
        LOG.info("Mulai generateMonitoringDPMDetailExcel untuk THBLREK: {}, UNITUPI: {}, UNITAP: {}, UNITUP: {}, PAGE{}",
                thblrek, unitupi, unitap, unitup, page);
        try {
            LOG.debug("Mengambil data Monitoring DPM Rekap dari repository...");
            List<MonitoringDpmDetilDTO> dtoList = dpmRepository
                    .getMonitoringDPMDetil(unitup, thblrek, page.index, page.size)
                    .await().indefinitely();

            LOG.info("Jumlah data yang diambil: {}", dtoList.size());

            LOG.debug("Mengonversi DTO ke Map untuk pembuatan Excel...");
            List<Map<String, Object>> data = dtoList.stream()
                    .map(dto -> objectMapper.convertValue(dto, new TypeReference<Map<String, Object>>() {
            }))
                    .peek(map -> LOG.trace("Data baris: {}", map))
                    .collect(Collectors.toList());

            LOG.debug("Menghasilkan file Excel...");
            byte[] excelBytes = excelExportUtil.generateExcel(data, "Monitoring DPM Detail");

            LOG.info("Excel berhasil dibuat. Ukuran file: {} bytes", excelBytes.length);
            return excelBytes;

        } catch (Exception e) {
            LOG.error("Gagal menghasilkan Excel Monitoring DPM Detail", e);
            throw new RuntimeException("Gagal menghasilkan file Excel", e);
        }
    }

    @WithSession
    public Uni<byte[]> generateVerifDlpdExcel(MonitoringDlpdParam request, Page page) {
        LOG.info("Mulai generateVerifDlpdExcel untuk THBLREK: {}, UNITUPI: {}, UNITAP: {}, UNITUP: {}, PAGE: {}",
                request.unit, request.kdProsesKlp, request.thblrek, request.dlpd, page);

        try {
            LOG.debug("Mengambil data Monitoring Verifikasi DLPD dari repository...");
            PanacheQuery<Billing> query = billingRepository.findForMonitoringVerifikasi(
                    request.unit, request.kdProsesKlp, request.thblrek, request.dlpd, page
            );

            return query.list() // Uni<List<Billing>>
                    .onItem().transform(billingList -> {
                        LOG.info("Jumlah data yang diambil: {}", billingList.size());

                        LOG.debug("Mengonversi entitas ke DTO dan kemudian ke Map...");
                        List<Map<String, Object>> data = billingList.stream()
                                .map(dto -> objectMapper.convertValue(dto, new TypeReference<Map<String, Object>>() {
                        }))
                                .peek(map -> LOG.trace("Data baris: {}", map))
                                .collect(Collectors.toList());

                        LOG.debug("Menghasilkan file Excel dari data...");
                        byte[] excelBytes = excelExportUtil.generateExcel(data, "Monitoring Verifikasi DLPD");

                        LOG.info("Excel berhasil dibuat. Ukuran file: {} bytes", excelBytes.length);
                        return excelBytes;
                    })
                    .onFailure().invoke(e -> {
                        LOG.error("Gagal menghasilkan Excel Verifikasi DLPD", e);
                    });

        } catch (Exception e) {
            LOG.error("Gagal memulai proses generate Excel", e);
            return Uni.createFrom().failure(new RuntimeException("Gagal menghasilkan file Excel", e));
        }
    }

    @WithSession
    public Uni<byte[]> generateVerifDlpdKolektifExcel(MonitoringDlpdParam request, Page page) {
        LOG.info("Mulai generateVerifDlpdKolektifExcel untuk UNIT: {}, KD_PROSES_KLP: {}, THBLREK: {}, DLPD: {}, PAGE: {}",
                request.unit, request.kdProsesKlp, request.thblrek, request.dlpd, page);

        return billingRepository.findForMonitoringVerifikasiDlpdKolektif(
                request.unit, request.kdProsesKlp, request.thblrek, request.dlpd, page.index, page.size
        )
                .onItem().transform(billingList -> {
                    LOG.info("Jumlah data yang diambil: {}", billingList.size());

                    LOG.debug("Mengonversi entitas ke Map...");
                    List<Map<String, Object>> data = billingList.stream()
                            .map(dto -> objectMapper.convertValue(dto, new TypeReference<Map<String, Object>>() {
                    }))
                            .peek(map -> LOG.trace("Data baris: {}", map))
                            .collect(Collectors.toList());

                    LOG.debug("Menghasilkan file Excel dari data...");
                    byte[] excelBytes = excelExportUtil.generateExcel(data, "Monitoring Verifikasi DLPD");

                    LOG.info("Excel berhasil dibuat. Ukuran file: {} bytes", excelBytes.length);
                    return excelBytes;
                })
                .onFailure().recoverWithItem(e -> {
                    LOG.error("Gagal menghasilkan Excel Verifikasi DLPD Kolektif", e);
                    return new byte[0]; // Atau lempar RuntimeException jika mau error propagation
                });
    }
    public Uni<Long> getCountForMonitoringVerifikasiDlpdKolektif(UnitParam unit, String kdProsesKlp, String thblrek, String dlpd){
        String sql ="";
        if (dlpd != null && !dlpd.equals("SEMUA")) {
            sql = """
                    select count(0) total from otc.vw_data_bill_dlpd_belum_verifikasi
                        where thblrek=$1 and unitup=$2 and kdprosesklp=$3 and dlpd=$4
                    """;
            return client.preparedQuery(sql)
                    .execute(Tuple.of(
                            thblrek, unit.unitup, kdProsesKlp, new BigDecimal(dlpd)
                    ))
                    .onItem().transform(rows -> {
                        Row row = rows.iterator().next();
                        return row.getLong("total");
                    });
        }else{
            sql = """
                    select count(0) total from otc.vw_data_bill_dlpd_belum_verifikasi
                        where thblrek=$1 and unitup=$2 and kdprosesklp=$3
                    """;
            return client.preparedQuery(sql)
                    .execute(Tuple.of(
                            thblrek, unit.unitup, kdProsesKlp
                    ))
                    .onItem().transform(rows -> {
                        Row row = rows.iterator().next();
                        return row.getLong("total");
                    });
        }


    }
}
