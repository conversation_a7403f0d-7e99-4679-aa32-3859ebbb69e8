package com.iconplus.ap2t.generator.service;

import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.data.entity.master.MasterDlpd;
import com.iconplus.ap2t.data.repository.master.MasterDlpdRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.List;

@Singleton
public class MasterDpldService {

    @Inject
    MasterDlpdRepository repository;

    @WithSession
    public Uni<APIResponse<List<MasterDlpd>>> all() {
        return repository.findAll().list()
            .onItem().transform(APIResponse::ok)
            .onFailure().recoverWithItem(ex -> {
                ex.printStackTrace();
                return APIResponse.error(ex.getMessage());
            });
    }
}
