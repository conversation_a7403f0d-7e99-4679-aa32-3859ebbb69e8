package com.iconplus.ap2t.generator.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.iconplus.ap2t.common.exception.AppException;
import com.iconplus.ap2t.common.param.CustomerParam;
import com.iconplus.ap2t.common.param.InisialisasiBillingDTO;
import com.iconplus.ap2t.common.param.billing.MeterParam;
import com.iconplus.ap2t.common.param.billing.Output;
import com.iconplus.ap2t.common.param.billing.SuplaiDilParam;
import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.common.response.billing.StanTanpaMeterResponse;
import com.iconplus.ap2t.common.utility.Constant;
import com.iconplus.ap2t.data.entity.billing.Billing;
import com.iconplus.ap2t.data.entity.billing.BillingEnergi;
import com.iconplus.ap2t.data.entity.billing.Dpm;
import com.iconplus.ap2t.data.entity.billing.DpmPecahan;
import com.iconplus.ap2t.data.entity.log.LogDPMKoreksi;
import com.iconplus.ap2t.data.entity.log.LogDataMeterAcmt;
import com.iconplus.ap2t.data.entity.log.LogJobHitungBilling;
import com.iconplus.ap2t.data.repository.billing.*;
import com.iconplus.ap2t.data.repository.log.LogDPMRepository;
import com.iconplus.ap2t.data.repository.log.LogJobHitungBillingRepository;
import com.iconplus.ap2t.data.repository.master.MasterTarifRepository;
import com.iconplus.ap2t.generator.dto.BillingEnergiBulanLaluDTO;
import com.iconplus.ap2t.generator.mapper.BillingEnergiMapper;
import com.iconplus.ap2t.generator.mapper.BillingMapper;
import com.iconplus.ap2t.generator.mapper.DpmMapper;
import com.iconplus.ap2t.generator.mapper.GeneralMapper;
import com.iconplus.ap2t.generator.proxy.EngineProxy;
import com.iconplus.ap2t.generator.response.SuplaiDILDTO;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Singleton
public class FsmService {

    Logger LOG = LoggerFactory.getLogger(FsmService.class.getName());
    private static final ObjectMapper objectMapper = new ObjectMapper();
    @Inject
    private DpmRepository dpmRepository;

    @Inject
    private DpmPecahanRepository dpmPecahanRepository;

    @Inject
    private CrmService crmService;

    @Inject
    private BillingService billingService;

    @Inject
    private BillingServiceOptimize billingServiceOptimize;

    @Inject
    private BillingRepository billingRepository;

    @Inject
    private BillingEnergiRepository billingEnergiRepository;

    @Inject
    private MasterTarifRepository masterTarifRepository;

    @Inject
    private LogDPMRepository logDPMRepository;

    @Inject
    private LogJobHitungBillingRepository logJobHitungBillingRepository;


    @Inject
    private MeterAcmtRepository meterAcmtRepository;

    @Inject
    @RestClient
    private EngineProxy engineProxy;

    @Inject
    InitRepository initRepository;

    @Inject
    GeneralMapper mapper;

    @WithTransaction
    public Uni<APIResponse<Dpm>> storeDPM(MeterParam params) {
        return updateDPM(params)
                .onItem().transformToUni(updatedDPM -> storeBillingProccess(updatedDPM)
                        .onItem().transformToUni(result -> {
                            BillingEnergi billing = (BillingEnergi) result.getData();
                            CustomerParam param = new CustomerParam();
                            param.idpel = billing.idpel;
                            param.thblrek = billing.thblrek;
                            return storeDataToWorkFlow(param)
                                    .onFailure().recoverWithItem(ex -> {
                                        LOG.error("Workflow processing failed with error: " + ex.getMessage());
                                        return null; // Abaikan error dan lanjutkan
                                    })
                                    .replaceWith(APIResponse.ok(updatedDPM)); // Menggabungkan hasil proses
                        }))
                .onFailure().transform(ex -> {
                    LOG.error(ex.getMessage());
                    return new AppException(ex.getMessage());
                });
    }


// JANGAN DI HAPUS !!
     @WithTransaction
     public Uni<APIResponse<Dpm>> storeDataDPMGeneral(MeterParam params) {
         // 1️⃣ Mengonversi MeterParam ke LogDPMKoreksi
         LogDPMKoreksi logDPM = DpmMapper.mapToLogDpm(new LogDPMKoreksi(), params);
    
    
         return logDPMRepository.saveLogDPM(logDPM)
                 .onItem().transformToUni(savedLog -> {
                     LOG.info("Successfully saved LogDPM with id: {}"); // ✅ Log ID yang berhasil disimpan
                     return updateDPM(params);
                 })
                 .onFailure().invoke(ex -> {
                     LOG.error("Error while saving LogDPM: {}", ex.getMessage(), ex);
                 })
                 .onItem().transformToUni(updatedDPM -> storeBillingProccess(updatedDPM)
                         .onItem().transformToUni(result -> {
                             BillingEnergi billing = (BillingEnergi) result.getData();
                             CustomerParam param = new CustomerParam();
                             param.idpel = billing.idpel;
                             param.thblrek = billing.thblrek;
    
                             // 5️⃣ Simpan ke Workflow, tangani error jika gagal
                             return storeDataToWorkFlowV2_new(param)
                                     .onFailure().recoverWithItem(ex -> {
                                         LOG.error("Workflow processing failed with error: {}", ex.getMessage(), ex);
                                         return null; // Abaikan error dan lanjutkan
                                     })
                                     .replaceWith(APIResponse.ok(updatedDPM)); // ✅ Kembalikan APIResponse yang sukses
                         }))
                 .onFailure().transform(ex -> {
                     LOG.error("Error in storeDataDPMGeneral: {}", ex.getMessage(), ex);
                     return new AppException("Error occurred during storeDataDPMGeneral: " + ex.getMessage());
                 });
     }

//    @WithTransaction
//    public Uni<APIResponse<Dpm>> storeDataDPMGeneral(MeterParam params) {
//        // 1️⃣ Mengonversi MeterParam ke LogDPMKoreksi
//        LogDPMKoreksi logDPM = DpmMapper.mapToLogDpm(new LogDPMKoreksi(), params);
//
//        return logDPMRepository.saveLogDPM(logDPM)
//                .onItem().transformToUni(savedLog -> {
//                    LOG.info("Successfully saved LogDPM  {}");
//                    return updateDPM(params);
//                })
//                .onFailure().invoke(ex -> {
//                    LOG.error("Error while saving LogDPM: {}", ex.getMessage(), ex);
//                })
//                .onItem().transformToUni(updatedDPM -> storeBillingProccess(updatedDPM)
//                        // 🚀 Menonaktifkan proses tanpa menghapusnya
//                        .onItem().transform(result -> APIResponse.ok(updatedDPM))
//
//                )
//                .onFailure().transform(ex -> {
//                    LOG.error("Error in storeDataDPMGeneral: {}", ex.getMessage(), ex);
//                    return new AppException("Error occurred during storeDataDPMGeneral: " + ex.getMessage());
//                });
//    }

    @WithTransaction
    public Uni<APIResponse<Dpm>> storeDPMV2(MeterParam params) {
        return updateDPM(params)
                .onItem().transformToUni(updatedDPM -> storeBillingProccess(updatedDPM)
                        .onItem().transformToUni(result -> {
                            BillingEnergi billing = (BillingEnergi) result.getData();
                            CustomerParam param = new CustomerParam();
                            param.idpel = billing.idpel;
                            param.thblrek = billing.thblrek;
                            return storeDataToWorkFlowV2(param)
                                    .onFailure().recoverWithItem(ex -> {
                                        LOG.error("Workflow processing failed with error: " + ex.getMessage());
                                        return null; // Abaikan error dan lanjutkan
                                    })
                                    .replaceWith(APIResponse.ok(updatedDPM)); // Menggabungkan hasil proses
                        }))
                .onFailure().transform(ex -> {
                    LOG.error(ex.getMessage());
                    return new AppException(ex.getMessage());
                });
    }

    @WithTransaction
    public Uni<APIResponse<Dpm>> storeDataDPM(MeterParam params) {
        return updateDPM(params)
                .onItem().transformToUni(updatedDPM -> storeBillingProccess(updatedDPM)
                        .onItem().transformToUni(result -> {

                            // Membuat log di tabel log_kiriman_acmt
                            LogDataMeterAcmt logAcmt = new LogDataMeterAcmt();
                            logAcmt.id = UUID.randomUUID().toString();
                            logAcmt.idtransaksi = params.idtransaksi.substring(0, 20);
                            logAcmt.unitup = params.unitup;
                            logAcmt.idpel = params.idpel;
                            logAcmt.blth = params.blth;
                            logAcmt.kdStan = params.kdStan;
                            logAcmt.kdBaca = params.kdBaca;
                            logAcmt.tglBaca = String.valueOf(params.tglBaca);
                            logAcmt.lwbp = params.lwbp;
                            logAcmt.lwbp2 = params.lwbp2;
                            logAcmt.lwbp_import = params.lwbp_import;
                            logAcmt.wbp = params.wbp;
                            logAcmt.wbp_import = params.wbp_import;
                            logAcmt.kvarh = params.kvarh;
                            logAcmt.kvarh_import = params.kvarh_import;
                            logAcmt.transaksi_by = params.transaksi_by;
                            logAcmt.dayaMax = params.dayaMax;
                            logAcmt.dayaMaxWbp = params.dayaMaxWbp;
                            logAcmt.kdDayaMaxWbp = params.kdDayaMaxWbp;
                            logAcmt.dayaKvaMax = params.dayaKvaMax;
                            logAcmt.kdDayaKvaMax = params.kdDayaKvaMax;
                            // logAcmt.flag_kirim_workflow = 0;
                            // logAcmt.status_kirim_workflow = "BELUM";
                            // logAcmt.tgl_terima_acmt = LocalDateTime.now();
                            // logAcmt.tgl_kirim_workflow = null;

                            // Simpan log menggunakan repository dan kembalikan hasilnya
                            return meterAcmtRepository.saveLog(logAcmt)
                                    .onItem().transformToUni(savedLog -> {
                                        // Setelah log berhasil disimpan, kembalikan APIResponse dengan updatedDPM
                                        return Uni.createFrom().item(APIResponse.ok(updatedDPM));
                                    });
                        }))
                .onFailure().transform(ex -> {
                    LOG.error(ex.getMessage());
                    return new AppException(ex.getMessage());
                });
    }

    public Uni<Dpm> updateDPM(MeterParam params) {
        LOG.info("UPDATE DPM : [{}, {}]", params.idpel, params.blth);
        return dpmRepository.getIdpelThbl(params.idpel, params.blth)
                .onItem().transformToUni(dpm -> {
                    if (dpm == null) {
                        throw new AppException("Failed to Store DPM: " + Constant.DATA_TIDAK_DITEMUKAN);
                    }
                    DpmMapper.mapToDpm(dpm, params);
                    return dpmRepository.updateDpm(dpm);
                });
    }

    @WithTransaction
    public Uni<List<?>> saveStanTanpaMeter(List<SuplaiDilParam> request) {
        return Uni.combine().all().unis(
                request.stream().map(item -> {
                    LOG.info("ITEM: {}", item);

                    StanTanpaMeterResponse res = new StanTanpaMeterResponse();
                    res.idpel = item.idpel;
                    res.thblrek = item.thblrek;

                    try {
                        MeterParam meter = new MeterParam();
                        meter.idpel = item.idpel;
                        meter.blth = item.thblrek;
                        meter.unitup = item.unitup;
                        meter.kdDayaKvaMax = "";
                        meter.tglBaca = LocalDate.parse(item.thblrek + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));

                        LOG.info("METER PARAM: [{}, {}]", meter.idpel, meter.blth);

                        SuplaiDILDTO dto = mapper.convert(item, SuplaiDILDTO.class);

                        return updateDPM(meter)
                                .onItem().transformToUni(dpm -> {
                                    LOG.info("DPM: [{}, {}, {}]", dpm.idpel, dpm.thblrek, dpm.tglbaca);
                                    return storeBill(dto, dpm)
                                            .onItem().transform(bill -> {
                                                res.status = bill.isSuccess();
                                                res.keterangan = bill.getMessage();
                                                return res;
                                            });
                                }).onFailure().recoverWithItem(ex -> {
                                    LOG.error("something went wrong: {}", ex.getMessage());
                                    res.status = false;
                                    res.keterangan = ex.getMessage();
                                    return res;
                                });

                    } catch (Exception ex) {
                        LOG.error("Exception occurred: {}", ex.getMessage(), ex);
                        res.status = false;
                        res.keterangan = ex.getMessage();
                        return Uni.createFrom().item(res);
                    } finally {
                        LOG.info("DATA HAS BEEN PROCESSED");
                    }
                }).collect(Collectors.toList())).with(results -> results);
    }

    private Uni<APIResponse<?>> storeBillingProccess(Dpm dpm) {
        LOG.info("STORE BILLING PROCCESS: {}", dpm.idpel);
        return crmService.getSuplaiDilByIdpel(dpm.idpel, dpm.thblrek)
                .onItem().transformToUni(response -> {
                    if (response.isSuccess() && response.getData() != null) {
                        ObjectMapper mapper = new ObjectMapper();
                        String jsonData;
                        SuplaiDILDTO dil;
                        try {
                            jsonData = mapper.writeValueAsString(response.getData());
                            dil = mapper.readValue(jsonData, SuplaiDILDTO.class);
                        } catch (JsonProcessingException e) {
                            throw new AppException("Error: failed to convert object Bill, " + e.getMessage());
                        }
                        return storeBill(dil, dpm);
                    } else {
                        throw new AppException("--Failed to GET Suplai DIL: " + response.getMessage());
                    }
                });
    }

    private Uni<APIResponse<?>> storeBill(SuplaiDILDTO dil, Dpm dpm) {
        LOG.info("STORE BILL: {}", dil.idpel);
        Integer statusMutasi = billingServiceOptimize.getMutasiPecahan(dil.thblmut, dpm.thblrek, dil.jnsmut);
        return billingRepository.findByIdpelThblrek(dil.idpel, dpm.thblrek)
                .onItem().transformToUni(bill -> {
                    if (bill != null) {
                        if ("3".equals(bill.postingbilling)) {
                            throw new AppException("Data Billing sudah posting 3");
                        }

                        BillingMapper.mapToBilling(bill, dil, dpm, false);
                        return billingRepository.persistAndFlush(bill);
                    } else {
                        return billingRepository
                                .persistAndFlush(BillingMapper.mapToBilling(new Billing(), dil, dpm, true));
                    }
                })
                .onFailure().transform(ex -> {
                    throw new AppException("Failed to store Bill: " + ex.getMessage());
                })
                .onItem().transformToUni(savedBill -> {
                    LOG.info("STORE BILL ENERGI: {}", dil.idpel);

                    return billingEnergiRepository.findByIdpelThblrek(dpm.idpel, dpm.thblrek)
                            .onItem()
                            .transformToUni(existingBillingEnergi ->  billingEnergiRepository.findByIdpelThblrekPrev(dpm.idpel, dpm.thblrek)
                                    .onItem().transformToUni(prevBillingEnergi ->
                                            dpmPecahanRepository.getDpmPecahanByIdpelThblrek(dpm.idpel, dpm.thblrek)
                                                    .onItem().transformToUni(lstDpmPecahan -> storeBillEnergiMutasi(lstDpmPecahan, dil, dpm,
                                            statusMutasi)
                                            .onItem().transformToUni(lstEnergiPecahan -> {
                                                LOG.info("Insert Energi");
                                                BillingEnergiBulanLaluDTO dataPrev = new BillingEnergiBulanLaluDTO();
                                                                if (prevBillingEnergi != null) {
                                                                    dataPrev.slakvarh_import = prevBillingEnergi.sahkvarhExport != null
                                                                            ? prevBillingEnergi.sahkvarhExport
                                                                            : BigDecimal.ZERO;

                                                                    dataPrev.slawbp_import = prevBillingEnergi.sahwbpExport != null
                                                                            ? prevBillingEnergi.sahwbpExport
                                                                            : BigDecimal.ZERO;

                                                                    dataPrev.slalwbp_import = prevBillingEnergi.sahlwbpExport != null
                                                                            ? prevBillingEnergi.sahlwbpExport
                                                                            : BigDecimal.ZERO;
                                                                } else {
                                                                    dataPrev.slakvarh_import = BigDecimal.ZERO;
                                                                    dataPrev.slawbp_import = BigDecimal.ZERO;
                                                                    dataPrev.slalwbp_import = BigDecimal.ZERO;
                                                                }

                                                BillingEnergi billingEnergi = statusMutasi == 1
                                                        ? BillingEnergiMapper.mapToBillingEnergiPecahan(
                                                                existingBillingEnergi != null ? existingBillingEnergi
                                                                        : new BillingEnergi(),
                                                                dil, lstDpmPecahan.getLast(), lstDpmPecahan, dpm, false)
                                                        : (existingBillingEnergi != null
                                                                ? BillingEnergiMapper.mapToBilling(
                                                                        existingBillingEnergi, dil, dpm, false,dataPrev)
                                                                : BillingEnergiMapper.mapToBilling(new BillingEnergi(),
                                                                        dil, dpm, true,dataPrev));
                                                LOG.info("Energi {} {}", billingEnergi.id, billingEnergi.idpel);
                                                return billingEnergiRepository.persistAndFlush(billingEnergi);
                                            })
                                            .onFailure().transform(ex -> {
                                                ex.printStackTrace();
                                                throw new AppException(
                                                        "Failed to store Bill Energi: " + ex.getMessage());
                                            })
                                            .onItem().transform(savedBillEnergi -> APIResponse.ok(savedBillEnergi)))));
                });
    }

    public Uni<List<BillingEnergi>> storeBillEnergiMutasi(List<DpmPecahan> lstDpmPecahan, SuplaiDILDTO dil, Dpm dpm,
                                                          Integer statusMutasi) {
        return statusMutasi == 1 ? billingEnergiRepository.deleteEnergiByIdpelThblrek(dpm.idpel, dpm.thblrek)
                .onItem().transformToUni(deleteEnergi -> Multi.createFrom().iterable(lstDpmPecahan)
                        .onItem().transformToUniAndConcatenate(dpmPecahan -> {
                            LOG.info("DataPecahan {}", dpmPecahan);
                            BillingEnergi billingEnergi = BillingEnergiMapper.mapToBillingEnergiPecahan(
                                    new BillingEnergi(),
                                    dil, dpmPecahan, lstDpmPecahan, dpm, true);
                            LOG.info("Energi {} {}", billingEnergi.id, billingEnergi.idpel);
                            return billingEnergiRepository.persistAndFlush(billingEnergi);
                        }).collect().asList())
                : Uni.createFrom().nullItem();
    }

    public Uni<Void> storeDataToWorkFlow(CustomerParam param) {
        return billingService.getInisialisasiBillingByIdpelThblrek(param)
                .onItem().transformToUni(dataInisialisasiBilling -> {
                    if (dataInisialisasiBilling == null) {
                        LOG.error("Failed to get Master Tarif, no data found for workflow");
                        return Uni.createFrom().voidItem();
                    } else {
                        LOG.info("Data WORKFLOW : [{} - {}]", dataInisialisasiBilling.workflowName,
                                dataInisialisasiBilling.workflowVersion);
                        LOG.info("SEND DATA TO WORKFLOW : [{}]", dataInisialisasiBilling.workflowName);
                        return engineProxy.runWorkflow(dataInisialisasiBilling)
                                .onFailure().invoke(ex -> LOG.error("Workflow execution failed: " + ex.getMessage()))
                                .onItem().transformToUni(response -> {
                                    return updateBill(param, response.readEntity(APIResponse.class));
                                });
                    }
                });
    }

    public Uni<Void> storeDataToWorkFlowV2(CustomerParam param) {
        LOG.info("PARAM CUSTOMER : {},{}", param.idpel, param.thblrek);
        return billingService.getInisialisasiBillingByIdpelThblrek(param)
                .onItem().transformToUni(dataInisialisasiBilling -> {
                    if (dataInisialisasiBilling == null) {
                        LOG.error("Failed to get Master Tarif, no data found for workflow");
                        return Uni.createFrom().voidItem();
                    } else {
                        return engineProxy.runWorkflowV2(dataInisialisasiBilling)
                                .onFailure().invoke(ex -> LOG.error("Workflow execution failed: " + ex.getMessage()))
                                .onItem().transformToUni(response -> {
                                    return updateBillV2(param, response.readEntity(APIResponse.class));
                                });
                    }
                });
    }

    public Uni<Void> storeDataToWorkFlowV2_new(CustomerParam param) {
        LOG.info("-- PARAM CUSTOMER : {},{}", param.idpel, param.thblrek);
        return billingServiceOptimize.getInisialisasiBillingByIdpelThblrek(param)
                .onItem().transformToUni(dataInisialisasiBilling -> {
                    if (dataInisialisasiBilling == null) {
                        LOG.error("-- Failed to get Master Tarif, no data found for workflow");
                        return Uni.createFrom().voidItem();
                    } else {
                        return saveLogHitungBilling(param,dataInisialisasiBilling);
                    }
                });
    }

    private Uni<Void> saveLogHitungBilling(CustomerParam paramCust, InisialisasiBillingDTO data) {
        LogJobHitungBilling log = new LogJobHitungBilling();
        log.id = UUID.randomUUID();
        log.idpel = paramCust.idpel;
        log.thblrek = paramCust.thblrek;
        log.payload = getPayload(data);
        log.status_proses = 0;
        log.message = "Inisialisasi log successfully saved";
        log.tglmulaiproses = LocalDateTime.now();
        log.tglselesaiproses = null;


        return logJobHitungBillingRepository.saveLogJobHitung(log)
                .onItem().invoke(() -> LOG.info("Inisialisasi log successfully saved"))
                .replaceWithVoid();
    }


    public String getPayload(InisialisasiBillingDTO dataInisialisasi) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(dataInisialisasi); // Langsung konversi DTO ke JSON
        } catch (JsonProcessingException e) {
            LOG.error("Error converting DTO to JSON", e);
            return "{}"; // Kembalikan JSON kosong untuk menghindari error lebih lanjut
        }
    }
    public Uni<InisialisasiBillingDTO> getInit(CustomerParam param){
        return initRepository.findSingle(param.thblrek,param.idpel)
                .onItem().ifNotNull().transform(item->{
                    try {
                        InisialisasiBillingDTO data= objectMapper.readValue(item.jsonInit,InisialisasiBillingDTO.class);
                        return data;
                    } catch (Exception ex){
                        return null;
                    }
                })
                .onItem().ifNull().fail().replaceWith(new InisialisasiBillingDTO());
    }
    public Uni<Void> storeDataToWorkFlowV3(CustomerParam param) {
        LOG.info("PARAM CUSTOMER : {},{}",param.idpel,param.thblrek);
        return getInit(param)
                .onItem().transformToUni(dataInisialisasiBilling -> {
                    if (dataInisialisasiBilling == null) {
                        LOG.error("Failed to get Master Tarif, no data found for workflow");
                        return Uni.createFrom().voidItem();
                    } else {
                        return engineProxy.runWorkflowV2(dataInisialisasiBilling)
                                .onFailure().invoke(ex -> LOG.error("Workflow execution failed: " + ex.getMessage()))
                                .onItem().transformToUni(response -> {
                                    return updateBillV2(param, response.readEntity(APIResponse.class));
                                });
                    }
                });
    }

    private Uni<APIResponse<Void>> updateBillV2(CustomerParam param, APIResponse<Map<String, Object>> response) {
        if (response.isSuccess()) {
            LOG.info("Data berhasil dikirim: [{}],[{}]", response.getMessage(), param.idpel);
            return billingRepository.findByIdpelThblrek(param.idpel, param.thblrek)
                    .onItem().ifNotNull().transformToUni(billing -> {
                        billing.tglrequesthitung = LocalDateTime.now();
                        return billingRepository.persistAndFlush(billing)
                                .replaceWith(APIResponse.ok());
                    })
                    .onItem().ifNull().continueWith(() -> {
                        LOG.error("Billing data not found for IDPEL: {}, THBLREK: {}", param.idpel, param.thblrek);
                        return APIResponse.error(
                                "Data tidak ditemukan untuk IDPEL: " + param.idpel + " dan THBLREK: " + param.thblrek);
                    });
        } else {
            LOG.error("Gagal mengirim data: [{}]", response.getMessage());
            return Uni.createFrom().item(APIResponse.error("Gagal mengirim data: " + response.getMessage()));
        }
    }

    private Uni<APIResponse<?>> updateBill(CustomerParam param, APIResponse<Map<String, Object>> response) {
        if (response.isSuccess()) {
            ObjectMapper objectMapper = new ObjectMapper();
            ObjectMapper objectMapperOutput = new ObjectMapper();
            LOG.info("UPDATE HITUNG BILL {}", response.getData());
            Map<String, Object> mapInput = (Map<String, Object>) response.getData().get("input");
            InisialisasiBillingDTO inputBilling = objectMapper.convertValue(mapInput.get("data"),
                    InisialisasiBillingDTO.class);

            objectMapperOutput.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            Output output = objectMapperOutput.convertValue(response.getData().get("output"), Output.class);

            return billingRepository.findByIdpelThblrek(param.idpel, param.thblrek)
                    .onItem()
                    .transformToUni(bill -> billingServiceOptimize.hitungDLPD(inputBilling,
                            BillingMapper.OutputToEntity(bill, output, null, null), null))
                    .onItem().transformToUni(billingdlpd -> billingRepository.persist(billingdlpd))
                    .onItem()
                    .transformToUni(savedBill -> billingEnergiRepository.findByIdpelThblrek(param.idpel, param.thblrek))
                    .onItem()
                    .transformToUni(billEnergi -> billingEnergiRepository
                            .persist(BillingEnergiMapper.OutputToEntity(billEnergi, inputBilling, output)))
                    .onFailure().transform(ex -> {
                        throw new AppException("Failed to store Bill WORKFLOW Energi Hitung: " + ex.getMessage());
                    })
                    .onItem().transform(savedBillEnergi -> {
                        return APIResponse.ok(savedBillEnergi);
                    });

        } else {
            throw new AppException("Failed to GET WORKFLOW: " + response.getMessage());
        }
    }

}
