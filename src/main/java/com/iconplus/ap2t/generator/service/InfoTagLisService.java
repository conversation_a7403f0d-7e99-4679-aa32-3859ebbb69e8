package com.iconplus.ap2t.generator.service;

import com.iconplus.ap2t.common.exception.AppException;
import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.common.utility.Constant;
import com.iconplus.ap2t.data.repository.billing.BillingRepository;
import com.iconplus.ap2t.data.repository.billing.InfoTagLisRepository;
import com.iconplus.ap2t.generator.dto.DataTaglisDto;
import com.iconplus.ap2t.generator.dto.InfoTagLisDTO;
import com.iconplus.ap2t.generator.mapper.DataTaglisMapper;
import com.iconplus.ap2t.generator.mapper.InfoTagLisMapper;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
@ApplicationScoped
public class InfoTagLisService {

    Logger LOG = LoggerFactory.getLogger(InfoTagLisService.class.getName());

    @Inject
    InfoTagLisRepository infoTagLisRepository;

    @Inject
    BillingRepository billingRepository;

    @WithTransaction
    public Uni<APIResponse<?>> getInfoTaglis(String idpel, String thblrek) {
        LOG.info("GET INFO TAGLIS: [{} - {}]", idpel, thblrek);
        // Convert the entity to a DTO
        //InfoTagLisDTO dto = VwTaglisMapper.toDto(entity);
        // Return the DTO wrapped in an APIResponse
        return infoTagLisRepository.find("idpel = ?1 and thblrek = ?2", idpel, thblrek)
                .firstResult()
                .onItem().transform(entity -> {
                    LOG.info("entity : "+entity);
                    if (entity == null) {
                        throw new AppException(Constant.DATA_TIDAK_DITEMUKAN);
                    }
                    InfoTagLisDTO dto = InfoTagLisMapper.toDto(entity);
                    return APIResponse.ok(dto);
                });
    }

    @WithTransaction
    public Uni<APIResponse<?>> getDataBillTaglis(String idpel, String thblrek) {
        LOG.info("GET DATA BILL TAGLIS : {} {}", idpel, thblrek);

        return billingRepository.findByIdpelThblrek(idpel, thblrek)
            .onItem().ifNotNull().transformToUni(item -> {
                DataTaglisDto dataDto = DataTaglisMapper.mapToDto(item);

                return APIResponse.ok(Uni.createFrom().item(dataDto));
            }).onItem().ifNull().failWith(new AppException(Constant.DATA_TIDAK_DITEMUKAN));
    }
}
