package com.iconplus.ap2t.generator.service;


import com.iconplus.ap2t.common.param.billing.DpmPecahanParam;
import com.iconplus.ap2t.data.entity.billing.DpmPecahan;
import com.iconplus.ap2t.data.repository.billing.DpmPecahanRepository;
import com.iconplus.ap2t.generator.mapper.DpmPecahanMapper;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.jboss.logging.Logger;

@Singleton
public class DpmPecahanService {
    private static final Logger LOGGER = Logger.getLogger(DpmPecahanService.class.getName());
    @Inject
    DpmPecahanRepository repository;

    @WithTransaction
    public Uni<Void> save(DpmPecahanParam req){
        LOGGER.info("[Start Service save DpmPecahan]");
        DpmPecahan pecahan = DpmPecahanMapper.mappToEntity(req);
        return repository.persist(pecahan)
                .flatMap(e -> repository.flush()) // Ensure changes are committed
                .onItem().invoke(() -> LOGGER.info("[End Service save DpmPecahan]"))
                .replaceWithVoid();
    }
}