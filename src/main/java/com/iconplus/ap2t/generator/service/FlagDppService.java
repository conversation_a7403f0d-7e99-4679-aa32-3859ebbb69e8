package com.iconplus.ap2t.generator.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.iconplus.ap2t.common.exception.AppException;
import com.iconplus.ap2t.common.param.billing.FlagDppParam;
import com.iconplus.ap2t.common.utility.Constant;
import com.iconplus.ap2t.common.utility.DataReference;
import com.iconplus.ap2t.common.utility.Helper;
import com.iconplus.ap2t.data.entity.billing.*;
import com.iconplus.ap2t.data.entity.master.MasterKodeProses;
import com.iconplus.ap2t.data.entity.master.MasterLebihBayarPascaLog;
import com.iconplus.ap2t.data.entity.master.MasterLebihBayarStimulusLog;
import com.iconplus.ap2t.data.repository.billing.*;
import com.iconplus.ap2t.data.repository.master.MasterKdProsesReposiroty;
import com.iconplus.ap2t.data.repository.master.MasterLBPascaRepository;
import com.iconplus.ap2t.data.repository.master.MasterLBStimulusRepository;
import com.iconplus.ap2t.data.repository.master.MasterSaldoTmpRepository;
import com.iconplus.ap2t.generator.mapper.*;
import com.iconplus.ap2t.generator.response.StandMeterDil;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.hibernate.reactive.mutiny.Mutiny;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;

@ApplicationScoped
public class FlagDppService {

    protected static final Logger log = LoggerFactory.getLogger(FlagDppService.class);

    protected static final DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    @Inject
    BillingRepository billingRepository;

    @Inject
    MasterKdProsesReposiroty masterKdProsesReposiroty;

    @Inject
    BillTabProsesRepository billTabProsesRepository;

    @Inject
    BillLogProsesRepository billLogProsesRepository;

    @Inject
    TransLBPascaRepository lebihBayarPascaRepository;

    @Inject
    MasterLBPascaRepository masterLBPascaRepository;

    @Inject
    MasterSaldoTmpRepository masterSaldoTmpRepository;

    @Inject
    TransKompensasiTmpRepository transKompensasiTmpRepository;

    @Inject
    TransLBStimulusRepository lebihBayarStimulusRepository;

    @Inject
    MasterLBStimulusRepository masterLBStimulusRepository;

    @Inject
    BillKirimCargoRepository billKirimCargoRepository;

    @Inject
    BillLogFlagDppRepository billLogFlagDppRepository;

    @Inject
    BillTabKdProsesKlpRepository billTabKdProsesKlpRepository;

    @Inject
    DpmRepository dpmRepository;

    @Inject
    CrmService crmService;

    @Inject
    Mutiny.SessionFactory sessionFactory;

    protected static String getNewBatch() {
        return LocalDateTime.now().format(formatter1) + Constant.BATCH_NUMBER.addAndGet(3);
    }

    public Uni<DataReference> execute(FlagDppParam request) {
        Instant start = Instant.now();
        DataReference record = new DataReference();
        String batch = getNewBatch();
        request.unitup = request.unitup.isBlank() ? "SEMUA" : request.unitup;
        log.info("starting flagging the dpp unitup : {} kdprosesklp : {} thblrek : {}", request.unitup, request.kdProsesKlp, request.thblrek);
        return sessionFactory.withTransaction((session, transaction) ->
            masterKdProsesReposiroty.findForFlagDpp(request).onItem().transformToMulti(billUps -> {
                log.info("item size kdprosesklp : {}", billUps.size());
                return Multi.createFrom().iterable(billUps);
            }).onItem().transformToUniAndConcatenate(billUp -> {
                log.info("processing unit : {} batch : {}", billUp.unitup, batch);
                return process(billUp, batch, request, record);
            }).onCompletion().call(() -> {
                log.info("all units have been processed");
                return masterKdProsesReposiroty.findSumRecSah(request.kdProsesKlp, request.unitap, request.thblrek)
                    .onItem().ifNull().continueWith(BigDecimal.ZERO)
                    .onItem().transformToUni(totalSum ->
                        billTabProsesRepository.updateRecSah(
                            request.userId, totalSum, request.kdProsesKlp, request.unitap, request.thblrek
                        ).invoke(updated -> {
                            log.info("all the {} data has been updated", updated);
                        })
                    ).onItem().transformToUni(xs -> {
                        if (record.getAllRecordSuccess() > 0) {
                            return setTheRekapAfterProcess(
                                request.thblrek, request.unitap, request.unitup,
                                request.kdProsesKlp, request.alasan, request.userId
                            ).onItem().transformToUni(cs -> {
                                String nextThbl = Helper.getThblWithOffSet(request.thblrek, 1);
                                return billTabProsesRepository.checkThblByUnitap(nextThbl, request.unitap).onItem().transformToUni(exist -> {
                                    if (exist < 1) {
                                        return setSuplaiTab(nextThbl, request.unitap, request.userId).call(() ->
                                            setSettingTab(request.userId, nextThbl, request.unitap, request.unitup, request.kdProsesKlp)
                                        );
                                    }
                                    return Uni.createFrom().voidItem();
                                });
                            });
                        }
                        return Uni.createFrom().voidItem();
                    });
            }).onCompletion().invoke(() -> {
                Instant end = Instant.now();
                Duration duration = Duration.between(start, end);
                log.info("processing {} data in {} ms", record.getAllTotalData(), duration.toMillis());
                log.info("with average per data is {}", duration.toMillis()/(record.getAllTotalData() > 0 ? record.getAllTotalData() : 1));
            }).collect().asList()).replaceWith(record);
    }

    protected Uni<DataReference> process(
            MasterKodeProses billUp, String batch, FlagDppParam request, DataReference record
    ) {
        return setStartBillLogProses(
            BillLogMapper.mapToEntityStart(new BillLogProses(), billUp, batch, request.userId)
        ).onFailure().transform(ex -> {
            ex.printStackTrace();
            throw new AppException("error starting bill tab proses up : " + ex.getMessage());
        }).chain(() -> {
            log.info("chaining bill set after log starts");
            return billingRepository.findCheckForFlagDpp(
                billUp.thblrek, billUp.unitap, billUp.unitup, billUp.kdProsesKlp
            ).onFailure().transform(ex -> {
                ex.printStackTrace();
                throw new AppException("error checking bill set : " + ex.getMessage());
            }).onItem().transformToUni(bs -> {
                log.info("bill set unitup : {} total : {}", billUp.unitup, bs.size());
                return mainProcess(bs.size(), billUp, record, request, batch).replaceWith(billUp).chain(btup -> {
                    log.info("chaning process bill log after bill set");
                    return setFinishBillLogProses(
                        batch, billUp.unitup, record.getMessage(), record.getTotalData(), record.getRecordSuccess(), record.getRecordFailed()
                    ).onFailure().transform(ex -> {
                        ex.printStackTrace();
                        throw new AppException("error checking bill set chaining : " + ex.getMessage());
                    });
                });
            });
        }).replaceWith(record);
    }

    @WithTransaction
    protected Uni<?> setStartBillLogProses(BillLogProses billLogProses) {
        return billLogProsesRepository.persist(billLogProses);
    }

    @WithTransaction
    protected Uni<?> setFinishBillLogProses(String batch, String unitup, String message, int totalData, int recordSuccess, int recordFailed) {
        return billLogProsesRepository.findByBatchAndUnit(batch, unitup).onItem().transformToUni(item -> {
            item.endProses = LocalDateTime.now();
            item.jmlRecProses = totalData;
            item.jmlRecGagal = recordFailed;
            item.jmlRecBerhasil = recordSuccess;
            item.message = message;
            return item.persist();
        }).onFailure().invoke(e -> log.error("Error finishing log: {}", e.getMessage()));
    }

    protected Uni<?> mainProcess(Integer billing, MasterKodeProses billUp, DataReference record, FlagDppParam request, String batch) {
        if (billing > 0) {
            record.setMessage("MASIH ADA PELANGGAN YANG BELUM BERHASIL DIHITUNG, MSG <> 0 OR MSG IS NULL");
            billUp.sah = "0";
            billUp.tglsah = LocalDateTime.now();
            billUp.bySah = request.userId;
            billUp.jmlrecsah = record.getRecordSuccess();
            return billUp.persist();
        } else {
            log.info("data bill parameter => thbl : {}, unitap : {}, unitup : {}, kdprosesklp : {}", billUp.thblrek, billUp.unitap, billUp.unitup, billUp.kdProsesKlp);
            return billingRepository.checkIfAnyDataHasNotVerificationDlpd(billUp.thblrek, billUp.unitap, billUp.unitup, billUp.kdProsesKlp)
                .chain(total -> {
                    if (total > 0) {
                        record.setMessage(total + " data belum verifikasi dlpd");
                        billUp.sah = "0";
                        billUp.tglsah = LocalDateTime.now();
                        billUp.bySah = request.userId;
                        billUp.jmlrecsah = record.getRecordSuccess();
                        return billUp.persist()
                            .replaceWith(record);
                    } else {
                        return processFlagging(billUp, record, request, batch);
                    }
                });
        }
    }

    @WithTransaction
    protected Uni<?> processFlagging(MasterKodeProses billUp, DataReference record, FlagDppParam request, String batch) {
        return billingRepository.findByThblAndUnitAndKdProsesKlp(
                billUp.thblrek, billUp.unitap, billUp.unitup, billUp.kdProsesKlp
            ).onFailure().transform(ex -> {
                ex.printStackTrace();
                throw new AppException("error find data bill : " + ex.getMessage());
            }).onItem().transformToMulti(dataBills -> {
                log.info("processing {} data, with all total is {}", record.getTotalData(), record.getAllTotalData());
                record.setTotalData(dataBills.size());
                record.setAllTotalData(record.getTotalData() + record.getAllTotalData());
                return Multi.createFrom().iterable(dataBills);
            }).onItem().transformToUniAndConcatenate(dataBill -> {
                log.info("unitup data bill {}, idpel {}", dataBill.unitup, dataBill.idpel);
                return setUpdateFlagDpp(dataBill, request.userId, batch)
                    .onFailure().transform(ex -> {
                        ex.printStackTrace();
                        throw new AppException("error update flagging dpp : " + ex.getMessage());
                    }).onItem().transformToUni(flag -> {
                        if (flag > 0) {
                            record.setRecordSuccess(record.getRecordSuccess() + 1);
                            record.setAllRecordSuccess(record.getAllRecordSuccess() + 1);
                        } else {
                            record.setRecordFailed(record.getRecordFailed() + 1);
                        }
                        log.info("processing flagging idpel : {}, result : {}", dataBill.idpel, flag);
                        return Uni.createFrom().item(flag);
                    })
//                    .chain(() -> {
//                        log.info("set new dpm => idpel : {}, thblrek : {}", dataBill.idpel, request.thblrek);
//                        return setDataDpm(dataBill.idpel, request.thblrek, request.userId)
//                            .onFailure(ex -> {
//                                ex.printStackTrace();
//                                log.error("error set data dpm : {}", ex.getMessage());
//                                return false;
//                            }).recoverWithNull();
//                    })
                    .chain(() -> {
                        log.info("check lebih bayar pasca => idpel : {}, thblrek : {}", dataBill.idpel, request.thblrek);
                        return checkLebihBayarPasca(dataBill.idpel, request.thblrek, request.userId)
                            .onFailure(ex -> {
                                ex.printStackTrace();
                                log.error("error lebih bayar pasca : {}", ex.getMessage());
                                return false;
                            }).recoverWithNull();
                    }).chain(() -> {
                        log.info("check lebih bayar stimulus => idpel : {}, thblrek : {}", dataBill.idpel, request.thblrek);
                        return checkLebihBayarStimulus(dataBill.idpel, request.thblrek, request.userId)
                            .onFailure(ex -> {
                                ex.printStackTrace();
                                log.error("error lebih bayar stimulus : {}", ex.getMessage());
                                return false;
                            }).recoverWithNull();
                    }).chain(() -> {
                        log.info("check saldo tmp => idpel : {}, thblrek : {}", dataBill.idpel, request.thblrek);
                        return checkSaldoTmp(dataBill.idpel, request.thblrek)
                            .onFailure(ex -> {
                                ex.printStackTrace();
                                log.error("error saldo tmp : {}", ex.getMessage());
                                return false;
                            }).recoverWithNull();
                    }).replaceWith(dataBill);
            }).onCompletion().invoke(() -> log.info("all the flagging process is done, starting the next step"))
            .collect().asList().onItem().transformToUni(listBill -> {
                log.info("detail processed record : {}", record.toString());
                if (record.getTotalData() < 1) {
                    record.setMessage("TIDAK DITEMUKAN PELANGGAN YANG AKAN DISAHKAN");
                    billUp.sah = "0";
                    billUp.tglsah = LocalDateTime.now();
                    billUp.bySah = request.userId;
                    billUp.jmlrecsah = record.getRecordSuccess();
                    return billUp.persist().replaceWith(listBill);
                } else if (record.getRecordFailed() < 1) {
                    record.setMessage("BERHASIL");
                    return setSuccessFlagDpp(
                        batch, billUp.unitap, billUp.unitup, billUp.kdProsesKlp, request.userId,
                        billUp.thblrek, record.getRecordSuccess(), true
                    ).replaceWith(listBill);
                } else if (record.getRecordFailed() > 0) {
                    record.setMessage("MASIH ADA PELANGGAN YANG BELUM BERHASIL DISAHKAN");
                    return setSuccessFlagDpp(
                        batch, billUp.unitap, billUp.unitup, billUp.kdProsesKlp, request.userId,
                        billUp.thblrek, record.getRecordSuccess(), false
                    ).replaceWith(listBill);
                }
                return Uni.createFrom().item(listBill);
            });
    }

    @WithTransaction
    protected Uni<Integer> setUpdateFlagDpp(Billing billing, String userId, String batch) {
        String kdGerak = "11";

        if (LocalDateTime.now().isAfter(LocalDateTime.parse(billing.thblrek + "06T00:00:00", DateTimeFormatter.ofPattern("yyyyMMdd'T'HH:mm:ss")))) {
            kdGerak = "13";
        }

        return billingRepository.setUpdateFlagDpp(
            billing.idpel, billing.thblrek, userId, batch, kdGerak
        );
    }

    @WithTransaction
    protected Uni<Void> setSuccessFlagDpp(
        String batch, String unitap, String unitup, String kdProsesKlp, String userId,
        String thblrek, int recordSuccess, boolean isAllCompleted
    ) {
        return billKirimCargoRepository.persist(
            BillCargoMapper.mapToEntity(
                new BillKirimCargo(), batch, unitap, unitup,
                kdProsesKlp, userId, recordSuccess, thblrek
            )
        ).onItem().transformToUni(cargo ->
            masterKdProsesReposiroty.setFlagSuccessDpp(
                kdProsesKlp, unitap, unitup, thblrek, userId, recordSuccess, isAllCompleted
            )
        ).replaceWithVoid();
    }

    @WithTransaction
    protected Uni<Dpm> setDataDpm(String idpel, String thblrek, String userId) {
        String nextThbl = Helper.getThblWithOffSet(thblrek, 1);
        return dpmRepository.getIdpelThbl(idpel, nextThbl).onItem().ifNull().switchTo(() ->
            crmService.getStandMeterByIdpel(idpel).onItem().transformToUni(stand -> {
                log.info("result stand meter crm by idpel : {} <=> {}", stand.getMessage(), stand.getStatus());
                return dpmRepository.getIdpelThbl(idpel, thblrek).onItem().transformToUni(oldDpm -> {
                    ObjectMapper mapper = new ObjectMapper();
                    StandMeterDil dil;
                    try {
                        String jsonData = mapper.writeValueAsString(stand.getData());
                        dil = mapper.readValue(jsonData, StandMeterDil.class);
                    } catch (JsonProcessingException e) {
                        throw new AppException("Error: failed to convert object Stan Meter Dil, " + e.getMessage());
                    }

                    return DpmMapper.mapToDpm(new Dpm(), oldDpm, dil, nextThbl, userId).persistAndFlush();
                }).onFailure().recoverWithItem(ex -> {
                    ex.printStackTrace();
                    log.error("error set new dpm, idpel : {}, thblrek : {}", idpel, thblrek);
                    return Uni.createFrom().voidItem();
                });
            }).replaceWith(dpmRepository.getIdpelThbl(idpel, nextThbl))
        );
    }

    @WithTransaction
    protected Uni<?> checkLebihBayarPasca(String idpel, String thblrek, String userId) {
        return lebihBayarPascaRepository.findByIdpelAndThblrek(idpel, thblrek).onItem().ifNotNull().transformToUni(item -> {
            item.postingBilling = "3";
            return masterLBPascaRepository.findByIdpel(item.idpel)
                .onItem().ifNotNull().transformToUni(masterLB -> {
                    if (masterLB.statusDkrp.isBlank() || masterLB.statusDkrp.trim().isEmpty()) {
                        masterLB.statusBilling = null;
                    } else if (masterLB.statusDkrp.equals("HITUNG")) {
                        masterLB.statusBilling = "SAH";
                    }

                    return MasterLBMapper.mapToEntityMasterLBPascaLog(
                        masterLB, new MasterLebihBayarPascaLog(), userId
                    ).persist().chain(() -> {
                        masterLB.rpSaldo = item.rpSaldoAkhir;
                        masterLB.tglCatat = LocalDateTime.now();
                        masterLB.ptgCatat = userId;

                        return masterLB.persist();
                    });
                }).chain(item::persist);
        });
    }

    @WithTransaction
    protected Uni<?> checkLebihBayarStimulus(String idpel, String thblrek, String userId) {
        return lebihBayarStimulusRepository.findByIdpelAndThblrek(idpel, thblrek).onItem().ifNotNull().transformToUni(item -> {
            item.postingBilling = "3";
            return masterLBStimulusRepository.findByIdpel(item.idpel)
                .onItem().ifNotNull().transformToUni(masterLB -> {
                    if (masterLB.statusDkrp == null || masterLB.statusDkrp.trim().isEmpty()) {
                        masterLB.statusBilling = null;
                    } else if (masterLB.statusDkrp.equals("HITUNG")) {
                        masterLB.statusBilling = "SAH";
                    }

                    return MasterLBMapper.mapToEntityMasterLBStimulusLog(
                        masterLB, new MasterLebihBayarStimulusLog(), userId
                    ).persist().chain(() -> {
                        masterLB.rpSaldo = item.rpSaldoAkhir;
                        masterLB.tglCatat = LocalDateTime.now();
                        masterLB.ptgCatat = userId;

                        return masterLB.persist();
                    });
                }).chain(item::persist);
        });
    }

    @WithTransaction
    protected Uni<?> checkSaldoTmp(String idpel, String thblrek) {
        return transKompensasiTmpRepository.findByIdpelAndThblrek(idpel, thblrek)
            .onItem().transformToUni(transKompensasiTmp -> {
                if (transKompensasiTmp != null) {
                    return masterSaldoTmpRepository.getMasterSaldoTmpByIdpel(idpel)
                        .onItem().transformToUni(tmp -> {
                            tmp.setRpSaldo(transKompensasiTmp.rpSaldoAkhir);
                            return masterSaldoTmpRepository.persistAndFlush(tmp);
                        });
                }
                return Uni.createFrom().voidItem();
            });
    }

    @WithTransaction
    protected Uni<?> setTheRekapAfterProcess(String thblrek, String unitap, String unitup, String kdProsesKlp, String alasan, String userId) {
        return billingRepository.findRekapJamNyala(
            thblrek, unitap, unitup, kdProsesKlp
        ).onItem().ifNotNull().transformToUni(jNyala -> {
            log.info("rekap jam nyala : {}", jNyala);
            if (jNyala.getLbrJnMax() > 0 || jNyala.getLbrJn2000() > 0 || jNyala.getLbrJn720() > 0 ||
                jNyala.getLbrKvarhLebih() > 0 || jNyala.getLbrKvarhLebih2() > 0 || jNyala.getLbrTgl1() > 0 ||
                jNyala.getLbrStSama() > 0) {

                log.info("logging to bill log");
                return billLogFlagDppRepository.persist(
                    BillLogMapper.mapToEntity(
                        new BillLogFlagDpp(), thblrek, unitap, unitup,
                        kdProsesKlp, alasan, userId, jNyala
                    )
                );
            }
            return Uni.createFrom().voidItem();
        }).onFailure().recoverWithItem(ex -> {
            ex.printStackTrace();
            return null;
        }).call(() -> {
            log.info("starting the recap after process");
            String batchRekap = getNewBatch();
            return recapProses(thblrek, unitap, unitup, kdProsesKlp, userId, batchRekap);
        });
    }

    //    bill$AP2T_rekap
    @WithTransaction
    protected Uni<?> recapProses(String thblrek, String unitap, String unitup, String kdProsesKlp, String user, String batch) {
        return masterKdProsesReposiroty.findForRekap(kdProsesKlp, unitap, unitup, thblrek)
            .onItem().transformToMulti(kdKlps -> Multi.createFrom().iterable(kdKlps))
            .onItem().transformToUniAndConcatenate(kdKlp -> {
                log.info("recap process master kode proses {}", kdKlp.kdProsesKlp);
                return billLogProsesRepository.persist(BillLogMapper.mapToRekapEntity(new BillLogProses(), kdKlp, batch, user))
                    .onItem().transformToUni(blp -> {
//                      seharusnya ada proses rekap 307 308 dst
                        kdKlp.tglBuatRekap = LocalDateTime.now();
                        kdKlp.byBuatRekap = user;
                        return masterKdProsesReposiroty.persist(kdKlp)
                            .onItem().transformToUni(rekapKlp -> {
                                blp.endProses = LocalDateTime.now();
                                blp.message = "BERHASIL";
                                blp.jmlRecBerhasil = 0;
                                return billLogProsesRepository.persist(blp);
                            });
                    }).onItem().transformToUni(billLogProses ->
                        masterKdProsesReposiroty.findRekapAp(kdKlp.kdProsesKlp, unitap, thblrek)
                            .onItem().transformToUni(mkps ->
                                billTabProsesRepository.updateRekap(
                                    !mkps.isEmpty(), user, kdKlp.kdProsesKlp, unitap, thblrek
                                )
                            )
                    );
            }).collect().asList();
    }

    @WithTransaction
    protected Uni<?> setSuplaiTab(String thblrek, String unitap, String userId) {
        String batchLog = getNewBatch();
        return billTabProsesRepository.checkSuplai(thblrek, unitap).onItem().transformToUni(suplai -> {
            if (suplai < 1) {
                return billLogProsesRepository.persist(BillLogMapper.mapEntityBillLogProsesSemua(new BillLogProses(), thblrek, unitap, userId, batchLog))
                    .onItem().transformToUni(billLogProses -> {
                        return billTabKdProsesKlpRepository.findByUnitap(unitap).onItem().transformToMulti(klps ->
                                Multi.createFrom().iterable(klps)
                            ).onItem().transformToUniAndConcatenate(klp -> {
                                return BillTabMapper.mapToEntityFromKlp(new BillTabProses(), klp, thblrek).persist();
                            }).collect().asList()
                            .onItem().transformToUni(bpts -> {
                                AtomicInteger btpsInc = new AtomicInteger(0);
                                return billTabProsesRepository.getBtpJoinUnit(thblrek, unitap).onItem().transformToMulti(btps ->
                                    Multi.createFrom().iterable(btps)
                                ).onItem().transformToUniAndConcatenate(btp -> {
                                    btpsInc.getAndIncrement();
                                    return masterKdProsesReposiroty.persist(
                                        BillTabMapper.mapEntityBillTabUp(new MasterKodeProses(), btp)
                                    );
                                }).onCompletion().call(() -> {
                                    billLogProses.endProses = LocalDateTime.now();
                                    billLogProses.jmlRecBerhasil = btpsInc.get();
                                    billLogProses.jmlRecProses = btpsInc.get();
                                    billLogProses.jmlRecGagal = 0;
                                    billLogProses.message = "BERHASIL";
                                    return billLogProses.persist();
                                }).collect().asList();
                            });
                    }).replaceWith(suplai);
            }
            return Uni.createFrom().item(suplai);
        });
    }

    @WithTransaction
    protected Uni<?> setSettingTab(String userId, String thblrek, String unitap, String unitup, String kdProsesKlp) {
        String batchLog = getNewBatch();
        return billTabProsesRepository.updateSettingTab(userId, thblrek, unitap, kdProsesKlp).onItem().transformToUni(st ->
            masterKdProsesReposiroty.updateSettingTab(thblrek, unitap, unitup, kdProsesKlp, userId).onItem().transformToUni(processedData ->
                BillLogMapper.mapToEntitySetting(
                    new BillLogProses(), thblrek, kdProsesKlp, unitap, unitup,
                    batchLog, userId, processedData
                ).persist()
            ).replaceWithVoid());
    }
}
