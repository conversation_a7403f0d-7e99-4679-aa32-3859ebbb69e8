//package com.iconplus.ap2t.generator.service;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.iconpln.ap2t.grpc.service.BillingInitService;
//import com.iconpln.ap2t.grpc.service.EnergiParam;
//import com.iconpln.ap2t.grpc.service.InitRequest;
//import com.iconpln.ap2t.grpc.service.InitResult;
//import com.iconplus.ap2t.common.param.CustomerParam;
//import com.iconplus.ap2t.common.param.DataEnergi;
//import io.quarkus.grpc.GrpcService;
//import io.smallrye.mutiny.Uni;
//import jakarta.inject.Inject;
//import org.hibernate.reactive.mutiny.Mutiny;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@GrpcService
//public class InitGrpcService implements BillingInitService {
//    final static Logger LOGGER = LoggerFactory.getLogger(BillingInitService.class);
//    @Inject
//    BillingServiceOptimize billingService;
//
//    @Inject
//    Mutiny.SessionFactory sessionFactory;
//
//    @Override
//    public Uni<InitResult> getInitialize(InitRequest request) {
//        CustomerParam param = new CustomerParam();
//        param.idpel = request.getIdpel();
//        param.thblrek = request.getThblrek();
//        return sessionFactory.withSession(session -> billingService.getInisialisasiBillingByIdpelThblrek(param)
//                .onItem().ifNull().failWith(() -> new RuntimeException("Data tidak ditemukan untuk param: " + param))
//                .onItem().ifNotNull().transformToUni(data->{
//                    ObjectMapper objectMapper = new ObjectMapper();
//                    try {
//                        String inisialisasi = objectMapper.writeValueAsString(data);
//                        LOGGER.info("inisialisasi : {}",inisialisasi);
//                    } catch (Exception ex){
//                        ex.printStackTrace();
//                        LOGGER.error(ex.getMessage());
//                    }
//                    List<EnergiParam> energiParams = new ArrayList<>();
//                    for(DataEnergi param1:data.getDataEnergi()){
//                        EnergiParam energi = EnergiParam.newBuilder()
//                                .setSlalwbp(param1.slalwbp.doubleValue()) //1
//                                .setSlwbp(param1.slalwbp.doubleValue()) //2
//                                .setKdmut(param1.kdmut) //3
//                                .setBatasBlok2(param1.batasBlok2.intValue()) //4
//                                .setFaktorQValue(param1.faktorQValue.doubleValue()) //5
//                                .setSahkvarh(param1.sahkvarh.doubleValue()) //6
//                                .setBatasBlok1(param1.batasBlok1.intValue()) //7
//                                .setBlok(param1.blok.intValue()) //8
//                                .setFakmkvam(param1.fakmkvam.doubleValue()) //9
//                                .setFaktorFakm(param1.faktorFakm.doubleValue()) //10
//                                .setFaktorFjn(param1.faktorFjn.doubleValue()) //11
//                                .setSlakvarh(param1.slakvarh.doubleValue()) //12
//                                .setFaktorFrt(param1.faktorFrt.doubleValue()) //13
//                                .setJenisBatas(param1.jenisBatas) //14
//                                .setDayamaxWbp(param1.dayamaxWbp!=null ? param1.dayamaxWbp.intValue() : 0) //15
//                                .setWorkflowVersionEnergi(param1.workflowVersionEnergi) //16
//                                .setTarif(param1.tarif) //17
//                                .setJnsmut(param1.jnsmut) //18
//                                .setDayaMax(param1.dayaMax!=null ? param1.dayaMax.intValue() : 0) //19
//                                .setBiayaBeban(param1.biayaBeban.doubleValue()) //20
//                                .setId(param1.id) //21
//                                .setPecahKe(param1.pecahKe) //22
//                                .setKdpt2(param1.kdpt2) //23
//                                .setWorkflowNameEnergi(param1.workflowNameEnergi) //24
//                                .setTglrubah(param1.tglrubah) //25
//                                .setStatusEmin(param1.statusEmin) //26
//                                .setEmin(param1.emin) //27
//                                .setFakmkvarh(param1.fakmkvarh.doubleValue()) //28
//                                .setJenisBatasEmin(param1.jenisBatasEmin) //29
//                                .setBiayaPakai1(param1.biayaPakai1.doubleValue()) //30
//                                .setFrt(param1.frt) //31
//                                .setBiayaPakai2(param1.biayaPakai2.doubleValue()) //32
//                                .setMetKwh(param1.metKwh) //33
//                                .setIdpel(param1.idpel) //34
//                                .setFaknpremium(param1.faknpremium!=null ? param1.faknpremium.doubleValue() : 0.0) //35
//                                .setBiayaPakai3(param1.biayaPakai3.doubleValue()) //36
//                                .setDaya(param1.daya.intValue()) //37
//                                .setFjn(param1.fjn) //38
//                                .setJamNyalaTanpaMeter(param1.jamNyalaTanpaMeter.doubleValue())//39
//                                .setThblmut(param1.thblmut) //40
//                                .setSahlwbp(param1.sahlwbp.doubleValue()) //41
//                                .setFaktorPValue(param1.faktorPValue.doubleValue()) //42
//                                .setSahwbp(param1.sahwbp.doubleValue()) //43
//                                .setFaktorKValue(param1.faktorKValue.doubleValue()) //44
//                                .setKdpt(param1.kdpt) //45
//                                .setFakm(param1.fakm.doubleValue()) //46
//                                .setFaktorNValue(param1.faktorNValue.doubleValue()) //47
//                                .setThblrek(param1.thblrek) //48
//                                .setBiayaKvar(param1.biayaKvar.doubleValue()) //49
//                                .setMetKvarh(param1.metKvarh) //50
//                                .setMetKvamaks(param1.metKvamaks) //51
//                                .setTglBacaLalu(param1.tglBacaLalu) //52
//                                .setTglBacaAkhir(param1.tglBacaAkhir) //53
//                                .setTglBacaBulanLalu(param1.tglBacaBulanLalu) //54
//                                .setTglbacabulanIni(param1.tglbacabulanIni) //55
//                                .setFraksiKwh(param1.fraksiKwh) //56
//                                .setFraksiDmp(param1.fraksiDmp) //57
//                                .setFraksiEmin(param1.fraksiEmin) //58
//                                .setFraksiHemat(param1.fraksiHemat) //59
//                                .setKvarhlebih(param1.kvarhlebih.doubleValue()) //60
//                                .setFlagDiskonStimulus(param1.flagDiskonStimulus) //61
//                                .setProsentaseDiskonStimulus(param1.prosentaseDiskonStimulus.doubleValue()) //62
//                                .setWorkflowDiskon(param1.workflowDiskon) //63
//                                .setWorkflowDiskonVersion(param1.workflowDiskonVersion) //64
//                                .setFlagPltsLama(param1.flagPltsLama) //65
//                                .setKapasitasPltsAtapLama(param1.kapasitasPltsAtapLama!=null ? param1.kapasitasPltsAtapLama.intValue() : 0) //66
//                                .setCapacityChargePltsLama(param1.capacityChargePltsLama.doubleValue()) //67
//                                .setKwhSaldoPltsLama(param1.kwhSaldoPltsLama.doubleValue()) //68
//                                .setFlagPltsBaru(param1.flagPltsBaru) //69
//                                .setKapasitasPltsAtapBaru(param1.kapasitasPltsAtapBaru!=null ? param1.kapasitasPltsAtapBaru.intValue() : 0) //70
//                                .setSlalwbpExport(param1.slalwbpExport!=null ? param1.slalwbpExport.doubleValue() : 0.0) //71
//                                .setSlawbpExport(param1.slawbpExport!=null ? param1.slawbpExport.doubleValue() : 0.0) //73
//                                .setSlakvarhExport(param1.slakvarhExport!=null ? param1.slakvarhExport.doubleValue() : 0.0) //74
//                                .setSahlwbpExport(param1.sahlwbpExport!=null ? param1.sahlwbpExport.doubleValue() : 0.0) //75
//                                .setSahwbpExport(param1.sahwbpExport!=null ? param1.sahwbpExport.doubleValue() : 0.0) //76
//                                .setSahkvarhExport(param1.sahkvarhExport!=null ? param1.sahkvarhExport.doubleValue() : 0.0) //77
//                                .build();
//                        energiParams.add(energi);
//                    }
//
//                    InitResult result = InitResult.newBuilder()
//                            .setId(data.id) //131
//                            .setIdpel(data.idpel)
//                            .setThblrek(data.thblrek)
//                            .setUnitap(data.unitap)
//                            .setUnitup(data.unitup)
//                            .setUnitupi(data.unitupi)
//                            .setJnsmut(data.jnsmut)
//                            .setKdmut(data.kdmut)
//                            .setThblmut(data.thblmut)
//                            .setTglnyala(data.tglnyala)
//                            .setTglrubah(data.tglrubah)
//                            .setKdproses(data.kdproses)
//                            .setNama(data.nama)
//                            .setNopel(data.nopel)
//                            .setTarif(data.tarif)
//                            .setKdpt(data.kdpt)
//                            .setDaya(data.daya.intValue())
//                            .setFaknpremium(data.faknpremium!=null ? data.faknpremium : "")
//                            .setKdbedajbst(data.kdbedajbst)
//                            .setKddk(data.kddk)
//                            .setKdbacameter(data.kdbacameter)
//                            .setKdmeterai(data.kdmeterai)
//                            .setLokettgk(data.lokettgk)
//                            .setKogol(data.kogol)
//                            .setSubkogol(data.subkogol)
//                            .setPemda(data.pemda)
//                            .setKdppj(data.kdppj)
//                            .setKdinkaso(data.kdinkaso)
//                            .setKdklp(data.kdklp)
//                            .setKdind(data.kdind)
//                            .setKdam(data.kdam)
//                            .setKdkvamaks(data.kdkvamaks)
//                            .setMaxDemand(data.maxDemand)
//                            .setFrt(data.frt)
//                            .setFjn(data.fjn)
//                            .setKdbpt(data.kdbpt)
//                            .setKdpembmeter(data.kdpembmeter)
//                            .setFakm(data.fakm!=null ? data.fakm.doubleValue() : 0.0)
//                            .setFakmkvam(data.fakmkvam.doubleValue())
//                            .setFakmkvarh(data.fakmkvarh.doubleValue())
//                            .setRpsewatrafoDil(data.rpsewatrafoDil!=null ? data.rpsewatrafoDil.doubleValue() : 0.0)
//                            .setFlagsewakap(data.flagsewakap)
//                            .setFaradkap(data.faradkap!=null ? data.faradkap.doubleValue() : 0.0)
//                            .setRpsewakap(data.rpsewakap!=null ? data.rpsewakap.doubleValue() : 0.0)
//                            .setKdangsa(data.kdangsa)
//                            .setRpangsa(data.rpangsa!=null ? data.rpangsa.doubleValue() : 0.0)
//                            .setLamaangsa(data.lamaangsa!=null ? data.lamaangsa.doubleValue() : 0.0)
//                            .setAngskea(data.angskea!=null ? data.angskea.doubleValue() : 0.0)
//                            .setThblangs1A(data.thblangs1a)
//                            .setKdangsb(data.kdangsb)
//                            .setRpangsb(data.rpangsb!=null ? data.rpangsb.doubleValue() : 0.0)
//                            .setLamaangsb(data.lamaangsb!=null ? data.lamaangsb.doubleValue() : 0.0)
//                            .setAngskeb(data.angskeb!=null ? data.angskeb.doubleValue() : 0.0)
//                            .setThblangs1B(data.thblangs1b)
//                            .setKdangsc(data.kdangsc)
//                            .setRpangsc(data.rpangsc!=null ? data.rpangsc.doubleValue() : 0.0)
//                            .setLamaangsc(data.lamaangsc!=null ? data.lamaangsc.doubleValue() : 0.0)
//                            .setAngskec(data.angskec!=null ? data.angskec.doubleValue() : 0.0)
//                            .setThblangs1C(data.thblangs1c)
//                            .setKdinvoice(data.kdinvoice)
//                            .setJnsmutAde(data.jnsmutAde)
//                            .setTahunKe(data.tahunKe)
//                            .setFlagppjangsa(data.flagppjangsa!=null ? data.flagppjangsa : 0)
//                            .setFlagppjangsb(data.flagppjangsb!=null ? data.flagppjangsb : 0)
//                            .setFlagppjangsc(data.flagppjangsc!=null ? data.flagppjangsc : 0)
//                            .setSahlwbp(data.sahlwbp.doubleValue())
//                            .setSahwbp(data.sahwbp!=null ? data.sahwbp.doubleValue() : 0.0)
//                            .setSahkvarh(data.sahkvarh!=null ? data.sahkvarh.doubleValue() : 0.0)
//                            .setSlalwbp(data.slalwbp.doubleValue())
//                            .setSlawbp(data.slawbp!=null ? data.slawbp.doubleValue() : 0.0)
//                            .setSlakvarh(data.slakvarh!=null ? data.slakvarh.doubleValue() : 0.0)
//                            .setDayamaxWbp(data.dayamaxWbp!=null ? data.dayamaxWbp.doubleValue() : 0.0)
//                            .setBatasBlok1(data.batasBlok1.intValue())
//                            .setBatasBlok2(data.batasBlok2.intValue())
//                            .setBiayaBeban(data.biayaBeban.doubleValue())
//                            .setBiayaKvar(data.biayaKvar.doubleValue())
//                            .setBiayaPakai1(data.biayaPakai1.doubleValue())
//                            .setBiayaPakai2(data.biayaPakai2.doubleValue())
//                            .setBiayaPakai3(data.biayaPakai3.doubleValue())
//                            .setBlok(data.blok.intValue())
//                            .setDayabpt(data.dayabpt!=null ? data.dayabpt.doubleValue() : 0.0)
//                            .setWorkflowName(data.workflowName)
//                            .setWorkflowVersion(data.workflowVersion)
//                            .setRpuap(data.rpuap!=null ? data.rpuap.doubleValue() : 0.0)
//                            .setRpoperasiparalel(data.rpoperasiparalel.doubleValue())
//                            .setRptarifbptrafo(data.rptarifbptrafo.doubleValue())
//                            .setRptmp(data.rptmp.doubleValue())
//                            .setRpsaldotmp(data.rpsaldotmp.doubleValue())
//                            .setKdpt2(data.kdpt2)
//                            .setTglbacalalu(data.tglbacalalu)
//                            .setTglbacaakhir(data.tglbacaakhir)
//                            .setFaktorFjn(data.faktorFjn.doubleValue())
//                            .setFaktorFakm(data.faktorFakm.doubleValue())
//                            .setFaktorFrt(data.faktorFrt.doubleValue())
//                            .setDayaMax(data.dayaMax.doubleValue())
//                            .setProsenPpj(data.prosenPpj.doubleValue())
//                            .setProsenPpn(data.prosenPpn.doubleValue())
//                            .setJamNyalaTanpaMeter(data.jamNyalaTanpaMeter.doubleValue())
//                            .setJenisBatas(data.jenisBatas)
//                            .setJenisBatasEmin(data.jenisBatasEmin)
//                            .setStatusEmin(data.statusEmin)
//                            .setEmin(data.emin.doubleValue())
//                            .setMetKwh(data.metKwh)
//                            .setMetKvarh(data.metKvarh)
//                            .setMetKvamaks(data.metKvamaks)
//                            .setFaktorKValue(data.faktorKValue.doubleValue())
//                            .setFaktorPValue(data.faktorPValue.doubleValue())
//                            .setFaktorQValue(data.faktorQValue.doubleValue())
//                            .setFaktorNValue(data.faktorNValue.doubleValue())
//                            .setFlagTypePembelianRec(data.flagTypePembelianRec)
//                            .setProsentaseRec(data.prosentaseRec!=null ? data.prosentaseRec.doubleValue() : 0.0)
//                            .setJmlunitRec(data.jmlunitRec!=null ? data.jmlunitRec.doubleValue() : 0.0)
//                            .setHargaRec(data.hargaRec!=null ? data.hargaRec.doubleValue() : 0.0) //249
//                            .addAllDataEnergi(energiParams)
//                            .build();
//
//                    return Uni.createFrom().item(result);
//                }));
//    }
//}
