package com.iconplus.ap2t.generator.service;

import com.iconplus.ap2t.common.param.billing.BacaUlangParam;
import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.data.repository.billing.BillingRepository;
import com.iconplus.ap2t.generator.proxy.AcmtProxy;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @AUTHOR RR
 * @DATE 17/10/2024
 */
@ApplicationScoped
public class AcmtProxyService {

    Logger LOG = LoggerFactory.getLogger(AcmtProxyService.class.getName());

    @Inject
    @RestClient
    AcmtProxy proxy;

    @Inject
    private BillingRepository billingRepository;

    @WithSession
    public Uni<APIResponse> acmtBacaMeter(BacaUlangParam param) {
        LOG.info("GET data baca ulang [{}]", param);

        return proxy.bacaUlangMeter(param)
                .onItem().transform(response -> response.readEntity(APIResponse.class))
                .onItem().call(apiResponse -> {
                    // Cari entitas berdasarkan ID
                    return billingRepository.find("idpel", param.idpel) // Ganti param.getId() sesuai dengan parameter input
                            .singleResult()
                            .onItem().transformToUni(billing -> {
                                if (billing == null) {
                                    return Uni.createFrom().failure(new IllegalArgumentException("Data tidak ditemukan untuk IDPEL: " + param.idpel));
                                }
                                // Update kolom
                                billing.postingbilling = "0";
                                billing.msg = "BACA ULANG";
                                // Simpan perubahan
                                return billing.persistAndFlush();
                            });
                });
    }

}
