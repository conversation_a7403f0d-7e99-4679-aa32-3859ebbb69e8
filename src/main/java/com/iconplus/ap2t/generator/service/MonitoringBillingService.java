package com.iconplus.ap2t.generator.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iconplus.ap2t.common.utility.ExcelExportUtil;
import com.iconplus.ap2t.data.param.Pagination;
import com.iconplus.ap2t.data.repository.billing.BillingRepository;
import io.quarkus.panache.common.Page;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@ApplicationScoped
public class MonitoringBillingService {
    Logger LOG = LoggerFactory.getLogger(BillingService.class.getName());

    @Inject
    BillingRepository billingRepository;

    @Inject
    ExcelExportUtil excelExportUtil;
    @Inject
    ObjectMapper objectMapper;

    public Uni<Pagination<List<Map<String, Object>>>> getMonitoringBillingRekap(String unitupi, String unitap, String unitup, String thblrek, Page page) {
        Uni<List<Map<String, Object>>> dataUni = billingRepository.getMonitoringBillingRekap(
                unitupi, unitap, unitup, thblrek, page.index, page.size
        );

        Uni<Long> countUni = billingRepository.getCountMonitoringBillingRekap(unitupi, unitap, unitup, thblrek
        );

        return dataUni
                .flatMap(data -> countUni
                        .map(totalItem -> {
                            int pageCount = (int) Math.ceil((double) totalItem / page.size);
                            return Pagination.of(totalItem, pageCount, page, data);
                        })
                );
    }

    public Uni<Pagination<List<Map<String, Object>>>> getMonitoringBillingDetil(String unitupi, String unitap, String unitup, String thblrek, Page page) {
        Uni<List<Map<String, Object>>> dataUni = billingRepository.getMonitoringBillingDetil(
                unitupi, unitap, unitup, thblrek, page.index, page.size
        );

        Uni<Long> countUni = billingRepository.getCountMonitoringBillingDetil(unitupi, unitap, unitup, thblrek
        );

        return dataUni
                .flatMap(data -> countUni
                        .map(totalItem -> {
                            int pageCount = (int) Math.ceil((double) totalItem / page.size);
                            return Pagination.of(totalItem, pageCount, page, data);
                        })
                );
    }

    public Uni<Pagination<List<Map<String, Object>>>> getMonitoringBillingDlpdRekap(String unitupi, String unitap, String unitup, String kdProsesKlp, String thblrek, String dlpd, Page page) {
        Uni<List<Map<String, Object>>> dataUni = billingRepository.getMonitoringBillingDlpdRekap(
                unitupi, unitap, unitup, kdProsesKlp, thblrek, dlpd, page.index, page.size
        );

        Uni<Long> countUni = billingRepository.getCountMonitoringBillingDlpdRekap(unitupi, unitap, unitup, kdProsesKlp, thblrek, dlpd
        );

        return dataUni
                .flatMap(data -> countUni
                        .map(totalItem -> {
                            int pageCount = (int) Math.ceil((double) totalItem / page.size);
                            return Pagination.of(totalItem, pageCount, page, data);
                        })
                );
    }

    public Uni<Pagination<List<Map<String, Object>>>> getMonitoringBillingDlpdDetil(String unitupi, String unitap, String unitup, String kdProsesKlp, String thblrek, String dlpd, Page page) {
        Uni<List<Map<String, Object>>> dataUni = billingRepository.getMonitoringBillingDlpdDetil(
                unitupi, unitap, unitup, kdProsesKlp, thblrek, dlpd, page.index, page.size
        );

        Uni<Long> countUni = billingRepository.getCountMonitoringBillingDlpdDetil(unitupi, unitap, unitup, kdProsesKlp, thblrek, dlpd
        );

        return dataUni
                .flatMap(data -> countUni
                        .map(totalItem -> {
                            int pageCount = (int) Math.ceil((double) totalItem / page.size);
                            return Pagination.of(totalItem, pageCount, page, data);
                        })
                );
    }

    public byte[] generateMonitoringDlpdRekapExcel(String unitupi, String unitap, String unitup,
            String kdProsesKlp, String thblrek, String dlpd, Page page) {
        LOG.info("Mulai EXPORT untuk THBLREK: {}, Page: {}", thblrek, page);

        try {
            LOG.debug("Mengambil data Monitoring DLPD Rekap dari repository...");
            List<Map<String, Object>> data = billingRepository
                    .getMonitoringBillingDlpdRekap(unitupi, unitap, unitup, kdProsesKlp, thblrek, dlpd, page.index, page.size)
                    .await().indefinitely();

            LOG.info("Jumlah data yang diambil: {}", data.size());

                // Jika data sudah berupa Map, konversi tidak perlu lagi
                // Tapi kalau bentuknya masih DTO, baru lakukan konversi seperti ini:
            List<Map<String, Object>> mappedData = data.stream()
                    .map(dto -> objectMapper.convertValue(dto, new TypeReference<Map<String, Object>>() {
            }))
                    .peek(map -> LOG.trace("Data baris: {}", map))
                    .collect(Collectors.toList());

            LOG.debug("Menghasilkan file Excel...");
            byte[] excelBytes = excelExportUtil.generateExcel(mappedData, "Monitoring DLPD Rekap");

            LOG.info("Excel berhasil dibuat. Ukuran file: {} bytes", excelBytes.length);
            return excelBytes;

        } catch (Exception e) {
            LOG.error("Gagal menghasilkan Excel Monitoring DLPD Rekap", e);
            throw new RuntimeException("Gagal menghasilkan file Excel", e);
        }
    }
    public byte[] generateMonitoringDlpdDetailExcel(String unitupi, String unitap, String unitup,
            String kdProsesKlp, String thblrek, String dlpd, Page page) {
        LOG.info("Mulai EXPORT untuk THBLREK: {}, Page: {}", thblrek, page);

        try {
            LOG.debug("Mengambil data Monitoring DLPD detail dari repository...");
            List<Map<String, Object>> data = billingRepository
                    .getMonitoringBillingDlpdDetil(unitupi, unitap, unitup, kdProsesKlp, thblrek, dlpd, page.index, page.size)
                    .await().indefinitely();

            LOG.info("Jumlah data yang diambil: {}", data.size());

                // Jika data sudah berupa Map, konversi tidak perlu lagi
                // Tapi kalau bentuknya masih DTO, baru lakukan konversi seperti ini:
            List<Map<String, Object>> mappedData = data.stream()
                    .map(dto -> objectMapper.convertValue(dto, new TypeReference<Map<String, Object>>() {
            }))
                    .peek(map -> LOG.trace("Data baris: {}", map))
                    .collect(Collectors.toList());

            LOG.debug("Menghasilkan file Excel...");
            byte[] excelBytes = excelExportUtil.generateExcel(mappedData, "Monitoring DLPD Rekap");

            LOG.info("Excel berhasil dibuat. Ukuran file: {} bytes", excelBytes.length);
            return excelBytes;

        } catch (Exception e) {
            LOG.error("Gagal menghasilkan Excel Monitoring DLPD Rekap", e);
            throw new RuntimeException("Gagal menghasilkan file Excel", e);
        }
    }

    public byte[] generateMonitoringBillingRekapExcel(String unitupi, String unitap, String unitup, String thblrek, Page page) {
        LOG.info("Mulai EXPORT untuk THBLREK: {}, Page: {}", thblrek, page);

        try {
            LOG.debug("Mengambil data Monitoring Billing Rekap dari repository...");
            List<Map<String, Object>> data = billingRepository
                    .getMonitoringBillingRekap(unitupi, unitap, unitup, thblrek, page.index, page.size)
                    .await().indefinitely();

            LOG.info("Jumlah data yang diambil: {}", data.size());

                // Jika data sudah berupa Map, konversi tidak perlu lagi
                // Tapi kalau bentuknya masih DTO, baru lakukan konversi seperti ini:
            List<Map<String, Object>> mappedData = data.stream()
                    .map(dto -> objectMapper.convertValue(dto, new TypeReference<Map<String, Object>>() {
            }))
                    .peek(map -> LOG.trace("Data baris: {}", map))
                    .collect(Collectors.toList());

            LOG.debug("Menghasilkan file Excel...");
            byte[] excelBytes = excelExportUtil.generateExcel(mappedData, "Monitoring Billing Rekap");

            LOG.info("Excel berhasil dibuat. Ukuran file: {} bytes", excelBytes.length);
            return excelBytes;

        } catch (Exception e) {
            LOG.error("Gagal menghasilkan Excel Monitoring Billing Rekap", e);
            throw new RuntimeException("Gagal menghasilkan file Excel", e);
        }
    }

    public byte[] generateMonitoringBillingDetailExcel(String unitupi, String unitap, String unitup, String thblrek, Page page) {
        LOG.info("Mulai EXPORT untuk THBLREK: {}, Page: {}", thblrek, page);

        try {
            LOG.debug("Mengambil data Monitoring Billing detail dari repository...");
            List<Map<String, Object>> data = billingRepository
                    .getMonitoringBillingDetil(unitupi, unitap, unitup, thblrek, page.index, page.size)
                    .await().indefinitely();

            LOG.info("Jumlah data yang diambil: {}", data.size());

            // Jika data sudah berupa Map, konversi tidak perlu lagi
            // Tapi kalau bentuknya masih DTO, baru lakukan konversi seperti ini:
            List<Map<String, Object>> mappedData = data.stream()
                    .map(dto -> objectMapper.convertValue(dto, new TypeReference<Map<String, Object>>() {
            }))
                    .peek(map -> LOG.trace("Data baris: {}", map))
                    .collect(Collectors.toList());

            LOG.debug("Menghasilkan file Excel...");
            byte[] excelBytes = excelExportUtil.generateExcel(mappedData, "Monitoring Billing Detail");

            LOG.info("Excel berhasil dibuat. Ukuran file: {} bytes", excelBytes.length);
            return excelBytes;

        } catch (Exception e) {
            LOG.error("Gagal menghasilkan Excel Monitoring Billing Detail", e);
            throw new RuntimeException("Gagal menghasilkan file Excel", e);
        }
    }

}
