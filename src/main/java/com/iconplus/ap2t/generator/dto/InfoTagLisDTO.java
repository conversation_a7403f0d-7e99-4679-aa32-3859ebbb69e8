package com.iconplus.ap2t.generator.dto;


import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class InfoTagLisDTO {
    private String id;
    private String namaUpi;
    private String namaAp;
    private String namaUp;
    private String idpel;
    private String thblrek;
    private String thblrekFormatted;
    private String noInvoice;
    private String nama;
    private String alamat;
    private String npwp;
    private String namaNpwp;
    private String alamatNpwp;
    private String nik;
    private String tarif;
    private BigDecimal daya;
    private String tarifLm;
    private BigDecimal dayaLm;
    private BigDecimal fakm;
    private BigDecimal fakmkvarh;
    private String fakmLm;
    private String fakmkvarhLm;
    private Integer jamnyala;
    private BigDecimal rpkwh;
    private BigDecimal rpptlBruto;
    private BigDecimal rpbebanKwhkva;
    private BigDecimal rptagDiskon;
    private String tglbacaakhir;
    private String tglbacaawal;
    private BigDecimal lwbp1Ini;
    private BigDecimal lwbp1Lalu;
    private BigDecimal wbp1Ini;
    private BigDecimal wbp1Lalu;
    private BigDecimal kvarh1Lalu;
    private BigDecimal kvarh1Ini;
    private String lwbpReal;
    private String wbpReal;
    private String kvarhReal;
    private String totalPemakaianLwbp;
    private String totalPemakaianWbp;
    private String totalPemakaianKvarh;
    private String pemakaianKwh;
    private BigDecimal lwbpKwhkva;
    private BigDecimal lwbpHarga;
    private BigDecimal lwbpRupiah;
    private BigDecimal wbpKwhkva;
    private BigDecimal wbpHarga;
    private BigDecimal wbpRupiah;
    private BigDecimal kvarhKwhkva;
    private BigDecimal kvarhHarga;
    private BigDecimal kvarhRupiah;
    private BigDecimal rpptlRupiah;
    private BigDecimal blok3Kwhkva;
    private BigDecimal blok3Harga;
    private BigDecimal blok3Rupiah;
    private BigDecimal blok4;
    private BigDecimal tarifBlok4;
    private BigDecimal rpblok4;
    private BigDecimal lwbpPecahKwhkva;
    private BigDecimal lwbpPecahHarga;
    private BigDecimal lwbpPecahRupiah;
    private BigDecimal wbpPecahKwhkva;
    private BigDecimal wbpPecahHarga;
    private BigDecimal wbpPecahRupiah;
    private BigDecimal blok3PecahKwhkva;
    private BigDecimal blok3PecahHarga;
    private BigDecimal blok3PecahRupiah;
    private BigDecimal prosenRelaksasi;
    private BigDecimal rpRelaksasi;
    private BigDecimal rpptlTagRupiah;
    private BigDecimal rplain2;
    private Integer flagLbhtagih;
    private BigDecimal restLbhtagih;
    private BigDecimal rpptldanlain2;
    private BigDecimal rpppn;
    private BigDecimal rptotallistrik;
    private BigDecimal rpsewaTagRupiah;
    private BigDecimal rpppnnonlistrik;
    private BigDecimal rptotalnonlistrik;
    private BigDecimal rptagTagRupiah;
    private BigDecimal prosenTag;
    private BigDecimal rpppjPtl;
    private BigDecimal ppjTagRupiah;
    private String rptagTagTerbilang;
    private BigDecimal rpptlBrutoRupiah;
    private String tgljttempo;
    private String ketLunas;
    private String statuskdgerakmasuk;
    private String tglbayar;
    private BigDecimal rpbkTagRupiah;
    private BigDecimal rpangsa;
    private BigDecimal rpangsb;
    private BigDecimal rpangsc;
    private String informasiFakturPajak;
    private BigDecimal totalRegRupiah;
    private Long isblok;
    private BigDecimal  kvarhtotalKwh;

    private Long flagPltsAtap;
    private Long isProsenppj;
    private BigDecimal rpKompensasi;
    private BigDecimal rprec;
    private BigDecimal rpppnRec;
    private BigDecimal rpptlRupiahBruto;
    private Long jmlunitrec;
    private BigDecimal hargarec;

    private String managerup;
    private String kotaup;


    public InfoTagLisDTO() {}

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNamaUpi() {
        return namaUpi;
    }

    public void setNamaUpi(String namaUpi) {
        this.namaUpi = namaUpi;
    }

    public String getNamaAp() {
        return namaAp;
    }

    public void setNamaAp(String namaAp) {
        this.namaAp = namaAp;
    }

    public String getNamaUp() {
        return namaUp;
    }

    public void setNamaUp(String namaUp) {
        this.namaUp = namaUp;
    }

    public String getIdpel() {
        return idpel;
    }

    public void setIdpel(String idpel) {
        this.idpel = idpel;
    }

    public String getThblrek() {
        return thblrek;
    }

    public void setThblrek(String thblrek) {
        this.thblrek = thblrek;
    }

    public String getThblrekFormatted() {
        return thblrekFormatted;
    }

    public void setThblrekFormatted(String thblrekFormatted) {
        this.thblrekFormatted = thblrekFormatted;
    }

    public String getNoInvoice() {
        return noInvoice;
    }

    public void setNoInvoice(String noInvoice) {
        this.noInvoice = noInvoice;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public String getAlamat() {
        return alamat;
    }

    public void setAlamat(String alamat) {
        this.alamat = alamat;
    }

    public String getNpwp() {
        return npwp;
    }

    public void setNpwp(String npwp) {
        this.npwp = npwp;
    }

    public String getNamaNpwp() {
        return namaNpwp;
    }

    public void setNamaNpwp(String namaNpwp) {
        this.namaNpwp = namaNpwp;
    }

    public String getAlamatNpwp() {
        return alamatNpwp;
    }

    public void setAlamatNpwp(String alamatNpwp) {
        this.alamatNpwp = alamatNpwp;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getTarif() {
        return tarif;
    }

    public void setTarif(String tarif) {
        this.tarif = tarif;
    }

    public BigDecimal getDaya() {
        return daya;
    }

    public void setDaya(BigDecimal daya) {
        this.daya = daya;
    }

    public String getTarifLm() {
        return tarifLm;
    }

    public void setTarifLm(String tarifLm) {
        this.tarifLm = tarifLm;
    }

    public BigDecimal getDayaLm() {
        return dayaLm;
    }

    public void setDayaLm(BigDecimal dayaLm) {
        this.dayaLm = dayaLm;
    }

    public BigDecimal getFakm() {
        return fakm;
    }

    public void setFakm(BigDecimal fakm) {
        this.fakm = fakm;
    }

    public BigDecimal getFakmkvarh() {
        return fakmkvarh;
    }

    public void setFakmkvarh(BigDecimal fakmkvarh) {
        this.fakmkvarh = fakmkvarh;
    }

    public String getFakmLm() {
        return fakmLm;
    }

    public void setFakmLm(String fakmLm) {
        this.fakmLm = fakmLm;
    }

    public String getFakmkvarhLm() {
        return fakmkvarhLm;
    }

    public void setFakmkvarhLm(String fakmkvarhLm) {
        this.fakmkvarhLm = fakmkvarhLm;
    }

    public Integer getJamnyala() {
        return jamnyala;
    }

    public void setJamnyala(Integer jamnyala) {
        this.jamnyala = jamnyala;
    }

    public BigDecimal getRpkwh() {
        return rpkwh;
    }

    public void setRpkwh(BigDecimal rpkwh) {
        this.rpkwh = rpkwh;
    }

    public BigDecimal getRpptlBruto() {
        return rpptlBruto;
    }

    public void setRpptlBruto(BigDecimal rpptlBruto) {
        this.rpptlBruto = rpptlBruto;
    }

    public BigDecimal getRpbebanKwhkva() {
        return rpbebanKwhkva;
    }

    public void setRpbebanKwhkva(BigDecimal rpbebanKwhkva) {
        this.rpbebanKwhkva = rpbebanKwhkva;
    }

    public BigDecimal getLwbp1Ini() {
        return lwbp1Ini;
    }

    public void setLwbp1Ini(BigDecimal lwbp1Ini) {
        this.lwbp1Ini = lwbp1Ini;
    }

    public BigDecimal getLwbp1Lalu() {
        return lwbp1Lalu;
    }

    public void setLwbp1Lalu(BigDecimal lwbp1Lalu) {
        this.lwbp1Lalu = lwbp1Lalu;
    }

    public BigDecimal getWbp1Ini() {
        return wbp1Ini;
    }

    public void setWbp1Ini(BigDecimal wbp1Ini) {
        this.wbp1Ini = wbp1Ini;
    }

    public BigDecimal getWbp1Lalu() {
        return wbp1Lalu;
    }

    public void setWbp1Lalu(BigDecimal wbp1Lalu) {
        this.wbp1Lalu = wbp1Lalu;
    }

    public BigDecimal getKvarh1Lalu() {
        return kvarh1Lalu;
    }

    public void setKvarh1Lalu(BigDecimal kvarh1Lalu) {
        this.kvarh1Lalu = kvarh1Lalu;
    }

    public BigDecimal getKvarh1Ini() {
        return kvarh1Ini;
    }

    public void setKvarh1Ini(BigDecimal kvarh1Ini) {
        this.kvarh1Ini = kvarh1Ini;
    }

    public String getLwbpReal() {
        return lwbpReal;
    }

    public void setLwbpReal(String lwbpReal) {
        this.lwbpReal = lwbpReal;
    }

    public String getWbpReal() {
        return wbpReal;
    }

    public void setWbpReal(String wbpReal) {
        this.wbpReal = wbpReal;
    }

    public String getKvarhReal() {
        return kvarhReal;
    }

    public void setKvarhReal(String kvarhReal) {
        this.kvarhReal = kvarhReal;
    }

    public String getTotalPemakaianLwbp() {
        return totalPemakaianLwbp;
    }

    public void setTotalPemakaianLwbp(String totalPemakaianLwbp) {
        this.totalPemakaianLwbp = totalPemakaianLwbp;
    }

    public String getTotalPemakaianWbp() {
        return totalPemakaianWbp;
    }

    public void setTotalPemakaianWbp(String totalPemakaianWbp) {
        this.totalPemakaianWbp = totalPemakaianWbp;
    }

    public String getTotalPemakaianKvarh() {
        return totalPemakaianKvarh;
    }

    public void setTotalPemakaianKvarh(String totalPemakaianKvarh) {
        this.totalPemakaianKvarh = totalPemakaianKvarh;
    }

    public String getPemakaianKwh() {
        return pemakaianKwh;
    }

    public void setPemakaianKwh(String pemakaianKwh) {
        this.pemakaianKwh = pemakaianKwh;
    }

    public BigDecimal getProsenRelaksasi() {
        return prosenRelaksasi;
    }

    public void setProsenRelaksasi(BigDecimal prosenRelaksasi) {
        this.prosenRelaksasi = prosenRelaksasi;
    }

    public BigDecimal getRpRelaksasi() {
        return rpRelaksasi;
    }

    public void setRpRelaksasi(BigDecimal rpRelaksasi) {
        this.rpRelaksasi = rpRelaksasi;
    }

    public BigDecimal getRpptlTagRupiah() {
        return rpptlTagRupiah;
    }

    public void setRpptlTagRupiah(BigDecimal rpptlTagRupiah) {
        this.rpptlTagRupiah = rpptlTagRupiah;
    }

    public BigDecimal getRplain2() {
        return rplain2;
    }

    public void setRplain2(BigDecimal rplain2) {
        this.rplain2 = rplain2;
    }

    public Integer getFlagLbhtagih() {
        return flagLbhtagih;
    }

    public void setFlagLbhtagih(Integer flagLbhtagih) {
        this.flagLbhtagih = flagLbhtagih;
    }

    public BigDecimal getRestLbhtagih() {
        return restLbhtagih;
    }

    public void setRestLbhtagih(BigDecimal restLbhtagih) {
        this.restLbhtagih = restLbhtagih;
    }

    public BigDecimal getRpptldanlain2() {
        return rpptldanlain2;
    }

    public void setRpptldanlain2(BigDecimal rpptldanlain2) {
        this.rpptldanlain2 = rpptldanlain2;
    }

    public BigDecimal getRpppn() {
        return rpppn;
    }

    public void setRpppn(BigDecimal rpppn) {
        this.rpppn = rpppn;
    }

    public BigDecimal getRptotallistrik() {
        return rptotallistrik;
    }

    public void setRptotallistrik(BigDecimal rptotallistrik) {
        this.rptotallistrik = rptotallistrik;
    }

    public BigDecimal getRpsewaTagRupiah() {
        return rpsewaTagRupiah;
    }

    public void setRpsewaTagRupiah(BigDecimal rpsewaTagRupiah) {
        this.rpsewaTagRupiah = rpsewaTagRupiah;
    }

    public BigDecimal getRpppnnonlistrik() {
        return rpppnnonlistrik;
    }

    public void setRpppnnonlistrik(BigDecimal rpppnnonlistrik) {
        this.rpppnnonlistrik = rpppnnonlistrik;
    }

    public BigDecimal getRptotalnonlistrik() {
        return rptotalnonlistrik;
    }

    public void setRptotalnonlistrik(BigDecimal rptotalnonlistrik) {
        this.rptotalnonlistrik = rptotalnonlistrik;
    }

    public BigDecimal getRptagTagRupiah() {
        return rptagTagRupiah;
    }

    public void setRptagTagRupiah(BigDecimal rptagTagRupiah) {
        this.rptagTagRupiah = rptagTagRupiah;
    }

    public String getRptagTagTerbilang() {
        return rptagTagTerbilang;
    }

    public void setRptagTagTerbilang(String rptagTagTerbilang) {
        this.rptagTagTerbilang = rptagTagTerbilang;
    }

    public BigDecimal getRpptlBrutoRupiah() {
        return rpptlBrutoRupiah;
    }

    public void setRpptlBrutoRupiah(BigDecimal rpptlBrutoRupiah) {
        this.rpptlBrutoRupiah = rpptlBrutoRupiah;
    }

    public String getTgljttempo() {
        return tgljttempo;
    }

    public void setTgljttempo(String tgljttempo) {
        this.tgljttempo = tgljttempo;
    }

    public String getKetLunas() {
        return ketLunas;
    }

    public void setKetLunas(String ketLunas) {
        this.ketLunas = ketLunas;
    }

    public String getStatuskdgerakmasuk() {
        return statuskdgerakmasuk;
    }

    public void setStatuskdgerakmasuk(String statuskdgerakmasuk) {
        this.statuskdgerakmasuk = statuskdgerakmasuk;
    }

    public String getTglbayar() {
        return tglbayar;
    }

    public void setTglbayar(String tglbayar) {
        this.tglbayar = tglbayar;
    }

    public BigDecimal getRpbkTagRupiah() {
        return rpbkTagRupiah;
    }

    public void setRpbkTagRupiah(BigDecimal rpbkTagRupiah) {
        this.rpbkTagRupiah = rpbkTagRupiah;
    }

    public BigDecimal getRptagDiskon() {
        return rptagDiskon;
    }

    public void setRptagDiskon(BigDecimal rptagDiskon) {
        this.rptagDiskon = rptagDiskon;
    }

    public String getTglbacaakhir() {
        return tglbacaakhir;
    }

    public void setTglbacaakhir(String tglbacaakhir) {
        this.tglbacaakhir = tglbacaakhir;
    }

    public String getTglbacaawal() {
        return tglbacaawal;
    }

    public void setTglbacaawal(String tglbacaawal) {
        this.tglbacaawal = tglbacaawal;
    }

    public BigDecimal getProsenTag() {
        return prosenTag;
    }

    public void setProsenTag(BigDecimal prosenTag) {
        this.prosenTag = prosenTag;
    }

    public BigDecimal getRpppjPtl() {
        return rpppjPtl;
    }

    public void setRpppjPtl(BigDecimal rpppjPtl) {
        this.rpppjPtl = rpppjPtl;
    }

    public BigDecimal getPpjTagRupiah() {
        return ppjTagRupiah;
    }

    public void setPpjTagRupiah(BigDecimal ppjTagRupiah) {
        this.ppjTagRupiah = ppjTagRupiah;
    }

    public BigDecimal getRpangsa() {
        return rpangsa;
    }

    public void setRpangsa(BigDecimal rpangsa) {
        this.rpangsa = rpangsa;
    }

    public BigDecimal getRpangsb() {
        return rpangsb;
    }

    public void setRpangsb(BigDecimal rpangsb) {
        this.rpangsb = rpangsb;
    }

    public BigDecimal getRpangsc() {
        return rpangsc;
    }

    public void setRpangsc(BigDecimal rpangsc) {
        this.rpangsc = rpangsc;
    }

    public BigDecimal getLwbpKwhkva() {
        return lwbpKwhkva;
    }

    public void setLwbpKwhkva(BigDecimal lwbpKwhkva) {
        this.lwbpKwhkva = lwbpKwhkva;
    }

    public BigDecimal getLwbpHarga() {
        return lwbpHarga;
    }

    public void setLwbpHarga(BigDecimal lwbpHarga) {
        this.lwbpHarga = lwbpHarga;
    }

    public BigDecimal getLwbpRupiah() {
        return lwbpRupiah;
    }

    public void setLwbpRupiah(BigDecimal lwbpRupiah) {
        this.lwbpRupiah = lwbpRupiah;
    }

    public BigDecimal getWbpKwhkva() {
        return wbpKwhkva;
    }

    public void setWbpKwhkva(BigDecimal wbpKwhkva) {
        this.wbpKwhkva = wbpKwhkva;
    }

    public BigDecimal getWbpHarga() {
        return wbpHarga;
    }

    public void setWbpHarga(BigDecimal wbpHarga) {
        this.wbpHarga = wbpHarga;
    }

    public BigDecimal getWbpRupiah() {
        return wbpRupiah;
    }

    public void setWbpRupiah(BigDecimal wbpRupiah) {
        this.wbpRupiah = wbpRupiah;
    }

    public BigDecimal getKvarhKwhkva() {
        return kvarhKwhkva;
    }

    public void setKvarhKwhkva(BigDecimal kvarhKwhkva) {
        this.kvarhKwhkva = kvarhKwhkva;
    }

    public BigDecimal getKvarhHarga() {
        return kvarhHarga;
    }

    public void setKvarhHarga(BigDecimal kvarhHarga) {
        this.kvarhHarga = kvarhHarga;
    }

    public BigDecimal getKvarhRupiah() {
        return kvarhRupiah;
    }

    public void setKvarhRupiah(BigDecimal kvarhRupiah) {
        this.kvarhRupiah = kvarhRupiah;
    }
    
    public BigDecimal getRpptlRupiah() {
        return rpptlRupiah;
    }

    public void setRpptlRupiah(BigDecimal rpptlRupiah) {
        this.rpptlRupiah = rpptlRupiah;
    }

    public BigDecimal getBlok3Kwhkva() {
        return blok3Kwhkva;
    }

    public void setBlok3Kwhkva(BigDecimal blok3Kwhkva) {
        this.blok3Kwhkva = blok3Kwhkva;
    }

    public BigDecimal getBlok3Harga() {
        return blok3Harga;
    }

    public void setBlok3Harga(BigDecimal blok3Harga) {
        this.blok3Harga = blok3Harga;
    }

    // Getter dan Setter untuk blok3Rupiah
    public BigDecimal getBlok3Rupiah() {
        return blok3Rupiah;
    }

    public void setBlok3Rupiah(BigDecimal blok3Rupiah) {
        this.blok3Rupiah = blok3Rupiah;
    }

    // Getter dan Setter untuk blok4
    public BigDecimal getBlok4() {
        return blok4;
    }

    public void setBlok4(BigDecimal blok4) {
        this.blok4 = blok4;
    }

    // Getter dan Setter untuk tarifBlok4
    public BigDecimal getTarifBlok4() {
        return tarifBlok4;
    }

    public void setTarifBlok4(BigDecimal tarifBlok4) {
        this.tarifBlok4 = tarifBlok4;
    }

    // Getter dan Setter untuk roblok4
    public BigDecimal getRpblok4() {
        return rpblok4;
    }

    public void setRpblok4(BigDecimal rpblok4) {
        this.rpblok4 = rpblok4;
    }
    
    public String getInformasiFakturPajak() {
        return informasiFakturPajak;
    }

    public void setInformasiFakturPajak(String informasiFakturPajak) {
        this.informasiFakturPajak = informasiFakturPajak;
    }
    public BigDecimal getTotalRegRupiah() {
        return totalRegRupiah;
    }

    public void setTotalRegRupiah(BigDecimal totalRegRupiah) {
        this.totalRegRupiah = totalRegRupiah;
    }

    public BigDecimal getLwbpPecahKwhkva() {
        return lwbpPecahKwhkva;
    }

    public void setLwbpPecahKwhkva(BigDecimal lwbpPecahKwhkva) {
        this.lwbpPecahKwhkva = lwbpPecahKwhkva;
    }

    public BigDecimal getLwbpPecahHarga() {
        return lwbpPecahHarga;
    }

    public void setLwbpPecahHarga(BigDecimal lwbpPecahHarga) {
        this.lwbpPecahHarga = lwbpPecahHarga;
    }

    public BigDecimal getLwbpPecahRupiah() {
        return lwbpPecahRupiah;
    }

    public void setLwbpPecahRupiah(BigDecimal lwbpPecahRupiah) {
        this.lwbpPecahRupiah = lwbpPecahRupiah;
    }

    public BigDecimal getWbpPecahKwhkva() {
        return wbpPecahKwhkva;
    }

    public void setWbpPecahKwhkva(BigDecimal wbpPecahKwhkva) {
        this.wbpPecahKwhkva = wbpPecahKwhkva;
    }

    public BigDecimal getWbpPecahHarga() {
        return wbpPecahHarga;
    }

    public void setWbpPecahHarga(BigDecimal wbpPecahHarga) {
        this.wbpPecahHarga = wbpPecahHarga;
    }

    public BigDecimal getWbpPecahRupiah() {
        return wbpPecahRupiah;
    }

    public void setWbpPecahRupiah(BigDecimal wbpPecahRupiah) {
        this.wbpPecahRupiah = wbpPecahRupiah;
    }

    public BigDecimal getBlok3PecahKwhkva() {
        return blok3PecahKwhkva;
    }

    public void setBlok3PecahKwhkva(BigDecimal blok3PecahKwhkva) {
        this.blok3PecahKwhkva = blok3PecahKwhkva;
    }

    public BigDecimal getBlok3PecahHarga() {
        return blok3PecahHarga;
    }

    public void setBlok3PecahHarga(BigDecimal blok3PecahHarga) {
        this.blok3PecahHarga = blok3PecahHarga;
    }

    public BigDecimal getBlok3PecahRupiah() {
        return blok3PecahRupiah;
    }

    public void setBlok3PecahRupiah(BigDecimal blok3PecahRupiah) {
        this.blok3PecahRupiah = blok3PecahRupiah;
    }
    
    public Long getIsblok() {
        return isblok;
    }

    public void setIsblok(Long isblok) {
        this.isblok = isblok;
    }



    public BigDecimal getKvarhtotalKwh() {
        return kvarhtotalKwh;
    }

    public void setKvarhtotalKwh(BigDecimal kvarhtotalKwh) {
        this.kvarhtotalKwh = kvarhtotalKwh;
    }

    public Long getFlagPltsAtap() {
        return flagPltsAtap;
    }

    public void setFlagPltsAtap(Long flagPltsAtap) {
        this.flagPltsAtap = flagPltsAtap;
    }

    public Long getIsProsenppj() {
        return isProsenppj;
    }

    public void setIsProsenppj(Long isProsenppj) {
        this.isProsenppj = isProsenppj;
    }

    public BigDecimal getRpKompensasi() {
        return rpKompensasi;
    }

    public void setRpKompensasi(BigDecimal rpKompensasi) {
        this.rpKompensasi = rpKompensasi;
    }

    public BigDecimal getRprec() {
        return rprec;
    }

    public void setRprec(BigDecimal rprec) {
        this.rprec = rprec;
    }

    public BigDecimal getRpppnRec() {
        return rpppnRec;
    }

    public void setRpppnRec(BigDecimal rpppnRec) {
        this.rpppnRec = rpppnRec;
    }

    public BigDecimal getRpptlRupiahBruto() {
        return rpptlRupiahBruto;
    }

    public void setRpptlRupiahBruto(BigDecimal rpptlRupiahBruto) {
        this.rpptlRupiahBruto = rpptlRupiahBruto;
    }

    public Long getJmlunitrec() {
        return jmlunitrec;
    }

    public void setJmlunitrec(Long jmlunitrec) {
        this.jmlunitrec = jmlunitrec;
    }

    public BigDecimal getHargarec() {
        return hargarec;
    }

    public void setHargarec(BigDecimal hargarec) {
        this.hargarec = hargarec;
    }
    public String getManagerup() {
        return managerup;
    }

    public void setManagerup(String managerup) {
        this.managerup = managerup;
    }
    public String getKotaup() {
        return kotaup;
    }

    public void setKotaup(String kotaup) {
        this.kotaup = kotaup;
    }

}
