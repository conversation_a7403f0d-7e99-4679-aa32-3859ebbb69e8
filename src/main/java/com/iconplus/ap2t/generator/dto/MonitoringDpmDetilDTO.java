package com.iconplus.ap2t.generator.dto;

import java.math.BigDecimal;

public class MonitoringDpmDetilDTO {
    private String thblrek;
    private String idpel;
    private String nama;
    private String unitup;
    private String thblmut;
    private String jenis_mk;
    private String tarif;
    private BigDecimal daya;
    private String kdpt;
    private String kdpt_2;
    private String koderbm;
    private String kddk;
    private String kodeposting;
    private String keterangan_posting;
    private String tglbaca;
    private BigDecimal slalwbp;
    private BigDecimal sahlwbp;
    private BigDecimal slawbp;
    private BigDecimal sahwbp;
    private BigDecimal slakvarh;
    private BigDecimal sahkvarh;
    private BigDecimal dayamax;
    private BigDecimal dayamax_wbp;
    private BigDecimal sahlwbp_import;
    private BigDecimal sahwbp_import;
    private BigDecimal sahkvarh_import;
    private BigDecimal fkmkwh;
    private BigDecimal fkmkvarh;
    private String kdproses;
    private String kdkelompok;
    private String kdam;
    private String kd_pesan;
    private String tglbaca1;

    public String getKdpt_2() {
        return kdpt_2;
    }

    public void setKdpt_2(String kdpt_2) {
        this.kdpt_2 = kdpt_2;
    }

    public String getThblrek() {
        return thblrek;
    }

    public void setThblrek(String thblrek) {
        this.thblrek = thblrek;
    }

    public String getIdpel() {
        return idpel;
    }

    public void setIdpel(String idpel) {
        this.idpel = idpel;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public String getUnitup() {
        return unitup;
    }

    public void setUnitup(String unitup) {
        this.unitup = unitup;
    }

    public String getThblmut() {
        return thblmut;
    }

    public void setThblmut(String thblmut) {
        this.thblmut = thblmut;
    }

    public String getJenis_mk() {
        return jenis_mk;
    }

    public void setJenis_mk(String jenis_mk) {
        this.jenis_mk = jenis_mk;
    }

    public String getTarif() {
        return tarif;
    }

    public void setTarif(String tarif) {
        this.tarif = tarif;
    }

    public BigDecimal getDaya() {
        return daya;
    }

    public void setDaya(BigDecimal daya) {
        this.daya = daya;
    }

    public String getKdpt() {
        return kdpt;
    }

    public void setKdpt(String kdpt) {
        this.kdpt = kdpt;
    }

    public String getKoderbm() {
        return koderbm;
    }

    public void setKoderbm(String koderbm) {
        this.koderbm = koderbm;
    }

    public String getKddk() {
        return kddk;
    }

    public void setKddk(String kddk) {
        this.kddk = kddk;
    }

    public String getKodeposting() {
        return kodeposting;
    }

    public void setKodeposting(String kodeposting) {
        this.kodeposting = kodeposting;
    }

    public String getKeterangan_posting() {
        return keterangan_posting;
    }

    public void setKeterangan_posting(String keterangan_posting) {
        this.keterangan_posting = keterangan_posting;
    }

    public String getTglbaca() {
        return tglbaca;
    }

    public void setTglbaca(String tglbaca) {
        this.tglbaca = tglbaca;
    }

    public BigDecimal getSlalwbp() {
        return slalwbp;
    }

    public void setSlalwbp(BigDecimal slalwbp) {
        this.slalwbp = slalwbp;
    }

    public BigDecimal getSahlwbp() {
        return sahlwbp;
    }

    public void setSahlwbp(BigDecimal sahlwbp) {
        this.sahlwbp = sahlwbp;
    }

    public BigDecimal getSlawbp() {
        return slawbp;
    }

    public void setSlawbp(BigDecimal slawbp) {
        this.slawbp = slawbp;
    }

    public BigDecimal getSahwbp() {
        return sahwbp;
    }

    public void setSahwbp(BigDecimal sahwbp) {
        this.sahwbp = sahwbp;
    }

    public BigDecimal getSlakvarh() {
        return slakvarh;
    }

    public void setSlakvarh(BigDecimal slakvarh) {
        this.slakvarh = slakvarh;
    }

    public BigDecimal getSahkvarh() {
        return sahkvarh;
    }

    public void setSahkvarh(BigDecimal sahkvarh) {
        this.sahkvarh = sahkvarh;
    }

    public BigDecimal getDayamax() {
        return dayamax;
    }

    public void setDayamax(BigDecimal dayamax) {
        this.dayamax = dayamax;
    }

    public BigDecimal getDayamax_wbp() {
        return dayamax_wbp;
    }

    public void setDayamax_wbp(BigDecimal dayamax_wbp) {
        this.dayamax_wbp = dayamax_wbp;
    }

    public BigDecimal getSahlwbp_import() {
        return sahlwbp_import;
    }

    public void setSahlwbp_import(BigDecimal sahlwbp_import) {
        this.sahlwbp_import = sahlwbp_import;
    }

    public BigDecimal getSahwbp_import() {
        return sahwbp_import;
    }

    public void setSahwbp_import(BigDecimal sahwbp_import) {
        this.sahwbp_import = sahwbp_import;
    }

    public BigDecimal getSahkvarh_import() {
        return sahkvarh_import;
    }

    public void setSahkvarh_import(BigDecimal sahkvarh_import) {
        this.sahkvarh_import = sahkvarh_import;
    }

    public BigDecimal getFkmkwh() {
        return fkmkwh;
    }

    public void setFkmkwh(BigDecimal fkmkwh) {
        this.fkmkwh = fkmkwh;
    }

    public BigDecimal getFkmkvarh() {
        return fkmkvarh;
    }

    public void setFkmkvarh(BigDecimal fkmkvarh) {
        this.fkmkvarh = fkmkvarh;
    }

    public String getKdproses() {
        return kdproses;
    }

    public void setKdproses(String kdproses) {
        this.kdproses = kdproses;
    }

    public String getKdkelompok() {
        return kdkelompok;
    }

    public void setKdkelompok(String kdkelompok) {
        this.kdkelompok = kdkelompok;
    }

    public String getKdam() {
        return kdam;
    }

    public void setKdam(String kdam) {
        this.kdam = kdam;
    }

    public String getKd_pesan() {
        return kd_pesan;
    }

    public void setKd_pesan(String kd_pesan) {
        this.kd_pesan = kd_pesan;
    }

    public String getTglbaca1() {
        return tglbaca1;
    }

    public void setTglbaca1(String tglbaca1) {
        this.tglbaca1 = tglbaca1;
    }
}
