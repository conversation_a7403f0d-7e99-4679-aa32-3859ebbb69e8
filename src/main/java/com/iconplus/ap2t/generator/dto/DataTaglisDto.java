package com.iconplus.ap2t.generator.dto;

import java.math.BigDecimal;
import java.util.List;

public class DataTaglisDto {
    public String idpel;
    public String thblrek;
    public String unitupi;
    public String unitap;
    public String unitup;
    public String tarif;
    public String kdpt;
    public String kdpt2;
    public BigDecimal daya;
    public String faknpremium;
    public BigDecimal dayar312;
    public String kogol;
    public String subkogol;
    public String pemda;
    public String kdppj;
    public String kdinkaso;
    public String kdklp;
    public String kdind;
    public String kdam;
    public String kdkvamaks;
    public String maxDemand;
    public String frt;
    public String fjn;
    public String kdbpt;
    public BigDecimal dayabpt;
    public String kddayabpt;
    public String jnsmut;
    public String thblmut;
    public String kdmut;
    public String tglnyala;
    public String tglrubah;
    public String kdpembmeter;
    public BigDecimal fakm;
    public BigDecimal fakmkvarh;
    public BigDecimal fakmkvam;
    public String tglbacalalu;
    public String tglbacaakhir;
    public BigDecimal slalwbp;
    public BigDecimal sahlwbp;
    public BigDecimal slawbp;
    public BigDecimal sahwbp;
    public BigDecimal slakvarh;
    public BigDecimal sahkvarh;
    public BigDecimal sahkvamaks;
    public BigDecimal dayamaks;
    public BigDecimal sahkvamaxWbp;
    public BigDecimal dayamaxWbp;
    public BigDecimal rasioDaya;
    public BigDecimal kwhlwbp;
    public BigDecimal kwhwbp;
    public BigDecimal blok3;
    public BigDecimal pemkwh;
    public BigDecimal pemkvarh;
    public BigDecimal kelbkvarh;
    public BigDecimal jamnyala;
    public BigDecimal rpwbp;
    public BigDecimal rpkvarh;
    public BigDecimal rptb;
    public String kdangsa;
    public BigDecimal lamaangsa;
    public String thblangs1a;
    public BigDecimal angskea;
    public String kdangsb;
    public BigDecimal lamaangsb;
    public String thblangs1b;
    public BigDecimal angskeb;
    public String kdangsc;
    public BigDecimal lamaangsc;
    public String thblangs1c;
    public BigDecimal angskec;
    public BigDecimal rppln;
    public BigDecimal rpproduksi;
    public BigDecimal rpsubsidi;
    public BigDecimal rptdllama;
    public BigDecimal rptdlbaru;
    public BigDecimal rpselisih;
    public BigDecimal rpreduksi;
    public BigDecimal rpbk1;
    public BigDecimal rpbk2;
    public BigDecimal rpbk3;
    public String norek;
    public BigDecimal rprekBruto;
    public BigDecimal rprekNetto;
    public BigDecimal rptagBruto;
    public BigDecimal rptagNetto;
    public BigDecimal rpjbst;
    public BigDecimal rpkompensasi;
    public BigDecimal rpdiskonPremium;
    public BigDecimal rpdiskon;
    public String flagdiskon;
    public BigDecimal prosendiskon;
    public String kddiskon;
    public String jnsdiskon;
    public BigDecimal rpinvoice;
    public String kdinvoice;
    public String statusemin;
    public BigDecimal fraksibptrafo;
    public BigDecimal trfbptrafo;
    public BigDecimal trfsewakap;
    public BigDecimal slalwbpPasang;
    public BigDecimal sahlwbpCabut;
    public BigDecimal slawbpPasang;
    public BigDecimal sahwbpCabut;
    public BigDecimal slakvarhPasang;
    public BigDecimal sahkvarhCabut;
    public BigDecimal sahkvamaksCabut;
    public BigDecimal dayamaksCabut;
    public BigDecimal dayajbst;
    public String kdbedajbst;
    public BigDecimal jumlahPadam;
    public String tglinisialisasi;
    public String tglcatat;
    public String tglhitung;
    public String tglsah;
    public String initby;
    public String catatby;
    public String hitungby;
    public String sahby;
    public BigDecimal pecahan;
    public String msg;
    public BigDecimal batch;
    public String postingbilling;
    public String kdproses;
    public String kdprosesklp;
    public String tgljttempo;
    public String tglbayarAwal;
    public String tglbayarAkhir;
    public BigDecimal dlpd;
    public String tglDpp;
    public BigDecimal rpptlplus;
    public BigDecimal rpptlminus;
    public BigDecimal prosenppj;
    public String tglKargo;
    public BigDecimal slalwbp1;
    public BigDecimal sahlwbp1;
    public BigDecimal slalwbp2;
    public BigDecimal sahlwbp2;
    public BigDecimal wbprataIns;
    public BigDecimal lwbp1rataIns;
    public String tipeskema;
    public Integer skema4;
    public BigDecimal sahlwbp1Cabut;
    public BigDecimal slalwbp1Pasang;
    public BigDecimal sahlwbp2Cabut;
    public BigDecimal slalwbp2Pasang;
    public BigDecimal kwhWbpPadamHari;
    public Integer jmlpadam;
    public String kdgerak;
    public BigDecimal rpppjptl;
    public BigDecimal rpppjangsa;
    public BigDecimal rpppjangsb;
    public BigDecimal rpppjangsc;
    public String tarifLmPindahTrf;
    public BigDecimal dayaLmPindahTrf;
    public String kdptLamaPindahTrf;
    public BigDecimal dlpdLm;
    public String dlpdFkm;
    public String dlpdKvarh;
    public String dlpd3bln;
    public String dlpdJnsmutasi;
    public String dlpdTglbaca;
    public String alasanKoreksi;
    public BigDecimal pemkwhLwbp1;
    public BigDecimal pemkwhLwbp2;
    public BigDecimal rpppnR3;
    public BigDecimal rpppnBptrafo;
    public BigDecimal rpppnSewatrafo;
    public BigDecimal rpppnOpspararel;
    public BigDecimal rpppnSewakap;
    public BigDecimal rpppnLain;
    public BigDecimal rptagMat;
    public BigDecimal rpppnRec;
    public BigDecimal rprec;
    public BigDecimal rpppnUap;
    public BigDecimal kwhKompor;
    public BigDecimal rpKompor;
    public BigDecimal blok4;
    public BigDecimal rpblok4;
    public BigDecimal rpopsel;
    public BigDecimal sahlwbpExport;
    public BigDecimal sahwbpExport;
    public BigDecimal sahkvarhExport;
    public BigDecimal slalwbpPasangExp;
    public BigDecimal sahlwbpCabutExp;
    public BigDecimal slawbpPasangExp;
    public BigDecimal sahwbpCabutExp;
    public BigDecimal slakvarhPasangExp;
    public BigDecimal sahkvarhCabutExp;
    public String eminRmPltsBaru;
    public String eminRmPltsLama;
    public BigDecimal rpsewatrafoDil;
    public String flagsewakap;
    public BigDecimal faradkap;
    public String nama;
    public String alamat;
    public String nopel;
    public String kddk;
    public String kdbacameter;
    public String kdrekg;
    public String copyrek;
    public String kdmeterai;
    public String lokettgk;
    public Integer tahunKe;
    public String jnsmutAde;
    public Integer flagppjangsa;
    public Integer flagppjangsb;
    public Integer flagppjangsc;
    public Long isblok;
    public List<NilaiRupiahDto> uraianNilaiRupiah;

//    public BigDecimal rpmat;
//    public BigDecimal rptag;
//    public BigDecimal rpbpju;
//    public BigDecimal rpbptrafo;
//    public BigDecimal rpsewatrafo;
//    public BigDecimal rplwbp;
//    public BigDecimal rpblok3;
//    public BigDecimal rpbeban;
//    public BigDecimal rpptl;
//    public BigDecimal rpppn;
//    public BigDecimal rpsewakap;
//    public BigDecimal rpangsa;
//    public BigDecimal rpangsb;
//    public BigDecimal rpangsc;



    public static class NilaiRupiahDto {
        public String uraian;
        public BigDecimal nilai;

        public NilaiRupiahDto(String uraian, BigDecimal nilai) {
            this.uraian = uraian;
            this.nilai = nilai;
        }

    }
}
