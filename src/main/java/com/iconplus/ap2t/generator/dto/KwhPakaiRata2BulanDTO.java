package com.iconplus.ap2t.generator.dto;

import java.math.BigDecimal;

public class KwhPakaiRata2BulanDTO {
    private BigDecimal lwbppakaiRata;
    private BigDecimal wbppakaiRata;
    private BigDecimal kvarhpakaiRata;

    public KwhPakaiRata2BulanDTO(Double lwbppakaiRata, Double wbppakaiRata, Double kvarhpakaiRata) {
        this.lwbppakaiRata = lwbppakaiRata != null ? BigDecimal.valueOf(lwbppakaiRata) : BigDecimal.ZERO;
        this.wbppakaiRata = wbppakaiRata != null ? BigDecimal.valueOf(wbppakaiRata) : BigDecimal.ZERO;
        this.kvarhpakaiRata = kvarhpakaiRata != null ? BigDecimal.valueOf(kvarhpakaiRata) : BigDecimal.ZERO;
    }

    public BigDecimal getLwbppakaiRata() {
        return lwbppakaiRata;
    }

    public void setLwbppakaiRata(BigDecimal lwbppakaiRata) {
        this.lwbppakaiRata = lwbppakaiRata;
    }

    public BigDecimal getWbppakaiRata() {
        return wbppakaiRata;
    }

    public void setWbppakaiRata(BigDecimal wbppakaiRata) {
        this.wbppakaiRata = wbppakaiRata;
    }

    public BigDecimal getKvarhpakaiRata() {
        return kvarhpakaiRata;
    }

    public void setKvarhpakaiRata(BigDecimal kvarhpakaiRata) {
        this.kvarhpakaiRata = kvarhpakaiRata;
    }

    @Override
    public String toString() {
        return "KwhPakaiRata2BulanDTO{" +
                "lwbppakaiRata=" + lwbppakaiRata +
                ", wbppakaiRata=" + wbppakaiRata +
                ", kvarhpakaiRata=" + kvarhpakaiRata +
                '}';
    }
}
