package com.iconplus.ap2t.generator.dto;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

public class KoreksiBillingPltsDTO {

    public String idpel;
    public String nama;
    public String alamat;
    public String unitup;
    public BigDecimal dlpd;
    public String bulanRekening;
    public String tahun;
    public String tarif;
    public Long daya;
    public String tarifLama;
    public BigDecimal dayaLama;
    public String frt;
    public String blthMutasi;
    public String jenisMK;
    public String tglBacaLalu;
    public String tglBacaAkhir;
    public LocalDate tglPerubahan;
    public BigDecimal lwbpLalu;
    public BigDecimal lwbpAkhir;
    public BigDecimal wbpLalu;
    public BigDecimal wbpAkhir;
    public BigDecimal kvarhLalu;
    public BigDecimal kvarhAkhir;
    public BigDecimal kvamaxAkhir;
    public BigDecimal kvawbpAkhir;
    public BigDecimal kwhLwbp;
    public BigDecimal kwhWpb;
    public BigDecimal kwhKvarh;
    public BigDecimal kwhKvamax;
    public BigDecimal kwhKvawbp;
    public BigDecimal jamNyala;
    public List<DataBillEnergiPltsDTO> dataBillEnergiPltsList;
    public List<UraianNilaiPltsDTO> uraianNilaiPltsList;

    public static class UraianNilaiPltsDTO {
        public String uraian;
        public BigDecimal nilai;

        public UraianNilaiPltsDTO(String uraian, BigDecimal nilai) {
            this.uraian = uraian;
            this.nilai = nilai;
        }
    }

    public static class DataBillEnergiPltsDTO {
        public String id;
        public int pecahKe;
        public String jenisEnergi;
        public BigDecimal lwbpLalu;
        public BigDecimal lwbpAkhir;
        public BigDecimal wbpLalu;
        public BigDecimal wbpAkhir;
        public BigDecimal kvarhLalu;
        public BigDecimal kvarhAkhir;
        public BigDecimal kvamaxAkhir;
        public BigDecimal kvawbpAkhir;
        public LocalDate tglPerubahan;

        public BigDecimal slalwbpExport;
        public BigDecimal slawbpExport;
        public BigDecimal slakvarhExport;
        public BigDecimal sahlwbpExport;
        public BigDecimal sahwbpExport;
        public BigDecimal sahkvarhExport;

        public DataBillEnergiPltsDTO(String id, int pecahKe,String jenisEnergi,BigDecimal lwbpLalu, BigDecimal lwbpAkhir, BigDecimal wbpLalu, BigDecimal wbpAkhir, BigDecimal kvarhLalu, BigDecimal kvarhAkhir, BigDecimal kvamaxAkhir, BigDecimal kvawbpAkhir, LocalDate tglPerubahan,
        BigDecimal slalwbpExport, BigDecimal slawbpExport, BigDecimal slakvarhExport, BigDecimal sahlwbpExport, BigDecimal sahwbpExport, BigDecimal sahkvarhExport) {
            this.id = id;
            this.pecahKe = pecahKe;
            this.jenisEnergi = jenisEnergi;
            this.lwbpLalu = lwbpLalu;
            this.lwbpAkhir = lwbpAkhir;
            this.wbpLalu = wbpLalu;
            this.wbpAkhir = wbpAkhir;
            this.kvarhLalu = kvarhLalu;
            this.kvarhAkhir = kvarhAkhir;
            this.kvamaxAkhir = kvamaxAkhir;
            this.kvawbpAkhir = kvawbpAkhir;
            this.tglPerubahan = tglPerubahan;
            
            this.slalwbpExport = slalwbpExport;
            this.slawbpExport = slawbpExport;
            this.slakvarhExport = slakvarhExport;
            this.sahlwbpExport = sahlwbpExport;
            this.sahwbpExport = sahwbpExport;
            this.sahkvarhExport = sahkvarhExport;
        }
    }



}