package com.iconplus.ap2t.generator.dto;

import java.math.BigDecimal;

public class MonitoringDpmRekapDTO {
    private String thblrek;
    private String unitupi;
    private String unitap;
    private String unitup;
    private String kdproses;
    private String kdkelompok;
    private String kodeposting;
    private String keterangan_posting;
    private BigDecimal jumlah;

    public String getUnitap() {
        return unitap;
    }

    public void setUnitap(String unitap) {
        this.unitap = unitap;
    }

    public String getThblrek() {
        return thblrek;
    }

    public void setThblrek(String thblrek) {
        this.thblrek = thblrek;
    }

    public String getUnitupi() {
        return unitupi;
    }

    public void setUnitupi(String unitupi) {
        this.unitupi = unitupi;
    }

    public String getUnitup() {
        return unitup;
    }

    public void setUnitup(String unitup) {
        this.unitup = unitup;
    }

    public String getKdproses() {
        return kdproses;
    }

    public void setKdproses(String kdproses) {
        this.kdproses = kdproses;
    }

    public String getKdkelompok() {
        return kdkelompok;
    }

    public void setKdkelompok(String kdkelompok) {
        this.kdkelompok = kdkelompok;
    }

    public String getKodeposting() {
        return kodeposting;
    }

    public void setKodeposting(String kodeposting) {
        this.kodeposting = kodeposting;
    }

    public String getKeterangan_posting() {
        return keterangan_posting;
    }

    public void setKeterangan_posting(String keterangan_posting) {
        this.keterangan_posting = keterangan_posting;
    }

    public BigDecimal getJumlah() {
        return jumlah;
    }

    public void setJumlah(BigDecimal jumlah) {
        this.jumlah = jumlah;
    }
}
