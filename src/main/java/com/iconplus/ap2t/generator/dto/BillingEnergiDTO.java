package com.iconplus.ap2t.generator.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
public class BillingEnergiDTO {
    public String id;

    public String idpel;

    public String thblrek;

    public String jenisEnergi;

    public Integer pecahKe;

    public String jnsmut;

    public String thblmut;

    public String kdmut;

    public LocalDate tglrubah;

    public String tarif;

    public String kdpt;

    public String kdpt2;

    public BigDecimal daya;

    public BigDecimal dayar312;

    public String kogol;

    public String subkogol;

    public String faknpremium;

    public String kdam;

    public String frt;

    public String fjn;

    public String kdind;

    public String maxDemand;

    public String kdkvamaks;

    public BigDecimal sahkvamaxWbp;

    public BigDecimal dayamaxWbp;

    public BigDecimal fakm;

    public BigDecimal fakmkvarh;

    public BigDecimal fakmkvam;

    public String kdpembmeter;

    public BigDecimal dayajbst;

    public String kdbedajbst;

    public LocalDateTime tglinisialisasi;

    public String tglbacalalu;

    public String tglbacaakhir;

    public BigDecimal slalwbp;

    public BigDecimal sahlwbp;

    public BigDecimal slawbp;

    public BigDecimal sahwbp;

    public BigDecimal slakvarh;

    public BigDecimal sahkvarh;

    public BigDecimal sahkvamaks;

    public BigDecimal batch;

    public String msg;

    public LocalDateTime tglhitung;

    public String fillerFraksi;

    public BigDecimal fraksibeban;

    public BigDecimal haribeban;

    public BigDecimal haribebanBulan;

    public BigDecimal fraksikwh;

    public BigDecimal fraksiemin;

    public BigDecimal fraksihemat;

    public BigDecimal fraksidmp;

    public BigDecimal haripakai;

    public BigDecimal haripakaiBulan;

    public BigDecimal jamnyala350Batas;

    public String fillerReal;

    public BigDecimal kwhlwbpReal;

    public BigDecimal kwhwbpReal;

    public BigDecimal kwhblok3Real;

    public BigDecimal pemkwhReal;

    public BigDecimal kvarhpakaiReal;

    public BigDecimal kvarhlebih;

    public BigDecimal cosphiReal;

    public BigDecimal jamnyalaReal;

    public String fillerDivideReal;

    public BigDecimal kwhlwbpRegReal;

    public BigDecimal kwhwbpRegReal;

    public BigDecimal pemkwhRegReal;

    public BigDecimal jamnyalaRegReal;

    public BigDecimal kwhlwbpPremiumReal;

    public BigDecimal kwhwbpPremiumReal;

    public BigDecimal pemkwhPremiumReal;

    public BigDecimal kwhlwbpBtobReal;

    public BigDecimal kwhwbpBtobReal;

    public BigDecimal pemkwhBtobReal;

    public String fillerAfterEmin;

    public BigDecimal kwhlwbpReg;

    public BigDecimal kwhwbpReg;

    public BigDecimal kwhblok3Reg;

    public BigDecimal pemkwhReg;

    public BigDecimal kwhminEminReg;

    public BigDecimal eminReg;

    public BigDecimal rprekmin;

    public String jnsbatasEminReg;

    public BigDecimal kwhlwbpPremium;

    public BigDecimal kwhwbpPremium;

    public BigDecimal pemkwhPremium;

    public BigDecimal kwhminEminPremium;

    public BigDecimal eminPremium;

    public String jnsbatasEminPremium;

    public BigDecimal kwhlwbpBtob;

    public BigDecimal kwhwbpBtob;

    public BigDecimal pemkwhBtob;

    public BigDecimal kwhminEminBtob;

    public BigDecimal eminBtob;

    public String jnsbatasEminBtob;

    public String filler101a;

    public String statussubsidi;

    public BigDecimal rpsub;

    public BigDecimal rpnonsub;

    public BigDecimal kwhsub;

    public BigDecimal kwhnonsub;

    public BigDecimal btskwhsubsidi;

    public BigDecimal rpblok1Sub;

    public BigDecimal rpblok2Sub;

    public BigDecimal rpblok3Sub;

    public BigDecimal rpblok1Nonsub;

    public BigDecimal rpblok2Nonsub;

    public BigDecimal rpblok3Nonsub;

    public BigDecimal kwhblok1Sub;

    public BigDecimal kwhblok2Sub;

    public BigDecimal kwhblok3Sub;

    public BigDecimal kwhblok1Nonsub;

    public BigDecimal kwhblok2Nonsub;

    public BigDecimal kwhblok3Nonsub;

    public String jnsbatas101a;

    public BigDecimal btsblok101a;

    public BigDecimal bpakai101a;

    public String fillerDmp;

    public BigDecimal kwhwbpBatas;

    public BigDecimal dayamaxwbpBatas;

    public BigDecimal dayamaxSuplai;

    public BigDecimal dayamaxwbpSuplai;

    public String kdjnsmeter;

    public String filler0;

    public String suplaiDayamax;

    public String suplaiDayamaxwbp;

    public BigDecimal dayamaxReal;

    public BigDecimal dayamaxwbpReal;

    public BigDecimal cosphiDmp;

    public BigDecimal kwhpakaimaks;

    public BigDecimal kwhpakainormal;

    public String syaratpakai1;

    public String syaratpakai2;

    public String statuspakai;

    public String statusdmp;

    public BigDecimal kwhwbp1Reg;

    public BigDecimal kwhwbp2Reg;

    public BigDecimal dayawbpDis;

    public BigDecimal kwhwbpDasarIns;

    public BigDecimal kwhwbpTambahIns;

    public BigDecimal trfwbp1Reg;

    public BigDecimal trfwbp2Reg;

    public BigDecimal trfdayawbpDis;

    public BigDecimal trfwbpIns;

    public BigDecimal rpwbp1Reg;

    public BigDecimal rpwbp2Reg;

    public BigDecimal rpdayawbpDis;

    public BigDecimal rpinsDasar;

    public BigDecimal rpinsTambah;

    public BigDecimal rpinsDmp;

    public BigDecimal rpinsMax;

    public String fillerRp;

    public BigDecimal rplwbpReg;

    public BigDecimal rpwbpReg;

    public BigDecimal rpblok3Reg;

    public BigDecimal rpkwhReg;

    public BigDecimal rplwbpPremium;

    public BigDecimal rpwbpPremium;

    public BigDecimal rpkwhPremium;

    public BigDecimal rplwbpBtob;

    public BigDecimal rpwbpBtob;

    public BigDecimal rpkwhBtob;

    public BigDecimal rpkvarh;

    public BigDecimal rpbeban;

    public String fillerTrf;

    public BigDecimal trfbeban;

    public BigDecimal trfkvarh;

    public BigDecimal trflwbpReg;

    public BigDecimal trfwbpReg;

    public BigDecimal trfblok3;

    public BigDecimal trflwbpPremium;

    public BigDecimal trfwbpPremium;

    public BigDecimal trflwbpBtob;

    public BigDecimal trfwbpBtob;

    public String fillerTdl;

    public BigDecimal faktorkTdl;

    public BigDecimal faktorpTdl;

    public BigDecimal trflwbpTdl;

    public BigDecimal trfwbpTdl;

    public BigDecimal lwbp3;

    public BigDecimal wbp3;

    public BigDecimal blok33;

    public BigDecimal kwhpakai3;

    public BigDecimal bel;

    public BigDecimal rplwbp3;

    public BigDecimal rpwbp3;

    public BigDecimal rpblok33;

    public BigDecimal rpkwh3;

    public BigDecimal rphargaJual;

    public BigDecimal rpkvarhJual;

    public BigDecimal kwhlwbp1Ins;

    public BigDecimal kwhlwbp2Ins;

    public BigDecimal kwhwbpIns;

    public BigDecimal pemkwhIns;

    public BigDecimal rplwbp1Ins;

    public BigDecimal rplwbp2Ins;

    public BigDecimal rpwbpIns;

    public BigDecimal rpkwhIns;

    public Integer jmlpadam;

    public BigDecimal kwhwbpPadam;

    public BigDecimal sfc;

    public BigDecimal hsd;

    public BigDecimal rpBhBakar;

    public BigDecimal rpTambahBiaya;

    public BigDecimal pemkwhLwbp1Ins;

    public BigDecimal pemkwhLwbp1Normal;

    public BigDecimal fkmkwhLwbp1;

    public BigDecimal rpinsentifLwbp1;

    public String tipeskema;

    public Integer skema4;

    public BigDecimal prosenWbpIns;

    public BigDecimal prosenLwbp1Ins;

    public BigDecimal kwhWbppadamIns;

    public BigDecimal kwhlwbp1Insentif;

    public BigDecimal fkwbpIns;

    public BigDecimal gensetWbp;

    public BigDecimal trflwbp1Ins;

    public BigDecimal rpgensetKeLwbp1;

    public BigDecimal rplwbp1InsWbp;

    public BigDecimal kwhWbpPadamHari;

    public BigDecimal kwhWbpPadamBulan;

    public BigDecimal trflwbp1Normal;

    public BigDecimal wbprataIns;

    public BigDecimal lbwp1rataIns;

    public BigDecimal kwhlwbp1InsentifPremium;

    public BigDecimal rasioReguler;

    public BigDecimal rasioPremium;

    public BigDecimal pemkwhLwbp1InsPremium;

    public BigDecimal pemkwhLwbp1NormalPremium;

    public BigDecimal trflwbp1NormalPremium;

    public BigDecimal trflwbp1InsPremium;

    public BigDecimal rplwbp1InsPremium;

    public BigDecimal rplwbp1InsWbpPremium;

    public BigDecimal rplwbp2InsPremium;

    public BigDecimal rpwbpInsPremium;

    public BigDecimal rpinsentifLwbp1Premium;

    public BigDecimal rpkwhInsPremium;

    public BigDecimal trflwbpDasar;

    public BigDecimal trfwbpDasar;

    public BigDecimal trfkvarhDasar;
}
