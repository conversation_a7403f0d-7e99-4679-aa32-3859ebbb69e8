package com.iconplus.ap2t.generator.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @AUTHOR RR
 * @DATE 15/10/2024
 */

public class KoreksiBillingDTO{
    public String idpel;
    public String nama;
    public String alamat;
    public String unitup;
    public BigDecimal dlpd;
    public String bulanRekening;
    public String tahun;
    public String tarif;
    public Long daya;
    public String tarifLama;
    public BigDecimal dayaLama;
    public String frt;
    public String blthMutasi;
    public String jenisMK;
    public String tglBacaLalu;
    public String tglBacaAkhir;
    public LocalDate tglPerubahan;
    public BigDecimal lwbpLalu;
    public BigDecimal lwbpAkhir;
    public BigDecimal wbpLalu;
    public BigDecimal wbpAkhir;
    public BigDecimal kvarhLalu;
    public BigDecimal kvarhAkhir;
    public BigDecimal kvamaxAkhir;
    public BigDecimal kvawbpAkhir;
    public BigDecimal kwhLwbp;
    public BigDecimal kwhWpb;
    public BigDecimal kwhKvarh;
    public BigDecimal kwhKvamax;
    public BigDecimal kwhKvawbp;
    public BigDecimal jamNyala;
    public List<DataBillEnergiDTO>dataBillEnergiList;
    public List<UraianNilaiDTO> uraianNilaiList;


    public static class UraianNilaiDTO {
        public String uraian;
        public BigDecimal nilai;

        public UraianNilaiDTO(String uraian, BigDecimal nilai) {
            this.uraian = uraian;
            this.nilai = nilai;
        }
    }

    public static class DataBillEnergiDTO{
        public String id;
        public int pecahKe;
        public String jenisEnergi;
        public BigDecimal lwbpLalu;
        public BigDecimal lwbpAkhir;
        public BigDecimal wbpLalu;
        public BigDecimal wbpAkhir;
        public BigDecimal kvarhLalu;
        public BigDecimal kvarhAkhir;
        public BigDecimal kvamaxAkhir;
        public BigDecimal kvawbpAkhir;
        public LocalDate tglPerubahan;

        public DataBillEnergiDTO(String id, int pecahKe, String jenisEnergi, BigDecimal lwbpLalu, BigDecimal lwbpAkhir, BigDecimal wbpLalu, BigDecimal wbpAkhir, BigDecimal kvarhLalu, BigDecimal kvarhAkhir, BigDecimal kvamaxAkhir, BigDecimal kvawbpAkhir, LocalDate tglPerubahan) {
            this.id = id;
            this.pecahKe = pecahKe;
            this.jenisEnergi = jenisEnergi;
            this.lwbpLalu = lwbpLalu;
            this.lwbpAkhir = lwbpAkhir;
            this.wbpLalu = wbpLalu;
            this.wbpAkhir = wbpAkhir;
            this.kvarhLalu = kvarhLalu;
            this.kvarhAkhir = kvarhAkhir;
            this.kvamaxAkhir = kvamaxAkhir;
            this.kvawbpAkhir = kvawbpAkhir;
            this.tglPerubahan = tglPerubahan;
        }
    }
}
