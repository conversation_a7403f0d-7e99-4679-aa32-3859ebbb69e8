package com.iconplus.ap2t.generator.dto;

public class DLPDBillingDTO {
    private Integer dlpd;
    private String dlpdFkm;
    private String dlpdKvarh;
    private String dlpd3bln;
    private String dlpdJnsmutasi;
    private String dlpdTglbaca;

    public Integer getDlpd() {
        return dlpd;
    }

    public void setDlpd(Integer dlpd) {
        this.dlpd = dlpd;
    }

    public String getDlpdFkm() {
        return dlpdFkm;
    }

    public void setDlpdFkm(String dlpdFkm) {
        this.dlpdFkm = dlpdFkm;
    }

    public String getDlpdKvarh() {
        return dlpdKvarh;
    }

    public void setDlpdKvarh(String dlpdKvarh) {
        this.dlpdKvarh = dlpdKvarh;
    }

    public String getDlpd3bln() {
        return dlpd3bln;
    }

    public void setDlpd3bln(String dlpd3bln) {
        this.dlpd3bln = dlpd3bln;
    }

    public String getDlpdJnsmutasi() {
        return dlpdJnsmutasi;
    }

    public void setDlpdJnsmutasi(String dlpdJnsmutasi) {
        this.dlpdJnsmutasi = dlpdJnsmutasi;
    }

    public String getDlpdTglbaca() {
        return dlpdTglbaca;
    }

    public void setDlpdTglbaca(String dlpdTglbaca) {
        this.dlpdTglbaca = dlpdTglbaca;
    }

    @Override
    public String toString() {
        return "DLPDBillingDTO{" +
                "dlpd=" + dlpd +
                ", dlpdFkm='" + dlpdFkm + '\'' +
                ", dlpdKvarh='" + dlpdKvarh + '\'' +
                ", dlpd3bln='" + dlpd3bln + '\'' +
                ", dlpdJnsmutasi='" + dlpdJnsmutasi + '\'' +
                ", dlpdTglbaca='" + dlpdTglbaca + '\'' +
                '}';
    }
}
