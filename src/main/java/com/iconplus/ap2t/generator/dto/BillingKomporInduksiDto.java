package com.iconplus.ap2t.generator.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

import java.math.BigDecimal;

@RegisterForReflection
public class BillingKomporInduksiDto {
    public String thblrek;
    public String idpel;
    @JsonProperty("id_kompor")
    public String id_kompor;
    @JsonProperty("kwh_baca")
    public BigDecimal kwhBaca;
    @JsonProperty("kwh_subsidi")
    public BigDecimal kwhSubsidi;
    @JsonProperty("kwh_nonsubsidi")
    public BigDecimal kwhNonSubsidi;
    @JsonProperty("rp_subsidi")
    public BigDecimal rpSubsidi;
    @JsonProperty("rp_nonsubsidi")
    public BigDecimal rpNonSubsidi;
    @JsonProperty("rp_total")
    public BigDecimal rpTotal;
}
