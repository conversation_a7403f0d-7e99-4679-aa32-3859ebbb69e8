package com.iconplus.ap2t.generator.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

import java.math.BigDecimal;

@RegisterForReflection
public class BillingKomporPegawaiDto {
    @JsonProperty("kwh_subsidi")
    public BigDecimal kwhSubsidi;
    @JsonProperty("kwh_nonsubsidi")
    public BigDecimal kwhNonSubsidi;
    @JsonProperty("rp_subsidi")
    public BigDecimal rpSubsidi;
    @JsonProperty("rp_nonsubsidi")
    public BigDecimal rpNonSubsidi;
}


