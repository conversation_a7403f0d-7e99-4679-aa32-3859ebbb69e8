
package com.iconplus.ap2t.generator.dto;

import java.math.BigDecimal;

public class BillingDTO {
    public String id;
    public String idpel;
    public String nopel;
    public String nama;
    public String norek;
    public String unitup;
    public String unitupi;
    public String unitap;
    public String thblrek;
    public String frt;
    public String fjn;
    public String kdppj;
    public String pemda;
    public String tarif;
    public String kogol;
    public String kdinkaso;
    public String tgljttempo;
    public BigDecimal kwhlwbp;
    public BigDecimal kwhwbp;
    public BigDecimal rplwbp;
    public BigDecimal rpwbp;
    public BigDecimal rpblok3;
    public BigDecimal rpkvarh;
    public BigDecimal rpbeban;
    public BigDecimal rpptl;
    public BigDecimal rptb;
    public BigDecimal rpppn;
    public BigDecimal rpbpju;
    public BigDecimal rpbptrafo;
    public BigDecimal rpsewatrafo;
    public BigDecimal rpsewakap;
    public BigDecimal rpangsa;
    public BigDecimal rpangsb;
    public BigDecimal rpangsc;
    public BigDecimal rpmat;
    public BigDecimal rppln;
    public BigDecimal rptag;
    public BigDecimal rpproduksi;
    public BigDecimal rpsubsidi;
    public BigDecimal rptdllama;
    public BigDecimal rptdlbaru;
    public BigDecimal rpselisih;
    public BigDecimal rpreduksi;
    public BigDecimal rpbk1;
    public BigDecimal rpbk2;
    public BigDecimal rpbk3;
    public BigDecimal rprekBruto;
    public BigDecimal rprekNetto;
    public BigDecimal rptagBruto;
    public BigDecimal rptagNetto;
    public BigDecimal rpjbst;
    public BigDecimal rpkompensasi;
    public BigDecimal rpdiskonPremium;
    public BigDecimal rpdiskon;
    public BigDecimal rpinvoice;
    public BigDecimal rpptlplus;
    public BigDecimal rpptlminus;
    public BigDecimal rpppjptl;
    public BigDecimal rpppjangsa;
    public BigDecimal rpppjangsb;
    public BigDecimal rpppjangsc;
    public BigDecimal rpppnR3;
    public BigDecimal rpppnBptrafo;
    public BigDecimal rpppnSewatrafo;
    public BigDecimal rpppnOpspararel;
    public BigDecimal rpppnSewakap;
    public BigDecimal rpppnLain;
    public BigDecimal rptagMat;
    public BigDecimal rpppnRec;
    public BigDecimal rprec;
    public BigDecimal rpppnUap;
    public BigDecimal rpblok4;
    public BigDecimal rpopsel;
    public BigDecimal rpsewatrafoDil;
    public BigDecimal slalwbp;
    public BigDecimal sahlwbp;
    public BigDecimal slalwbpPasang;
    public BigDecimal sahlwbpCabut;
    public BigDecimal slalwbp1;
    public BigDecimal sahlwbp1;
    public BigDecimal slalwbp2;
    public BigDecimal sahlwbp2;
    public BigDecimal lwbp1rataIns;
    public BigDecimal sahlwbp1Cabut;
    public BigDecimal slalwbp1Pasang;
    public BigDecimal sahlwbp2Cabut;
    public BigDecimal slalwbp2Pasang;
    public BigDecimal pemkwhLwbp1;
    public BigDecimal pemkwhLwbp2;
    public BigDecimal sahlwbpExport;
    public BigDecimal slalwbpPasangExp;
    public BigDecimal sahlwbpCabutExp;
}