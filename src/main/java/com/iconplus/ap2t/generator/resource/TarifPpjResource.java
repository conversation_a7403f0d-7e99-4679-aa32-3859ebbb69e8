package com.iconplus.ap2t.generator.resource;

import com.iconplus.ap2t.common.param.billing.PpjParam;
import com.iconplus.ap2t.data.entity.billing.TarifPpj;
import com.iconplus.ap2t.data.repository.billing.PpjRepository;
import io.quarkus.panache.common.Sort;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/api/tarif_ppj")
@Tag(name = "Master Tarif PPJ",description = "API Master Tarif PPJ")
@ApplicationScoped
public class TarifPpjResource {
    Logger LOG = LoggerFactory.getLogger(TarifPpjResource.class.getName());
    @Inject
    PpjRepository ppjRepository;
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> getIndex(@QueryParam("page") Integer page){
        if(page==null){
            page = 0;
        }
        return TarifPpj.findAll(Sort.by("tarif"))
                .page(page,100).list()
                .onItem().transform(items->Response.ok(items).build());
    }
    @Path("/find")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<Response> findTarifPPj(PpjParam param){
        LOG.info("find tarif ppj");
//        try {
            return ppjRepository.getProsenPPJ(param.tarif,param.pemda,param.upi,param.daya).map(result->{
                return Response.ok(result).status(200).build();
            }).onFailure().recoverWithItem(Response.ok(0.0d).status(200).build());
//        } catch (Exception ex){
//            return Uni.createFrom().item(Response.ok(0.0d).build());
//        }
//       return ppjService.getTarifSingle(param.tarif,param.pemda,param.upi,param.daya).onItem()
//                .ifNotNull().transform(item->Response.ok(item).build())
//                .onFailure().recoverWithItem(Response.ok(0.0d).build());
    }
    @Path("/single")
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<Response> findTarifPPjSingleRow(PpjParam param){
        return ppjRepository.getTarifSingle(param.tarif,param.pemda,param.upi,param.daya).onItem()
                .ifNotNull().transform(item->Response.ok(item).build())
                .onFailure().recoverWithItem(Response.ok(0.0d).build());
    }
    @Path("/upi/{kode}")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> findTarifPPjUpi(@PathParam("kode") String kode){
        return TarifPpj.find("unitUpi=?1",
                        kode)
                .list().onItem()
                .transform(result->Response.ok(result).build())
                .onItem().ifNull().continueWith(Response.ok(0.0d).build());
    }
    @Path("/pemda/{kode}/{pemda}")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> findTarifPPjPemda(@PathParam("kode") String kode,
                                      @PathParam("pemda") String pemda){
        return TarifPpj.find("unitUpi=?1 and pemda=?2",
                        kode,pemda)
                .list().onItem()
                .transform(result->Response.ok(result).build())
                .onItem().ifNull().continueWith(Response.ok(0.0d).build());
    }
    @Path("/tarif/{kode}")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> findTarifBasedOnKode(@PathParam("kode") String kode){
        return TarifPpj.find("tarif=?1",
                        kode)
                .list().onItem()
                .transform(result->Response.ok(result).build())
                .onItem().ifNull().continueWith(Response.ok(0.0d).build());
    }
}
