package com.iconplus.ap2t.generator.resource;


import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.data.entity.master.MasterDlpd;
import com.iconplus.ap2t.generator.service.MasterDpldService;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import java.util.List;

@Path("/api/dlpd")
@Tag(name = "Master DLPD",description = "API Master DLPD")
@ApplicationScoped
public class DlpdResource {

    @Inject
    MasterDpldService service;

    @GET
    @Path("/")
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<List<MasterDlpd>>> getAll(){
        return service.all();
    }
}
