package com.iconplus.ap2t.generator.resource;

import com.iconplus.ap2t.common.param.billing.DpmPecahanParam;
import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.generator.service.DpmPecahanService;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

@Path("/api/dpm-pecahan")
@Tag(name = "Dpm Pecahan", description = "API Dpm Pecahan")
@ApplicationScoped
public class DpmPecahanResource {

    @Inject
    DpmPecahanService service;

    @POST
    @Path("/save")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> add(DpmPecahanParam req) {
        return APIResponse.ok(service.save(req));
    }
}