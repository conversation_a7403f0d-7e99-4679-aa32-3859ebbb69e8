package com.iconplus.ap2t.generator.resource;

import com.iconplus.ap2t.common.param.billing.TarifParam;
import com.iconplus.ap2t.data.entity.master.MasterTarif;
import com.iconplus.ap2t.data.repository.billing.FjnRepository;
import com.iconplus.ap2t.data.repository.billing.FrtRepository;
import com.iconplus.ap2t.data.repository.master.MasterTarifRepository;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.jboss.logging.Logger;

import java.util.List;

import static jakarta.ws.rs.core.Response.Status.NOT_FOUND;

@Path("/api/tarif")
@Tag(name = "Master Tarif Dasar",description = "API Master Tarif Dasar")
@ApplicationScoped
public class TarifDasarResource {
    @Inject
    FjnRepository fjnRepository;
    @Inject
    FrtRepository frtRepository;
    @Inject
    MasterTarifRepository masterTarifRepository;
    final Logger LOGGER = Logger.getLogger(TarifDasarResource.class.getName());
    @GET
    @Path("/")
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<List<MasterTarif>> getAll(){
        return MasterTarif.listAll();
    }
    @GET
    @Path("/{kode}")
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<List<MasterTarif>> getByKode(@PathParam("kode") String kode){
        return masterTarifRepository.findByKode(kode);
    }
    @POST
    @Path("/single")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    public Uni<Response> findTarifDasar(@FormParam("kode") String kode,
                                        @FormParam("kdpt") String kdpt,
                                        @FormParam("daya") Long daya){
//        LOGGER.info(String.format("kode : %s, kdpt: %s, Daya : %d",kode,kdpt,daya));
        Uni<MasterTarif> tarif = masterTarifRepository.findByKodedaya(kode,kdpt,daya);
//        MasterTarif result = optional.orElseThrow(() -> new NotFoundException());
        return tarif.onItem().ifNotNull().transform(entity->Response.ok(entity).build())
                .onItem().ifNull().continueWith(Response.ok().status(NOT_FOUND)::build);
//        return MasterTarif.findTarif(kode,kdpt,daya).stream().toList();
    }
    @POST
    @Path("/master")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<Response> findMasterTarif(TarifParam param){
//        LOGGER.info(String.format("kode : %s, kdpt: %s, Daya : %d",kode,kdpt,daya));
        Uni<MasterTarif> tarif = masterTarifRepository.findByKodedaya(param.kode,param.kdpt,param.daya);
//        MasterTarif result = optional.orElseThrow(() -> new NotFoundException());
        return tarif.onItem().ifNotNull().transform(entity->Response.ok(entity).build())
                .onItem().ifNull().continueWith(Response.ok().status(NOT_FOUND)::build);
//        return MasterTarif.findTarif(kode,kdpt,daya).stream().toList();
    }
    @GET
    @Path("/fjn/{kode}")
    @Produces(MediaType.TEXT_PLAIN)
    public Uni<Double> getFJN(@PathParam("kode") String kode){
        return fjnRepository.getFjnValue(kode);
    }
    @GET
    @Path("/frt/{kode}")
    @Produces(MediaType.TEXT_PLAIN)
    public Uni<Double> getFRT(@PathParam("kode") String kode){
        return frtRepository.getFrtValue(kode);
    }
}
