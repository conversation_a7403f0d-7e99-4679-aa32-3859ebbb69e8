package com.iconplus.ap2t.generator.resource;

import com.iconplus.ap2t.common.param.billing.MeterParam;
import com.iconplus.ap2t.common.param.billing.SuplaiDilParam;
import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.generator.service.FsmService;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.util.List;

@ApplicationScoped
@Path("/fsm")
public class FsmResource {

    @Inject
    FsmService fsmService;

    @POST
    @Path("/store-lama")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @WithTransaction
    public Uni<Response> postFSMData(MeterParam params) {
        return fsmService.storeDPM(params).onItem().transform(result ->
            Response.ok().entity(result).build()
        );
    }

    @POST
    @Path("/stan-tanpa-meter")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @WithTransaction
    public Uni<APIResponse<?>> postFSMData(List<SuplaiDilParam> params) {
        return fsmService.saveStanTanpaMeter(params).onItem().transform(APIResponse::ok);
    }

    @POST
    @Path("/store-dpm")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @WithTransaction
    public Uni<Response> postFSMDataDpm(MeterParam params) {
        return fsmService.storeDataDPM(params).onItem().transform(result ->
            Response.ok().entity(result).build()
        );
    }

    @POST
    @Path("/store-general")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @WithTransaction
    public Uni<Response> postFSMDataDpmGeneral(MeterParam params) {
        return fsmService.storeDataDPMGeneral(params).onItem().transform(result ->
            Response.ok().entity(result).build()
        );
    }
}
