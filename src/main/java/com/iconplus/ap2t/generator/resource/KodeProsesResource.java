package com.iconplus.ap2t.generator.resource;

import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.data.entity.master.MasterKodeProses;
import com.iconplus.ap2t.generator.service.MasterKdProsesService;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import java.util.List;

@Path("/api/kd-proses")
@Tag(name = "Master Kode Proses",description = "API Master Kode Proses")
@ApplicationScoped
public class KodeProsesResource {

    @Inject
    MasterKdProsesService service;

    @GET
    @Path("/kdklp")
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<List<MasterKodeProses>>> getKdKlpByUnitupAndThbl(
        @QueryParam("unitup") String unitup, @QueryParam("thbl") String thbl
    ) {
        return service.getKdKlpByUnitupAndBlth(unitup, thbl);
    }
}
