package com.iconplus.ap2t.generator.resource;

import com.iconplus.ap2t.common.param.billing.EntryStandParam;
import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.data.entity.billing.Dpm;
import com.iconplus.ap2t.data.repository.billing.DpmRepository;
import com.iconplus.ap2t.generator.service.EntryStandService;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.hibernate.reactive.mutiny.Mutiny;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ApplicationScoped
@Path("/entry-stand")
public class EntryStandResource {
    Logger LOG = LoggerFactory.getLogger(EntryStandResource.class.getName());
    @Inject
    DpmRepository repository;

    @Inject
    Mutiny.SessionFactory sf;

    @Inject
    EntryStandService service;

    @GET
    @Path("/")
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<Dpm>> getDataEntryStand(@QueryParam("idpel") String idpel, @QueryParam("thblrek") String thblrek){
        return service.findDataEntryStand(idpel,thblrek);
    }
    
    @POST
    @Path("/store")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @WithTransaction
    public Uni<Response> Entry(EntryStandParam params){
        return service.storeStand(params).onItem().transform(result ->
                Response.ok().entity(result).build()
        );
    }

}
