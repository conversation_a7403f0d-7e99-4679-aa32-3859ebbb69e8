package com.iconplus.ap2t.generator.resource;

import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.generator.dto.KoreksiBillingDTO;
import com.iconplus.ap2t.generator.dto.KoreksiBillingPltsDTO;
import com.iconplus.ap2t.generator.param.KoreksiBillingParam;
import com.iconplus.ap2t.generator.param.KoreksiBillingPltsParam;
import com.iconplus.ap2t.generator.service.BillingService;
import com.iconplus.ap2t.generator.service.BillingServiceOptimize;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

/**
 * @AUTHOR RR
 * @DATE 14/10/2024
 */

@Path("/api/")
@Tag(name = "Koreksi Billing",description = "Koreksi Data Billing")
@ApplicationScoped
public class KoreksiBillingResource {

    @Inject
    private BillingService service;

    @Inject
    private BillingServiceOptimize serviceBilling;

    @GET
    @Path("/data-koreksi-billing")
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<KoreksiBillingDTO>> getDataKoreksiBilling(@QueryParam("idpel") String idpel, @QueryParam("thblrek") String thblrek){
        return serviceBilling.findDataKoreksiBill(idpel,thblrek);
    }
    @GET
    @Path("/data-koreksi-billing-stand-plts")
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<KoreksiBillingPltsDTO>> getDataKoreksiBillingStandPlts(@QueryParam("idpel") String idpel, @QueryParam("thblrek") String thblrek){
        return serviceBilling.findDataKoreksiBillPlts(idpel,thblrek);
    }
    @GET
    @Path("/data-koreksi-billing-720")
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<KoreksiBillingDTO>> getDataKoreksiBilling720(@QueryParam("idpel") String idpel, @QueryParam("thblrek") String thblrek){
        return serviceBilling.findDataKoreksiBill720(idpel,thblrek);
    }

    @POST
    @Path("/koreksi-billing/stand-meter")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> correctBilling(KoreksiBillingParam param) {
        return serviceBilling.saveKoreksiBilling(param);
    }
    
    @POST
    @Path("/koreksi-billing/stand-plts")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> correctBillingPlts(KoreksiBillingPltsParam param) {
        return serviceBilling.saveKoreksiBillingPlts(param);
    }
    
    @POST
    @Path("/koreksi-billing/720")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> correctBilling720(KoreksiBillingParam param) {
        return serviceBilling.saveKoreksiBilling720(param);
    }
}
