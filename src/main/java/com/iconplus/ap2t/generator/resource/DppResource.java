package com.iconplus.ap2t.generator.resource;

import com.iconplus.ap2t.common.param.billing.FlagDppParam;
import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.generator.service.FlagDppService;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/api/")
@Tag(name = "DPP Resource",description = "API Billing")
@ApplicationScoped
public class DppResource {

    public final static Logger log = LoggerFactory.getLogger(DppResource.class);

    @Inject
    FlagDppService flagDppService;

    @POST
    @Path("/closing-billing")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> flagDpp(FlagDppParam request){
        log.info("Flagging The Dpp");
        return APIResponse.ok(flagDppService.execute(request));
    }
}
