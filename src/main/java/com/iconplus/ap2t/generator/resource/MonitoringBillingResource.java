package com.iconplus.ap2t.generator.resource;

import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.generator.service.MonitoringBillingService;
import io.quarkus.panache.common.Page;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import java.util.Optional;

@Path("/api/monitoring/billing")
@Tag(name = "MonitoringBilling", description = "API Monitoring Billing")
@ApplicationScoped
public class MonitoringBillingResource {

    @Inject
    MonitoringBillingService service;

    @GET
    @Path("/monitoring-billing-rekap")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> getMonitoringBillingRekap(
            @QueryParam("unitupi") String unitupi,
            @QueryParam("unitap") String unitap,
            @QueryParam("unitup") String unitup,
            @QueryParam("thblrek") String thblrek,
            @QueryParam("page") Integer page, @QueryParam("size") Integer size
    ) {
        return APIResponse.ok(service.getMonitoringBillingRekap(
                unitupi, unitap, unitup, thblrek, Page.of(
                        Optional.of(page).orElse(1),
                        Optional.of(size).orElse(100)
                )
        ));
    }

    @GET
    @Path("/monitoring-billing-detil")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> getMonitoringBillingDetil(
            @QueryParam("unitupi") String unitupi,
            @QueryParam("unitap") String unitap,
            @QueryParam("unitup") String unitup,
            @QueryParam("thblrek") String thblrek,
            @QueryParam("page") Integer page, @QueryParam("size") Integer size
    ) {
        return APIResponse.ok(service.getMonitoringBillingDetil(
                unitupi, unitap, unitup, thblrek, Page.of(
                        Optional.of(page).orElse(1),
                        Optional.of(size).orElse(100)
                )
        ));
    }

    @GET
    @Path("/monitoring-billing-dlpd-rekap")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> getMonitoringBillingRekap(
            @QueryParam("unitupi") String unitupi,
            @QueryParam("unitap") String unitap,
            @QueryParam("unitup") String unitup,
            @QueryParam("thblrek") String thblrek,
            @QueryParam("kdProsesKlp") String kdProsesKlp,
            @QueryParam("dlpd") String dlpd,
            @QueryParam("page") Integer page, @QueryParam("size") Integer size
    ) {
        return APIResponse.ok(service.getMonitoringBillingDlpdRekap(
                unitupi, unitap, unitup, kdProsesKlp, thblrek, dlpd, Page.of(
                        Optional.of(page).orElse(1),
                        Optional.of(size).orElse(100)
                )
        ));
    }

    @GET
    @Path("/monitoring-billing-dlpd-detil")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> getMonitoringBillingDetil(
            @QueryParam("unitupi") String unitupi,
            @QueryParam("unitap") String unitap,
            @QueryParam("unitup") String unitup,
            @QueryParam("thblrek") String thblrek,
            @QueryParam("kdProsesKlp") String kdProsesKlp,
            @QueryParam("dlpd") String dlpd,
            @QueryParam("page") Integer page, @QueryParam("size") Integer size
    ) {
        return APIResponse.ok(service.getMonitoringBillingDlpdDetil(
                unitupi, unitap, unitup, kdProsesKlp, thblrek, dlpd, Page.of(
                        Optional.of(page).orElse(1),
                        Optional.of(size).orElse(100)
                )
        ));
    }

    @GET
    @Path("/monitoring-dlpd-rekap-export")
    @Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public Response downloadMonitoringDlpdRekapExcel(
            @QueryParam("unitupi") String unitupi,
            @QueryParam("unitap") String unitap,
            @QueryParam("unitup") String unitup,
            @QueryParam("kdProsesKlp") String kdProsesKlp,
            @QueryParam("thblrek") String thblrek,
            @QueryParam("dlpd") String dlpd,
            @QueryParam("page") int page,
            @QueryParam("size") int size) {
        Page paging = Page.of(page, size);
        byte[] excelBytes = service.generateMonitoringDlpdRekapExcel(
                unitupi, unitap, unitup, kdProsesKlp, thblrek, dlpd, paging);

        String fileName = String.format("Monitoring_Dlpd_Rekap_%s.xlsx", thblrek);
        return Response.ok(excelBytes)
                .header("Content-Disposition", "attachment; filename=\"" + fileName + "\"")
                .build();
    }

    @GET
    @Path("/monitoring-dlpd-detail-export")
    @Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public Response downloadMonitoringDlpdDetailExcel(
            @QueryParam("unitupi") String unitupi,
            @QueryParam("unitap") String unitap,
            @QueryParam("unitup") String unitup,
            @QueryParam("kdProsesKlp") String kdProsesKlp,
            @QueryParam("thblrek") String thblrek,
            @QueryParam("dlpd") String dlpd,
            @QueryParam("page") int page,
            @QueryParam("size") int size) {
        Page paging = Page.of(page, size);
        byte[] excelBytes = service.generateMonitoringDlpdDetailExcel(
                unitupi, unitap, unitup, kdProsesKlp, thblrek, dlpd, paging);

        String fileName = String.format("Monitoring_Dlpd_Detail_%s.xlsx", thblrek);
        return Response.ok(excelBytes)
                .header("Content-Disposition", "attachment; filename=\"" + fileName + "\"")
                .build();
    }
    @GET
    @Path("/monitoring-billing-rekap-export")
    @Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public Response downloadMonitoringBillingRekapExcel(
            @QueryParam("unitupi") String unitupi,
            @QueryParam("unitap") String unitap,
            @QueryParam("unitup") String unitup,
            @QueryParam("thblrek") String thblrek,
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("1000") int size) {
        Page paging = Page.of(page, size);
        byte[] excelBytes = service.generateMonitoringBillingRekapExcel(
                unitupi, unitap, unitup, thblrek, paging);

        String fileName = String.format("Monitoring_Billing_Rekap_%s.xlsx", thblrek);
        return Response.ok(excelBytes)
                .header("Content-Disposition", "attachment; filename=\"" + fileName + "\"")
                .build();
    }
    @GET
    @Path("/monitoring-billing-detail-export")
    @Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public Response downloadMonitoringBillingDetailExcel(
            @QueryParam("unitupi") String unitupi,
            @QueryParam("unitap") String unitap,
            @QueryParam("unitup") String unitup,
            @QueryParam("thblrek") String thblrek,
            @QueryParam("page") int page,
            @QueryParam("size") int size) {
        Page paging = Page.of(page, size);
        byte[] excelBytes = service.generateMonitoringBillingDetailExcel(
                unitupi, unitap, unitup, thblrek, paging);

        String fileName = String.format("Monitoring_Billing_Detail_%s.xlsx", thblrek);
        return Response.ok(excelBytes)
                .header("Content-Disposition", "attachment; filename=\"" + fileName + "\"")
                .build();
    }

}
