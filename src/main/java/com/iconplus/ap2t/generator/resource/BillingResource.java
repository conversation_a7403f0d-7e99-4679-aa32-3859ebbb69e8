package com.iconplus.ap2t.generator.resource;

import com.iconplus.ap2t.common.param.billing.*;
import com.iconplus.ap2t.common.param.master.UnitParam;
import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.generator.service.BillingService;
import com.iconplus.ap2t.generator.service.BillingServiceOptimize;
import io.quarkus.panache.common.Page;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.parameters.RequestBody;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;

@Path("/api/")
@Tag(name = "Billing", description = "API Billing")
@ApplicationScoped
public class BillingResource {

    final static Logger LOGGER = LoggerFactory.getLogger(BillingResource.class);

    @Inject
    BillingService service;

    @Inject
    BillingServiceOptimize optimize;

    @GET
    @Path("/baca-billing")
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> getDataBilling(@QueryParam("idpel") String idpel, @QueryParam("thblrek") String thblrek) {
        LOGGER.info("Cari data billing - IDPEL : {}", idpel);
        return service.findDataBill(idpel, thblrek);
    }

    @POST
    @Path("/save-billing")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<Response> saveBilling(OutputBillingParam req) {
        LOGGER.info("Save Billing Proses");
        return optimize.saveBilling(req).onItem().transform(apiResponse -> Response.ok(apiResponse).build());
    }

    @GET
    @Path("/verifikasi-dlpd")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> getVerifikasiMonitoringDlpd(
        @QueryParam("unitupi") String unitupi, @QueryParam("unitap") String unitap, @QueryParam("unitup") String unitup,
        @QueryParam("thbl") String thbl, @QueryParam("dlpd") String dlpd, @QueryParam("kdProsesKlp") String kdProsesKlp,
        @QueryParam("page") Integer page, @QueryParam("size") Integer size
    ) {
        MonitoringDlpdParam request = new MonitoringDlpdParam(
            new UnitParam(unitupi, unitap, unitup), thbl, dlpd, kdProsesKlp
        );
        return APIResponse.ok(service.getDataVerifikasiDlpd(
            request, Page.of(
                Optional.of(page).orElse(1),
                Optional.of(size).orElse(100)
            )
        ));
    }
    @GET
    @Path("/verifikasi-dlpd/export")
    @Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public Uni<Response> exportExcelVerifDlpd(
            @QueryParam("unitupi") String unitupi,
            @QueryParam("unitap") String unitap,
            @QueryParam("unitup") String unitup,
            @QueryParam("thbl") String thbl,
            @QueryParam("dlpd") String dlpd,
            @QueryParam("kdProsesKlp") String kdProsesKlp,
            @QueryParam("page") Integer page,
            @QueryParam("size") Integer size
    ) {
        MonitoringDlpdParam request = new MonitoringDlpdParam(
                new UnitParam(unitupi, unitap, unitup), thbl, dlpd, kdProsesKlp
        );

        Page paging = Page.of(
                Optional.ofNullable(page).orElse(1),
                Optional.ofNullable(size).orElse(100)
        );

        String fileName = String.format("verifikasi-dlpd-pelanggan_%s.xlsx", thbl);

        return service.generateVerifDlpdExcel(request, paging)
                .onItem().transform(data -> Response.ok(data)
                .header("Content-Disposition", "attachment; filename=\"" + fileName + "\"")
                .build());
    }
    @GET
    @Path("/verifikasi-dlpd-kolektif/export")
    @Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public Uni<Response> exportExcelVerifDlpdKolektif(
            @QueryParam("unitupi") String unitupi,
            @QueryParam("unitap") String unitap,
            @QueryParam("unitup") String unitup,
            @QueryParam("thbl") String thbl,
            @QueryParam("dlpd") String dlpd,
            @QueryParam("kdProsesKlp") String kdProsesKlp,
            @QueryParam("page") Integer page,
            @QueryParam("size") Integer size
    ) {
        MonitoringDlpdParam request = new MonitoringDlpdParam(
                new UnitParam(unitupi, unitap, unitup), thbl, dlpd, kdProsesKlp
        );

        Page paging = Page.of(
                Optional.ofNullable(page).orElse(1),
                Optional.ofNullable(size).orElse(100)
        );

        String fileName = String.format("verifikasi-dlpd-kolektif_%s.xlsx", thbl);

        return service.generateVerifDlpdKolektifExcel(request, paging)
                .onItem().transform(data -> Response.ok(data)
                .header("Content-Disposition", "attachment; filename=\"" + fileName + "\"")
                .build());
    }
    
    

    @POST
    @Path("/verifikasi-dlpd")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> setVerifikasiMonitoringDlpd(@RequestBody VerifikasiDlpdParam request) {
        return APIResponse.ok(service.setVerifikasiDldp(request));
    }

    @POST
    @Path("/save-billing-mutasi")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> saveBillingMutasi(OutputBillingMutasiParam req) {
        return APIResponse.ok(optimize.saveBillingMutasi(req));
    }

    @GET
    @Path("/monitoring-dpm-detil")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> getMonDPMDetil(
            @QueryParam("unitupi") String unitupi,
            @QueryParam("unitap") String unitap,
            @QueryParam("unitup") String unitup,
            @QueryParam("thblrek") String thblrek,
            @QueryParam("page") Integer page, @QueryParam("size") Integer size
    ) {
        return APIResponse.ok(service.getMonitoringDPMDetil(
                unitup, thblrek, Page.of(
                        Optional.of(page).orElse(1),
                        Optional.of(size).orElse(100)
                )
        ));
    }

    @GET
    @Path("/monitoring-dpm-rekap")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> getMonDPMRekap(
            @QueryParam("unitupi") String unitupi,
            @QueryParam("unitap") String unitap,
            @QueryParam("unitup") String unitup,
            @QueryParam("thblrek") String thblrek,
            @QueryParam("page") Integer page, @QueryParam("size") Integer size
    ) {
        return APIResponse.ok(service.getMonitoringDPMRekap(
                unitupi, unitap, unitup, thblrek, Page.of(
                        Optional.of(page).orElse(1),
                        Optional.of(size).orElse(100)
                )
        ));
    }

    @GET
    @Path("/monitoring-dpm-rekap/export")
    @Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public Response exportExcelMonitoringDPMRekap(
            @QueryParam("unitupi") String unitupi,
            @QueryParam("unitap") String unitap,
            @QueryParam("unitup") String unitup,
            @QueryParam("thblrek") String thblrek,
            @QueryParam("page") int page,
            @QueryParam("size") int size
    ) {
        Page paging = Page.of(page, size);
        byte[] excel = service.generateMonitoringDPMRekapExcel(unitupi, unitap, unitup, thblrek, paging);
        String fileName = String.format("monitoring-dpm-monitoring-dpm-rekap_%s.xlsx", thblrek);
        return Response.ok(excel)
        .header("Content-Disposition", "attachment; filename=\"" + fileName + "\"")
                .build();
    }
    @GET
    @Path("/monitoring-dpm-detail/export")
    @Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public Response exportExcelMonitoringDPMDetail(
            @QueryParam("unitupi") String unitupi,
            @QueryParam("unitap") String unitap,
            @QueryParam("unitup") String unitup,
            @QueryParam("thblrek") String thblrek,
            @QueryParam("page") int page,
            @QueryParam("size") int size
    ) {
        Page paging = Page.of(page, size);
        byte[] excel = service.generateMonitoringDPMDetailExcel(unitupi, unitap, unitup, thblrek,paging);
        String fileName = String.format("monitoring-dpm-detail_%s.xlsx", thblrek);
        return Response.ok(excel)
        .header("Content-Disposition", "attachment; filename=\"" + fileName + "\"")
                .build();
    }
      



    @GET
    @Path("/monitoring-dpm-rekap2")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> getMonDPMRekap2(
            @QueryParam("unitupi") String unitupi,
            @QueryParam("unitap") String unitap,
            @QueryParam("unitup") String unitup,
            @QueryParam("thblrek") String thblrek,
            @QueryParam("page") Integer page, @QueryParam("size") Integer size
    ) {
        return APIResponse.ok(service.getMonitoringDPMRekap2(
                unitupi, unitap, unitup, thblrek, Page.of(
                        Optional.of(page).orElse(1),
                        Optional.of(size).orElse(100)
                )
        ));
    }

    @GET
    @Path("/verifikasi-dlpd-kolektif")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> getDataVerifikasiDlpdKolektif(
            @QueryParam("unitupi") String unitupi, @QueryParam("unitap") String unitap, @QueryParam("unitup") String unitup,
            @QueryParam("thbl") String thbl, @QueryParam("dlpd") String dlpd, @QueryParam("kdProsesKlp") String kdProsesKlp,
            @QueryParam("page") Integer page, @QueryParam("size") Integer size
    ) {
        MonitoringDlpdParam request = new MonitoringDlpdParam(
                new UnitParam(unitupi, unitap, unitup), thbl, dlpd, kdProsesKlp
        );
        return APIResponse.ok(service.getDataVerifikasiDlpdKolektif(
                request, Page.of(
                        Optional.of(page).orElse(1),
                        Optional.of(size).orElse(100)
                )
        ));
    }

    @POST
    @Path("/verifikasi-dlpd-kolektif")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> setVerifikasiMonitoringDlpd(@RequestBody VerifDlpdParam request) {
        return APIResponse.ok(service.setVerifikasiDldp(request));
    }
}