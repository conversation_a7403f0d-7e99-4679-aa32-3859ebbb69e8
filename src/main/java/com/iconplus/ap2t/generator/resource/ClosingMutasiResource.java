package com.iconplus.ap2t.generator.resource;

import com.iconplus.ap2t.common.param.billing.ClosingMutasiParam;
import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.generator.service.ClosingMutasiService;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

/**
 * <AUTHOR>
 * Date: 20/06/2025
 * Time: 11:03
 */
@Path("/api/")
@Tag(name = "Closing Mutasi",description = "Closing Mutasi Billing")
@ApplicationScoped
public class ClosingMutasiResource {
    @Inject
    private ClosingMutasiService service;

    @POST
    @Path("/closing-billing/mutasi")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> closingMutasi(ClosingMutasiParam param){
        return service.closingMutasi(param);
    }
}
