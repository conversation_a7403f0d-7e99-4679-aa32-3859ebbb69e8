package com.iconplus.ap2t.generator.resource;

import com.iconplus.ap2t.common.param.billing.KomponenParam;
import com.iconplus.ap2t.data.entity.billing.KomponenBiaya;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.RestResponse;

import java.util.List;

import static jakarta.ws.rs.core.Response.Status.CREATED;
import static jakarta.ws.rs.core.Response.Status.NOT_FOUND;

@Path("/api/komponen_biaya")
@Tag(name = "Komponen Biaya",description = "Komponen Biaya billing")
@ApplicationScoped
public class KomponenBiayaResource {
    private static final Logger LOGGER = Logger.getLogger(KomponenBiayaResource.class.getName());
    @GET
    @Path("/")
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<List<KomponenBiaya>> getAll(){
        return KomponenBiaya.listAll();
    }
    @GET
    @Path("/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<KomponenBiaya> getSingle(Long id) {
        return KomponenBiaya.findById(id);
    }
    @POST
    @Path("/")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public RestResponse<KomponenBiaya> create(KomponenBiaya komponenBiaya){
        if (komponenBiaya == null || komponenBiaya.name != null) {
            throw new WebApplicationException("Id was invalidly set on request.", 422);
        }
        return RestResponse.status(CREATED, komponenBiaya);
    }
    @PUT
    @Path("/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<Response> updateKomponen(@PathParam("id") Long id,
                                        KomponenParam komponenBiaya){
        LOGGER.info("update id : "+id);
        if (komponenBiaya == null || komponenBiaya.name == null) {
            throw new WebApplicationException("Id was invalidly set on request.", 422);
        }
        return Panache
                .withTransaction(() -> KomponenBiaya.<KomponenBiaya> findById(id)
                        .onItem().ifNotNull().invoke(entity -> {

                            entity.formulaId = komponenBiaya.formulaId;
                            entity.name = komponenBiaya.name;
                            entity.variable = komponenBiaya.variable;
                            entity.tableName = komponenBiaya.tableName;
                            entity.columnName = komponenBiaya.columnName;
                            entity.noUrut = komponenBiaya.noUrut;
                            entity.enabled = komponenBiaya.enabled;
                        })
                )
                .onItem().ifNotNull().transform(entity -> Response.ok(entity).build())
                .onItem().ifNull().continueWith(Response.ok().status(NOT_FOUND)::build);
    }
}
