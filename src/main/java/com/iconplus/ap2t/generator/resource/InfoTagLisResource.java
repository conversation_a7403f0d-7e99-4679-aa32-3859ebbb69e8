package com.iconplus.ap2t.generator.resource;

import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.generator.service.InfoTagLisService;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

/**
 * <AUTHOR>
 */

@Path("/api/info-taglis")
@Tag(name = "Info Tagihan Listrik", description = "API Info Tagihan Listrik")
@ApplicationScoped
public class InfoTagLisResource {

    @Inject
    private InfoTagLisService infoTagLisService;

    @GET
    @Path("/download")
    @Tag(name = "Info Tagihan Listrik", description = "Get Data Info Tagihan Listrik For Download Invoice")
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> getInfoTaglis(
            @QueryParam("idpel") String idpel,
            @QueryParam("thblrek") String thblrek
    ) {
        return infoTagLisService.getInfoTaglis(idpel, thblrek);
    }

    @GET
    @Path("/get-data-taglis")
    @Tag(name = "Info Tagihan Listrik", description = "Get Data Tagihan Listrik Invoice")
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> getDataTaglis(
        @QueryParam("idpel") String idpel,
        @QueryParam("thblrek") String thblrek
    ) {
        return infoTagLisService.getDataBillTaglis(idpel, thblrek);
    }
}
