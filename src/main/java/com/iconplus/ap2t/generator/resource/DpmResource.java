package com.iconplus.ap2t.generator.resource;

import com.iconplus.ap2t.common.param.billing.CrmDpmParam;
import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.generator.service.DpmService;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

@Path("/api/dpm")
@Tag(name = "DPM", description = "API DPM")
@ApplicationScoped
public class DpmResource {

    @Inject
    DpmService service;

    @POST
    @Path("/save-mutasi-pdl")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse<?>> add(CrmDpmParam req){
        return APIResponse.ok(service.saveDpmFromCrm(req));
    }
}
