package com.iconplus.ap2t.generator.resource;


import com.iconplus.ap2t.common.param.billing.BacaUlangParam;
import com.iconplus.ap2t.common.response.APIResponse;
import com.iconplus.ap2t.generator.service.AcmtProxyService;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

/**
 * @AUTHOR RR
 * @DATE 17/10/2024
 */
@Path("/api/")
@Tag(name = "acmt proxy",description = "Baca Ulang ACMT")
@ApplicationScoped
public class AcmtProxyResource {
    @Inject
    private AcmtProxyService service;

    @POST
    @Path("acmt-proxy/baca-ulang-meter")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<APIResponse> bacaUlangMeterProxy(BacaUlangParam param){
         return service.acmtBacaMeter(param);
    }
}
