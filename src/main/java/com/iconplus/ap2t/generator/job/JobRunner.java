package com.iconplus.ap2t.generator.job;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iconplus.ap2t.common.param.CustomerParam;
import com.iconplus.ap2t.data.entity.billing.InitBilling;
import com.iconplus.ap2t.data.repository.billing.BillingRepository;
import com.iconplus.ap2t.data.repository.billing.InitRepository;
import com.iconplus.ap2t.generator.service.BillingServiceOptimize;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.hibernate.reactive.mutiny.Mutiny;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.List;

@ApplicationScoped
public class JobRunner {
    Logger LOGGER = LoggerFactory.getLogger(JobRunner.class);
    private static final ObjectMapper mapper = new ObjectMapper();
    @Inject
    Mutiny.SessionFactory sessionFactory;

    @Inject
    InitRepository initRepository;
    @Inject
    BillingRepository billingRepository;
    @Inject
    BillingServiceOptimize billingOptimize;
//    @Scheduled(every = "10m")
    public Uni<Void> runJob(){
        LOGGER.info("inisialisasi 1000 record");
        return sessionFactory.withTransaction((session,tx) -> getThblRek("202409",0,1000)
                        .onItem().transformToMulti(items-> Multi.createFrom().iterable(items))
                        .onItem().transformToUniAndConcatenate(param->{
                            LOGGER.info("saving billing inisialisasi {}, {}",param.idpel,param.thblrek);
                            InitBilling initBilling = new InitBilling();
                            initBilling.thblrek = param.thblrek;
                            initBilling.idpel = param.idpel;
                            initBilling.flag = "0";
                            initBilling.createdAt = Instant.now();
                            return billingOptimize.getInisialisasiBillingByIdpelThblrek(param)
                                    .onItem().transformToUni(data -> {
                                        try {
                                            initBilling.jsonInit = mapper.writeValueAsString(data);
                                            initBilling.updatedAt = Instant.now();
                                            initBilling.status = "OK";
                                            return initBilling.persist().onItem().transform(saved->saved);
                                        } catch (Exception ex) {
                                            LOGGER.error("Error serializing billing data", ex);
                                            return Uni.createFrom().nullItem(); // Returns a Uni<InitBilling>
                                        }
                                    });
                        })
                        .collect().asList().onItem().transformToUni(items->{
                            if (items.isEmpty()) {
                                LOGGER.info("No items to save.");
                                return Uni.createFrom().voidItem();
                            }
                            LOGGER.info("saving {} items",items.size());
                            return Uni.createFrom().voidItem();
                        }))
                .replaceWithVoid();
    }
    Uni<List<CustomerParam>> getThblRek(String thblrek, int page, int size){
        return sessionFactory.withSession(session -> {
            LOGGER.info("get customer params");
            return billingRepository.getParamThblrek(thblrek,"1",page,size);
        });
    }
}
