package com.iconplus.ap2t.generator.job;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iconplus.ap2t.common.param.InisialisasiBillingDTO;
import com.iconplus.ap2t.data.repository.billing.InitRepository;
import com.iconplus.ap2t.generator.proxy.EngineProxy;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.hibernate.reactive.mutiny.Mutiny;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;

@ApplicationScoped
public class JobHitEngine {
    Logger LOGGER = LoggerFactory.getLogger(JobHitEngine.class);
    final ObjectMapper mapper = new ObjectMapper();
    @Inject
    @RestClient
    EngineProxy engineProxy;
    @Inject
    InitRepository initRepository;
    @Inject
    Mutiny.SessionFactory sessionFactory;
    @ConfigProperty(name = "ROW_COUNT")
    Integer rows;
//    @Scheduled(every = "25s")
    public Uni<Void> hitEngineBilling() {
        return sessionFactory.withSession(session ->
                initRepository.findByThblRekFlag("202501", "0", 0, rows)
                        .onItem().transformToMulti(items -> Multi.createFrom().iterable(items))
                        .onItem().transformToUniAndConcatenate(item -> {
                            try {
                                LOGGER.info("Proses THBL : {}, IDPEL : {} ..", item.thblrek, item.idpel);
                                InisialisasiBillingDTO data = mapper.readValue(item.jsonInit, InisialisasiBillingDTO.class);

                                return engineProxy.runWorkflowV2(data).onItem().transform(body -> {
                                    String response = body.readEntity(String.class);
                                    LOGGER.info("Response engine : {}",response);
                                    item.flag = "1";
                                    item.updatedAt = Instant.now();
                                    return item;
                                });

                            } catch (JsonProcessingException e) {
                                throw new RuntimeException(e);
                            }
                        })
                        .group().intoLists().of(10)  // 🔹 Update per batch 50 item
                        .onItem().transformToUniAndConcatenate(batch -> {
                            LOGGER.info("Updating batch of {} records ...", batch.size());
                            return sessionFactory.withTransaction(tx -> initRepository.persist(batch));
                        }).collect().asList()
                        .replaceWithVoid()
        );
    }
}
