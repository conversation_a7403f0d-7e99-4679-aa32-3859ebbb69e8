package com.iconplus.ap2t.generator.job;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iconplus.ap2t.data.entity.log.LogJobHitungBilling;
import com.iconplus.ap2t.data.repository.billing.BillingRepository;
import com.iconplus.ap2t.data.repository.log.LogJobHitungBillingRepository;
import com.iconplus.ap2t.generator.service.BillingService;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.hibernate.reactive.mutiny.Mutiny;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@ApplicationScoped
public class JobLogHitung {
    Logger LOG = LoggerFactory.getLogger(JobLogHitung.class);
    final ObjectMapper mapper = new ObjectMapper();
    ExecutorService executor = Executors.newFixedThreadPool(10);
    @Inject
    BillingRepository billingRepository;
    @Inject
    Mutiny.SessionFactory sessionFactory;
    @Inject
    BillingService billingService;
    @Inject
    LogJobHitungBillingRepository logJobHitungBillingRepository;
    @ConfigProperty(name = "ROW_COUNT")
    Integer rows;
    @ConfigProperty(name = "THBLREK")
    String thblrek;

//    @Scheduled(every = "1m")
    public Uni<Void> generateLogJob(){
        LOG.info("--  Generate Initialization payload there is not exist---");
        return generateLogUnProcessedV2(rows,thblrek);
    }
    @WithTransaction
    public Uni<Void> generateLogUnProcessedV2 (int rows,String thblrek){
        LOG.info("Proses {} rows to load",rows);
        return sessionFactory.withTransaction((session,tx) ->billingRepository.getCustomerNoLog(rows,thblrek)
                .onItem().transformToMulti(items-> Multi.createFrom().iterable(items))
                .onItem().transformToUniAndConcatenate(param->{
                    LOG.info("-- init : {} - {}",param.idpel,param.thblrek);
                    return billingService.getInisialisasiBillingByIdpelThblrek(param)
                            .ifNoItem().after(Duration.ofMillis(10000))
                            .failWith(new Throwable("-- Proses inisialisasi terlalu lama --"))
                            .onItem().transformToUni(data -> {
                                try {

                                    String payload = mapper.writeValueAsString(data);
                                    LOG.info("--payload : {}",payload);
                                    LogJobHitungBilling logJobHitungBilling = new LogJobHitungBilling();
                                    logJobHitungBilling.id = UUID.randomUUID();
                                    logJobHitungBilling.status_proses=0;
                                    logJobHitungBilling.idpel = param.idpel;
                                    logJobHitungBilling.thblrek = param.thblrek;
                                    logJobHitungBilling.message = "Initialization has Ready";
                                    logJobHitungBilling.payload = payload;
                                    logJobHitungBilling.status_proses=0;

                                    return Panache.withTransaction(logJobHitungBilling::persist)
                                            .onItem().transform(saved->{
                                                LOG.info("--saving log {} - {} -- sucess ---",param.idpel,param.thblrek);
                                                return saved;
                                            });
                                } catch (JsonProcessingException e) {
                                    LOG.error("Error parsing : {}",e.getMessage());
                                    return null;
                                }

                            }).onFailure().recoverWithNull();
                }).collect().asList().replaceWithVoid()
        );
    }
}
