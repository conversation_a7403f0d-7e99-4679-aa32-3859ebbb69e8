package com.iconplus.ap2t.generator.job;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iconplus.ap2t.common.param.InisialisasiBillingDTO;
import com.iconplus.ap2t.common.param.billing.OutputBillingMutasiParam;
import com.iconplus.ap2t.data.repository.billing.BillingRepository;
import com.iconplus.ap2t.data.repository.billing.InitRepository;
import com.iconplus.ap2t.generator.proxy.EngineProxy;
import com.iconplus.ap2t.generator.resource.BillingResource;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.hibernate.reactive.mutiny.Mutiny;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@ApplicationScoped
public class JobRecalculate {
    static final Logger LOGGER = LoggerFactory.getLogger(JobRecalculate.class);
    private static final ObjectMapper mapper = new ObjectMapper();
    @Inject
    @RestClient
    EngineProxy engineProxy;
    @Inject
    InitRepository initRepository;
    @Inject
    BillingRepository billingRepository;
    @Inject
    Mutiny.SessionFactory sessionFactory;
    @Inject
    BillingResource billingResource;

    public Uni<Void> processRecalculate(){
        return sessionFactory.withSession(session -> {
            LOGGER.info("Process 100 rows");
            return getInitialisasi("202501",0,20)
                    .onItem().transformToMulti(items->Multi.createFrom().iterable(items))
                    .onItem().transformToUniAndConcatenate(item->{
                        LOGGER.info("init bill data length : {}",item.length());
                        return runEngine(item);
                    })
                    .collect().asList().replaceWithVoid();
        });
    }
    private Uni<List<String>> getInitialisasi(String thblrek,int page, int size){
            return sessionFactory.withSession(session -> billingRepository.getPelanggan(thblrek,"1",page,size)
                    .onItem().transformToMulti(items-> Multi.createFrom().iterable(items))
                    .onItem().transformToUniAndConcatenate(item->{
                        LOGGER.info("init : {} {}",thblrek,item.idpel);
                        return initRepository.getInitialisasi(item.thblrek,item.idpel);
                    }).collect().asList());
    }
    public Uni<Void> runEngine(String init){
        return sessionFactory.withSession(session -> {
            try {
                InisialisasiBillingDTO data = mapper.readValue(init,InisialisasiBillingDTO.class);
                LOGGER.info("Process for IDPEL : {}",data.idpel);
                return engineProxy.runWorkflowV2(data)
                        .onItem().transform(response->{
                            String content = response.readEntity(String.class);
                            LOGGER.info("Engine Response : {}",content);
                            try {
                                OutputBillingMutasiParam param = mapper.readValue(content, OutputBillingMutasiParam.class);
                                return billingResource.saveBillingMutasi(param).onItem().transform(response2->{
                                    LOGGER.info("Saving Billing Mutasi Response : {}",response2.getMessage());
                                    return response2.getMessage();
                                });
                            } catch (Exception ex){}
                            return content;
                        }).replaceWithVoid();
            } catch (Exception ex){
                ex.printStackTrace();
                return Uni.createFrom().voidItem();
            }
        });
    }
}
