package com.iconplus.ap2t.generator.response;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class SuplaiDILDTO {
    public String idpel;
    public String kdprosesklp;
    public String unitupi;
    public String unitap;
    public String unitup;
    public String jnsmut;
    public String kdmut;
    public String thblmut;
    public String tglnyala;
    public String tglrubah;
    public String kdproses;
    public String nopel;
    public String nama;
    public String tarif;
    public String kdpt;
    public String kdpt2;
    public BigDecimal daya;
    public String faknpremium;
    public BigDecimal dayajbst;
    public String kdbedajbst;
    public String kddk;
    public String kdbacameter;
    public String kdrekG;
    public String copyrek;
    public String kdmeterai;
    public String lokettgk;
    public String kogol;
    public String subkogol;
    public String pemda;
    public String kdppj;
    public String kdinkaso;
    public String kdklp;
    public String kdind;
    public String kdam;
    public String kdkvamaks;
    public String maxDemand;
    public String frt;
    public String fjn;
    public String kdbpt;
    public BigDecimal dayabpt;
    public String kddayabpt;
    public String kdpembmeter;
    public BigDecimal fakm;
    public BigDecimal fakmkvarh;
    public BigDecimal fakmkvam;
    public BigDecimal rpsewatrafo;
    public String kdsewakap;
    public String flagsewakap;
    public BigDecimal faradkap;
    public BigDecimal rpsewakap;
    public String kdangsa;
    public BigDecimal rpangsa;
    public BigDecimal lamaangsa;
    public String thblangs1a;
    public BigDecimal angskea;
    public String kdangsb;
    public BigDecimal rpangsb;
    public BigDecimal lamaangsb;
    public String thblangs1b;
    public BigDecimal angskeb;
    public String kdangsc;
    public BigDecimal rpangsc;
    public BigDecimal lamaangsc;
    public String thblangs1c;
    public BigDecimal angskec;
    public String tarifLm;
    public String kdptLm;
    public String kdpt2Lm;
    public BigDecimal dayaLm;
    public String faknpremiumLm;
    public String frtLm;
    public String fjnLm;
    public BigDecimal fakmLm;
    public BigDecimal fakmkvarhLm;
    public BigDecimal dayajbstLm;
    public String kdinvoice;
    public BigDecimal rata2lwbp;
    public BigDecimal rata2wbp;
    public String jnsmutAde;
    public Integer tahunKe;
    public BigDecimal dayaLmB2b;
    public Integer flagppjangsa;
    public Integer flagppjangsb;
    public Integer flagppjangsc;

    public SuplaiDILDTO() {}
}
