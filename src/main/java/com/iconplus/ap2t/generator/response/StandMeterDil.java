package com.iconplus.ap2t.generator.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

public class StandMeterDil {
    public String rt;
    public String rw;
    public String unitupi;
    public String unitap;
    public String unitup;
    public String nobang;
    public BigDecimal fakmkvam;
    @JsonProperty("kdproses")
    public String kdProses;
    @JsonProperty("ketnobang")
    public String ketNobang;
    @JsonProperty("fkmkwh_lm")
    public BigDecimal fkmKwhLm;
    public String tarif;
    public String kddk;
    @JsonProperty("nometer_kwh")
    public String noMeterKwh;
    @JsonProperty("kdsewakap")
    public String kdSewaKap;
    @JsonProperty("kdbacameter")
    public String kdBacaMeter;
    @JsonProperty("kdaya_lm")
    public String kDayaLm;
    @JsonProperty("tarif_lm")
    public String tarifLm;
    @JsonProperty("tglnyala_pb")
    public String tglNyalaPb;
    @JsonProperty("kdpt_2")
    public String kdpt2;
    @JsonProperty("kdpt_2_lm")
    public String kdpt2Lm;
    @JsonProperty("kddk_awal_kvarh")
    public BigDecimal kddkAwalKvarh;
    @JsonProperty("kdaya")
    public String kDaya;
    @JsonProperty("kdppj")
    public String kdPpj;
    public BigDecimal fakmkvarh;
    @JsonProperty("jenis_mk")
    public String jenisMk;
    @JsonProperty("namapnj")
    public String namaPnj;
    @JsonProperty("kdklp")
    public String kdKlp;
    public BigDecimal fakmkwh;
    public String frt;
    public String idpel;
    public BigDecimal daya;
    public String fjn;
    public String thblmut;
    public String pnj;
    public String kdpt;
    @JsonProperty("daya_lm")
    public BigDecimal dayaLm;
    @JsonProperty("frt_lm")
    public String frtLm;
    @JsonProperty("letakapp")
    public String letaApp;
    @JsonProperty("fkmkvarh_lm")
    public String fkmkvarhLm;
    public String nama;
    public String nopel;
    @JsonProperty("kddk_awal_wbp")
    public BigDecimal kddkAwalWbp;
    @JsonProperty("kddk_awal_lwbp")
    public BigDecimal kddkAwalLwbp;
    public String kdam;
    @JsonProperty("kdpembmeter")
    public String kdPembMeter;
    @JsonProperty("rpsewakap")
    public Number rpSewaKap;
    @JsonProperty("tglrubah_mk")
    public String tglRubahMk;
    public String kdbpt;
    public String tg;
    @JsonProperty("tg_lm")
    public String tgLm;
}
