package com.iconplus.ap2t.secman.resource;

import com.iconplus.ap2t.common.param.auth.AppKeyParam;
import com.iconplus.ap2t.common.response.MessageResult;
import com.iconplus.ap2t.data.entity.secman.AppKey;
import com.iconplus.ap2t.data.repository.secman.AppKeyRepository;
import com.iconplus.ap2t.data.repository.secman.ModuleRepository;
import com.iconplus.ap2t.secman.service.AppKeyService;
import com.iconplus.ap2t.secman.service.AuthenticationService;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.security.PermitAll;
import jakarta.annotation.security.RolesAllowed;
import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.apache.commons.lang3.time.DateUtils;
import org.eclipse.microprofile.jwt.Claims;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.hibernate.reactive.mutiny.Mutiny;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.UUID;

@Path("/api/appkey")
@RequestScoped
@Tag(name = "Kunci Aplikasi",description = "API pengaturan kunci aplikasi")
public class AppKeyResource {
    static Logger LOG = LoggerFactory.getLogger(AppKeyResource.class);
    @Inject
    JsonWebToken jwt;
    @Inject
    Mutiny.SessionFactory sf;

    @Inject
    AppKeyRepository appKeyRepository;
    @Inject
    AuthenticationService authenticationService;
    @Inject
    ModuleRepository moduleRepository;
    @WithSession
    @PostConstruct
    public void postConstruct(){
//        authenticationService.init();
    }
    @GET
    @Path("/")
    @RolesAllowed({"User","Admin"})
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> getAppKey(){

        return Panache.withSession(()->{
            return AppKey.findAll().list()
                    .onItem().ifNotNull().transform(items->Response.ok(items).build())
                    .onFailure().recoverWithItem(Response.ok().build());
        });
    }
    @POST
    @Path("/")
    @RolesAllowed({"User","Admin"})
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> saveAppKey(AppKeyParam param){
        LOG.info("user id : {}",jwt.claim(Claims.upn).get());
        MessageResult notFound = new MessageResult(false,"Module Not Found");
        String uuid = jwt.getClaim(Claims.upn);
        UUID userId = UUID.fromString(uuid);
        return moduleRepository.findById(param.moduleId)
                .onItem().ifNotNull().transformToUni((module)->{
                    AppKey appKey = new AppKey();
                    appKey.apiKey = AppKeyService.generateKey();
                    appKey.apiSecret = AppKeyService.generateSecretKey();
                    appKey.name = param.name;
                    appKey.moduleId = param.moduleId;
                    appKey.description = param.description;
                    appKey.createdBy = userId;
                    Date expired = DateUtils.addYears(new Date(),1);
                    appKey.expiredDate = expired.toInstant();
                    return appKeyRepository.persist(appKey)
                            .onItem().transform(item->Response.ok(item).build());
                }).onItem()
                .ifNull()
                .continueWith(Response.ok(notFound).status(404).build());


    }
    @POST
    @Path("/check")
    @PermitAll
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> checkAppKey(@HeaderParam("key")String key,
                                     @HeaderParam("secret")String secret){
        LOG.info("key to check : {}",key);
        MessageResult failed = new MessageResult(false,"Key InValid!");
        MessageResult success = new MessageResult(true,"Key Valid!");
        if(key==null){
            return Uni.createFrom().item(Response.ok(failed).build());
        }
        return appKeyRepository.findKey(key)
                .onItem().ifNotNull().
                transform(appKey -> {
                    if(appKey.apiSecret.equals(secret)){
                        return Response.ok(success).status(200).build();
                    } else {
                        return Response.ok(failed).status(403).build();
                    }
                })
                .onItem().ifNull().continueWith(Response.ok(failed).status(403).build());
    }
    @DELETE
    @Path("/{key}")
    @RolesAllowed({"User","Admin"})
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> deleteAppKey(@PathParam("key") String key){
        LOG.info("key to delete : {}",key);
        MessageResult failed = new MessageResult(false,"Hapus App Key Gagal!");
        MessageResult success = new MessageResult(true,"Hapus App Key Sukses!");
        return sf.withTransaction((t)->appKeyRepository.findKey(key)
                .onItem().ifNotNull().
                transformToUni(appKey -> {
                    LOG.info("id to delete : {}",appKey.id);
                    return AppKey
                            .deleteById(appKey.id)
                            .onItem()
                            .transform(deleted->Response.ok(success).build());
                })
                .onItem().ifNull().continueWith(Response.ok(failed).build()))
                .onFailure().recoverWithItem(Response.ok(failed).build());
    }

    @POST
    @Path("/search")
    @RolesAllowed({"User", "Admin"})
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    public Uni<Response> searchAppKeyByName(@FormParam("keyword") String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            // Jika keyword kosong, kembalikan respons gagal
            return Uni.createFrom().item(
                Response.status(Response.Status.BAD_REQUEST)
                        .entity(new MessageResult(false, "Keyword tidak boleh kosong!"))
                        .build()
            );
        }
    
        String namePattern = "%" + keyword + "%"; // Pastikan keyword tidak mengandung spasi berlebih
        return appKeyRepository.searchName(namePattern)
                .onItem().transform(items -> {
                    if (items == null || items.isEmpty()) {
                        return Response.status(Response.Status.NOT_FOUND)
                                .entity(new MessageResult(false, "Tidak ada hasil untuk pencarian keyword: " + keyword))
                                .build();
                    }
                    return Response.ok(items).build();
                })
                .onFailure().recoverWithItem(throwable -> 
                    Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                            .entity(new MessageResult(false, "Terjadi kesalahan saat mencari data: " + throwable.getMessage()))
                            .build()
                );
    }
    
}
