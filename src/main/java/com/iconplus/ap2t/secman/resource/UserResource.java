package com.iconplus.ap2t.secman.resource;


import com.iconplus.ap2t.common.param.auth.AccessToken;
import com.iconplus.ap2t.common.param.auth.UserParam;
import com.iconplus.ap2t.common.response.MessageResult;
import com.iconplus.ap2t.data.entity.secman.User;
import com.iconplus.ap2t.data.repository.secman.JabatanRepository;
import com.iconplus.ap2t.data.repository.secman.UserAuditRepository;
import com.iconplus.ap2t.data.repository.secman.UserRepository;
import com.iconplus.ap2t.secman.result.AuthResult;
import com.iconplus.ap2t.secman.service.AppKeyService;
import com.iconplus.ap2t.secman.service.AuthenticationService;
import com.iconplus.ap2t.secman.service.MailServerService;
import com.iconplus.ap2t.secman.service.TokenBlacklistService;
import io.quarkus.elytron.security.common.BcryptUtil;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import jakarta.annotation.security.RolesAllowed;
import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.hibernate.reactive.mutiny.Mutiny;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Path("/api/user")
@RequestScoped
@Tag(name = "Pengguna Aplikasi", description = "pengaturan pengguna aplikasi")
public class UserResource {
    private Logger LOG = LoggerFactory.getLogger(UserResource.class);
    @Inject
    JsonWebToken jwt;

    @Inject
    AuthenticationService authenticationService;
    @Inject
    TokenBlacklistService tokenBlacklistService;
    @Inject
    UserRepository userRepository;
    @Inject
    UserAuditRepository userAuditRepository;
    @Inject
    JabatanRepository jabatanRepository;
    @Inject
    MailServerService mailService;
    @Inject
    Mutiny.SessionFactory sessionFactory;

    @GET
    @Path("/")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    @WithSession
    @Tag(name = "Mengambil daftar pengguna di sistem")
    public Uni<List<User>> getAllUsers(@QueryParam("page") @DefaultValue("0") Integer page,
                                       @QueryParam("size") @DefaultValue("20") Integer size) {
        return userRepository.getAll(page, size);
    }

    @GET
    @Path("/{id}")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    @WithSession
    @Tag(name = "Mengambil pengguna berdasarkan id")
    public Uni<Response> getUserById(@PathParam("id") String id) {
        LOG.info("id : {}", id);

        try {
            UUID uuid = UUID.fromString(id);
            return userRepository.findById(uuid)
                    .onItem().ifNotNull().transform(user -> Response.ok(user).build())
                    .onItem().ifNull().continueWith(
                            Response.status(Response.Status.NOT_FOUND)
                                    .entity(new MessageResult(false, "Data Not Found")).build())
                    .onFailure().recoverWithItem(throwable -> {
                        MessageResult result = new MessageResult(false, "Terjadi kesalahan: " + throwable.getMessage());
                        return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(result).build();
                    });
        } catch (IllegalArgumentException ex) {
            MessageResult result = new MessageResult(false, "ID tidak valid: " + ex.getMessage());
            return Uni.createFrom().item(Response.status(Response.Status.BAD_REQUEST).entity(result).build());
        }
    }

    @GET
    @Path("/me")
    @RolesAllowed("User")
    @Produces(MediaType.APPLICATION_JSON)
    @Tag(name = "Mengambil informasi pengguna aktif")
    public Uni<Response> getUser() {
        // User user = authenticationService.getCurrentUser();
        // LOG.info("token : {}",jwt.getRawToken());
        MessageResult blacklisted = new MessageResult(false, "Token telah di blokir karena sesi telah berakhir");
        if (tokenBlacklistService.isTokenBlacklisted(jwt.getRawToken())) {
            return Uni.createFrom().item(Response.ok(blacklisted).status(403).build());
        }
        LOG.info("Get User from DB");
        return User.findByEmail(jwt.getClaim("email")
                .toString()).onItem()
                .transform(user -> {
                    AuthResult authResult = new AuthResult();
                    authResult.user = user;
                    AccessToken accessToken = new AccessToken(jwt.getRawToken(), jwt.getExpirationTime());
                    authResult.token = accessToken;
                    authenticationService.getInstance().setAuthResult(authResult);
                    return Response.ok(user).build();
                });
        // return Uni.createFrom().item(Response.ok().build());
    }

    @PUT
    @Path("/update/{id}")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    @Tag(name = "Update Pengguna")
    public Uni<Response> updateUser(@PathParam("id") UUID uuid, UserParam userParam) {
        return userRepository.findById(uuid)
                .onItem().ifNotNull().transformToUni(user -> {
                    user.name = userParam.name;
                    user.nip = userParam.nip;
                    user.email = userParam.email;
                    if (userParam.password != null && !userParam.password.isEmpty()) {
                        user.password = BcryptUtil.bcryptHash(userParam.password);
                    }
                    user.ponsel = userParam.ponsel;
                    user.avatar = userParam.avatar;
                    user.jenisPp = userParam.jenisPp;

                    // Update jabatanId if jabatan exists
                    return jabatanRepository.findById(userParam.jabatanId)
                            .onItem().ifNotNull().transformToUni(jabatan -> {
                                user.jabatan = jabatan;
                                return Panache.withTransaction(() -> user.persist())
                                        .onItem().transform(updatedUser -> Response.ok(updatedUser).build())
                                        .onFailure().recoverWithItem(
                                                Response.status(Response.Status.INTERNAL_SERVER_ERROR).build());
                            })
                            .onItem().ifNull()
                            .continueWith(
                                    Response.status(Response.Status.NOT_FOUND).entity("Jabatan Not Found").build())
                            .onFailure()
                            .recoverWithItem(Response.status(Response.Status.INTERNAL_SERVER_ERROR).build());
                })
                .onItem().ifNull()
                .continueWith(Response.status(Response.Status.NOT_FOUND).entity("User Not Found").build())
                .onFailure().recoverWithItem(Response.status(Response.Status.INTERNAL_SERVER_ERROR).build());
    }

    @POST
    @Path("/store")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<Response> saveUser(UserParam userParam) {
        return jabatanRepository.findById(userParam.jabatanId)
                .onItem().ifNotNull().transformToUni(jabatan -> {
                    User user = new User();
                    user.name = userParam.name;
                    user.nip = userParam.nip;
                    user.email = userParam.email;
                    user.password = BcryptUtil.bcryptHash(userParam.password);
                    user.ponsel = userParam.ponsel;
                    user.avatar = userParam.avatar;
                    user.statusApproval = "pending";
                    user.jenisPp = userParam.jenisPp;
                    user.activationCode = AppKeyService.generateRandomString(64);
                    user.tfaCode = AppKeyService.generateToken(6);
                    user.statusLogin = "inactive";
                    user.jabatan = jabatan;

                    // Pesan untuk sukses dan error
                    MessageResult success = new MessageResult(true, "User Berhasil di tambahkan");
                    MessageResult errorMsg = new MessageResult(false, "Terjadi Kesalahan Server");

                    return Panache.withTransaction(() -> User.persist(user)
                            .onItem().transform(item -> Response.ok(success).build())
                            .onFailure().recoverWithItem(() -> Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                                    .entity(errorMsg).build()));
                }).onItem().ifNull().continueWith(Response.status(Response.Status.NOT_FOUND).build());
    }

    @DELETE
    @Path("/{id}")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> deleteUser(@PathParam("id") UUID id) {
        MessageResult success = new MessageResult(true, "Hapus User Sukses!");
        MessageResult notFound = new MessageResult(false, "User tidak ditemukan!");
        MessageResult conflict = new MessageResult(false, "User tidak dapat dihapus karena memiliki data terkait!");

        return Panache.withTransaction(() -> userRepository.findById(id)
                .onItem().ifNotNull().transformToUni(user -> {
                    // Setel id_jabatan pada User menjadi null sebelum menghapus
                    user.setJabatan(null);
                    return user.persist()
                            .onItem()
                            .transformToUni(updatedUser -> updatedUser.delete().onItem()
                                    .transform(deletedUser -> Response.ok(success).build()))
                            .onFailure().recoverWithItem(Response.ok(conflict)
                                    .status(Response.Status.CONFLICT).build());
                })
                .onItem().ifNull().continueWith(Response.ok(notFound).build()));
    }

    @POST
    @Path("/search")
    @RolesAllowed({"User", "Admin"})
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> searchUserByName(@FormParam("keyword") String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            // Validasi input untuk memastikan keyword tidak kosong
            MessageResult result = new MessageResult(false, "Keyword cannot be empty.");
            return Uni.createFrom().item(
                    Response.status(Response.Status.BAD_REQUEST)
                            .entity(result)
                            .build());
        }
    
        String namePattern = "%" + keyword.trim() + "%"; // Hilangkan spasi berlebih
        return userRepository.searchName(namePattern)
                .onItem().transform(items -> {
                    if (items == null || items.isEmpty()) {
                        // Tidak ditemukan hasil
                        return Response.status(Response.Status.NOT_FOUND)
                                .entity(new MessageResult(false, "No users found matching the keyword: " + keyword))
                                .build();
                    }
                    // Berhasil ditemukan
                    return Response.ok(items).build();
                })
                .onFailure().recoverWithItem(throwable -> {
                    // Jika terjadi kesalahan selama proses pencarian
                    MessageResult result = new MessageResult(false, "Error occurred: " + throwable.getMessage());
                    return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                            .entity(result)
                            .build();
                });
    }
    
    @POST
    @Path("/approval")
    @RolesAllowed({ "Admin", "User" })
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    @Produces(MediaType.APPLICATION_JSON)
    @Tag(name = "Aproval pendaftaran pengguna baru")
    public Uni<Response> approvedUser(@FormParam("userid") String userid,
            @FormParam("status") @DefaultValue("rejected") String status) {
        UUID userId = UUID.fromString(userid);
        if (status.equals("approved")) {
            MessageResult approved = new MessageResult(true, "Permohonan pendaftaran Pengguna di setujui");
        } else {
            MessageResult approved = new MessageResult(false, "Permohonan pendaftaran Pengguna di ditolak");
        }
        MessageResult success = new MessageResult(true, "Proses Persetujuan pendaftaran Pengguna Sukses");
        return sessionFactory.withTransaction((s) -> {
            return userRepository.findById(userId).onItem()
                    .transformToUni(user -> {
                        user.statusApproval = status;
                        return Panache.withTransaction(() -> user.persist()).onItem()
                                .transform(result -> mailService.sendApprovalMail(user)
                                        .runSubscriptionOn(Infrastructure.getDefaultExecutor())
                                        .ifNoItem().after(Duration.ofMinutes(2)) // Set a 1-minute timeout (adjust if
                                                                                 // necessary)
                                        .failWith(new RuntimeException("Email sending took too long"))
                                        .onItem().transform(mailSent -> {
                                            LOG.info("Sending approval email to {}", user.email);
                                            return Response.ok(result).status(200).build();
                                        }));
                    }).replaceWith(Response.ok(success).build());
        });
    }

    @GET
    @Path("/dashboard")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    @Tag(name = "Dashboard Pengguna Aplikasi", description = "monitoring user")
    public Uni<Response> getUserSummary() {
        Uni<Long> totalUsers = userRepository.count();
        Uni<Long> pendingUsers = userRepository.countPendingApprovals();
        Uni<Long> onlineUsers = userRepository.countUsersOnline();

        return Uni.combine().all().unis(totalUsers, pendingUsers, onlineUsers).asTuple()
                .onItem().transform(tuple -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("jml_user", tuple.getItem1());
                    result.put("jml_user_pending", tuple.getItem2());
                    result.put("jml_user_online", tuple.getItem3());

                    return Response.ok(result).build();
                })
                .onFailure().recoverWithItem(throwable -> {
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("status", false);
                    errorResult.put("message", "Terjadi kesalahan: " + throwable.getMessage());
                    return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(errorResult).build();
                });
    }

    // @GET
    // @Path("/count")
    // @RolesAllowed({ "User", "Admin" })
    // @Produces(MediaType.APPLICATION_JSON)
    // @WithSession
    // @Tag(name = "Dashboard Data User ")
    // public Uni<Response> getUserCount() {
    //     return userRepository.count()
    //             .onItem().transform(count -> Response.ok(new MessageResult(true, "Total users: " + count)).build())
    //             .onFailure().recoverWithItem(throwable -> {
    //                 MessageResult result = new MessageResult(false, "Terjadi kesalahan: " + throwable.getMessage());
    //                 return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(result).build();
    //             });
    // }

    // @GET
    // @Path("/pending-approvals")
    // @RolesAllowed({ "User", "Admin" })
    // @Produces(MediaType.APPLICATION_JSON)
    // public Uni<Response> getPendingApprovalCount() {
    //     return userRepository.countPendingApprovals()
    //             .onItem()
    //             .transform(count -> Response
    //                     .ok(new MessageResult(true, "Jumlah pengguna yang menunggu persetujuan: " + count)).build())
    //             .onFailure().recoverWithItem(throwable -> {
    //                 MessageResult result = new MessageResult(false, "Terjadi kesalahan: " + throwable.getMessage());
    //                 return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(result).build();
    //             });
    // }

   

}