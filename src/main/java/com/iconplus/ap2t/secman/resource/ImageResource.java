package com.iconplus.ap2t.secman.resource;

import jakarta.ws.rs.*;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Objects;

@Path("/images")
@Tag(name = "Gambar",description = "Ambil Gambar")
public class ImageResource {
    static final Logger LOGGER = LoggerFactory.getLogger(ImageResource.class);
    @GET
    @Path("/{filename}")
    @Produces("image/png")
    public Response getFile(@PathParam("filename")String filename){
        LOGGER.info("Get Image from file");
        String currentDirectory = System.getProperty("user.dir");
        String uploadDir = currentDirectory+ File.separator+"uploads";
        String filePath = uploadDir+File.separator+filename;
        File imageFile = new File(filePath);

        if (!imageFile.exists()) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }
//        try {
//            return FileUtil.fileToBase64String(filePath);
//        } catch (Exception ex){
//            return ex.getMessage();
//        }
        try (InputStream imageStream = new FileInputStream(imageFile)) {
            byte[] imageData = new byte[(int) imageFile.length()];
            int result = imageStream.read(imageData);
            LOGGER.info("Result load image : {}",result);
            return Response.ok(imageData).type("image/png").build();
        } catch (IOException e) {
            LOGGER.error("Load Image Error : {}",e.getMessage());
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity("Failed to read image").build();
        }
    }
    @GET
    @Path("/captcha")
    @Produces("image/jpg")
    @Tag(name = "Generate Captcha")
    public Response generateToken(@QueryParam("token") @DefaultValue("123456") String token){
        LOGGER.info("Generate Number Token Image");
        int width = 290;
        int height = 100;
        BufferedImage buffImg = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = buffImg.createGraphics();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
//            BufferedImage bgImage = ImageIO.read(new URL("https://i2.pickpik.com/photos/870/79/678/banner-header-graph-paper-squared-paper-thumb.jpg"));
            Image bgImage = ImageIO.read(Objects.requireNonNull(getClass().getClassLoader().getResourceAsStream("captchabg.jpg")));

            //fill the rectangle with grey color
            g2d.setColor(Color.GRAY);
            g2d.fillRect(0, 0, width, height);
            g2d.drawImage(bgImage,0,0,null);
            //draw a string
            g2d.setColor(Color.MAGENTA);
            g2d.setFont(new Font("Tahoma", Font.BOLD, 64));
            g2d.drawString(token, 20, 70);
            g2d.dispose();
            ImageIO.write(buffImg, "jpg", baos);
            byte[] bytes = baos.toByteArray();
            buffImg.flush();
            bgImage.flush();
            return Response.ok(bytes).type("image/jpg").build();
        } catch (IOException ex){
            LOGGER.error("Error : {}",ex.getMessage());
        }
        return Response.ok().build();
    }
}
