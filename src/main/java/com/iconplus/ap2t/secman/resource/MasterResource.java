package com.iconplus.ap2t.secman.resource;

import com.iconplus.ap2t.secman.proxy.MasterProxy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.eclipse.microprofile.rest.client.inject.RestClient;

@Path("/api/master")
@ApplicationScoped
@Tag(name = "Master data",description = "master data")
public class MasterResource {
    @Inject
    @RestClient
    MasterProxy masterProxy;
    @GET
    @Path("/ap")
    public Response getAP(){
        return Response.ok(masterProxy.getListAP()).build();
    }
    @GET
    @Path("/ap/upi/{kode}")
    public Response getAPbyUPI(@PathParam("kode") String kode){
        return Response.ok(masterProxy.getAPbyUPI(kode)).build();
    }
    @GET
    @Path("/up")
    public Response getUP(){
        return Response.ok(masterProxy.getListUP()).build();
    }
    @GET
    @Path("/up/ap/{kode}")
    public Response getUPbyAP(@PathParam("kode") String kode){
        return Response.ok(masterProxy.getUpByAp(kode)).build();
    }
    @GET
    @Path("/upi")
    public Response getUpi(){
        return Response.ok(masterProxy.getListUpi()).build();
    }
}
