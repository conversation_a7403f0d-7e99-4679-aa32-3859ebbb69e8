package com.iconplus.ap2t.secman.resource;

import com.iconplus.ap2t.common.param.auth.AccessToken;
import com.iconplus.ap2t.common.param.auth.TfaParam;
import com.iconplus.ap2t.common.response.MessageResult;
import com.iconplus.ap2t.data.entity.secman.User;
import com.iconplus.ap2t.data.repository.secman.JabatanRepository;
import com.iconplus.ap2t.data.repository.secman.UserRepository;
import com.iconplus.ap2t.secman.response.LoginResponse;
import com.iconplus.ap2t.secman.result.AuthResult;
import com.iconplus.ap2t.secman.service.*;
import io.quarkus.elytron.security.common.BcryptUtil;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.quarkus.qute.Template;
import io.quarkus.qute.TemplateInstance;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.security.PermitAll;
import jakarta.annotation.security.RolesAllowed;
import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.hibernate.reactive.mutiny.Mutiny;
import org.jboss.resteasy.reactive.PartType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Path("/auth")
@RequestScoped
@Tag(name = "Autentikasi Pengguna",description = "Pengaturan autorisasi pengguna")
public class AuthResource {
    final Logger LOGGER = LoggerFactory.getLogger(AuthResource.class);
    @Inject
    Ap2tJwtService jwtService;
    @Inject
    UploadService uploadService;
    @Inject
    UserRepository userRepository;
    @Inject
    JsonWebToken jwt;
    @Inject
    AuthenticationService authenticationService;

    @Inject
    TokenBlacklistService tokenBlacklistService;

    private static final Set<String> blacklist = ConcurrentHashMap.newKeySet();

    @Inject
    JabatanRepository jabatanRepository;
    @Inject
    Template activated;
    @Inject
    Template code_invalid;
    @Inject
    Mutiny.SessionFactory sessionFactory;
    @Inject
    MailServerService mailService;
    public boolean isTokenBlacklisted(String token) {
        return blacklist.contains(token);
    }
    @POST
    @Path("/register")
    @PermitAll
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    public Uni<Response> Register(@FormParam("name") String name,
                                  @FormParam("email") String email,
                                  @FormParam("password") String password,
                                  @FormParam("nip") String nip,
                                  @FormParam("jenis_pp") String jenisPp,
                                  @FormParam("jabatan_id") Long jabatanId,
//                                  @FormParam("level_id") Long levelId,
                                  @FormParam("phone") String phone,
                                  @FormParam("kode_unit") String unitUp,
                                  @FormParam("file") @PartType(MediaType.APPLICATION_OCTET_STREAM)
                                 InputStream file){
        MessageResult notFound = new MessageResult(false,"Jabatan tidak ada");
        MessageResult noFile = new MessageResult(false,"File tidak ada");
        if(file==null){
            return Uni.createFrom().item(Response.ok(noFile).build());
        }

        return sessionFactory.withTransaction(session -> uploadService.uploadFile(file).onItem().transformToUni(filename->{
            return jabatanRepository.findById(jabatanId).onItem().ifNotNull()
                    .transformToUni(jabatan->{
                        User user = new User();
                        user.name = name;
                        user.email = email;
                        user.password = BcryptUtil.bcryptHash(password);
                        user.nip = nip;
                        user.jenisPp = jenisPp;
                        user.kodeUnit = unitUp;
                        user.ponsel = phone;
                        user.jabatan = jabatan;
                        user.avatar = filename;
                        user.levelId = jabatan.userLevel.id;
                        user.statusApproval = "pending";
                        user.statusLogin = "inactive";
                        user.activationCode = AppKeyService.generateRandomString(64);
                        user.tfaCode = AppKeyService.generateToken(6);
                        AccessToken publicToken = jwtService.generateUserToken(user);

                        return userRepository.persist(user).onItem().transformToUni(user1->{
                            AuthResult result = new AuthResult(user1,publicToken);
                            return mailService.sendRegisterNotification(result.user)
                                    .onItem().transform(mailSent->Response.ok(result).build());
                        });
                    })
                    .onItem().ifNull().continueWith(Response.ok(notFound).build());
        }));

    }
    @POST
    @Path("/login")
    @PermitAll
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    public Uni<Response> Login(@FormParam("email") String email,
                               @FormParam("password") String password){
        LOGGER.info("Attempting login for email: {}", email);
        MessageResult notFound = new MessageResult(false, "User Not Registered");
        MessageResult invalidCredentials = new MessageResult(false, "Invalid Credentials");
        MessageResult invalidStatus = new MessageResult(false, "Pengguna belum di ijinkan masuk, silakan minta approval Admin terlebih dahulu ya?");
        return sessionFactory.withSession((session -> User.findByEmail(email)
                .onItem().ifNotNull().transformToUni(user -> {
                    LOGGER.info("Comparing password for user: {}", user.email);
                    // Check if the password matches
                    if(user.statusApproval.equals("pending")){
                        return Uni.createFrom().item(Response.ok(invalidStatus).build());
                    }
                    if (BcryptUtil.matches(password, user.password)) {
                        // Generate JWT Token
                        MessageResult result=new MessageResult(true,"Masukkan Angka yang tampil ini");
                        user.tfaCode = AppKeyService.generateToken(6);
                        LoginResponse loginResponse = new LoginResponse();
                        loginResponse.userId = user.id.toString();
                        loginResponse.captchaUrl = String.format("/images/captcha?token=%s",user.tfaCode);
                        result.data = loginResponse;
                        // Persist the user and send TFA email
                        return Panache.withTransaction(() -> user.persist())
                                .onItem().transform(persistedUser -> {

                                    // Send TFA email notification asynchronously
//                                    return mailService.sendTFANotification(user)
//                                            .runSubscriptionOn(Infrastructure.getDefaultExecutor())
//                                            .ifNoItem().after(Duration.ofMinutes(2)) // Set a 1-minute timeout (adjust if necessary)
//                                            .failWith(new RuntimeException("Email sending took too long"))
//                                            .onItem().transform(mailSent -> {
//                                                LOGGER.info("TFA email sent successfully to {}", user.email);
//                                                return Response.ok(result).status(200).build();
//                                            });
                                    return Response.ok(result).build();
                                });
                    } else {
                        // Invalid password
                        return Uni.createFrom().item(Response.ok(invalidCredentials).status(403).build());
                    }
                })
                .onItem().ifNull().continueWith(Response.ok(notFound).status(404).build())));
    }
    @GET
    @Path("/logout")
    @RolesAllowed({"User","Admin"})
    @Produces(MediaType.APPLICATION_JSON)
    public Response logoutSystem(){
        tokenBlacklistService.blacklistToken(jwt.getRawToken());
        authenticationService.cookieLogout();
        MessageResult succeed = new MessageResult(true,"Logout Sukses!");
        return Response.ok(succeed).status(200).build();
    }
    @GET
    @Path("/activation/{code}")
    @PermitAll
    @Produces(MediaType.TEXT_HTML)
    public Uni<TemplateInstance> activateUser(@PathParam("code") String code){
        return userRepository.activationValid(code)
                .onItem().transform(value->{
                    if(value){
                        return activated.instance();
                    }
                    return code_invalid.instance();
                });
    }
    @POST
    @Path("/twofactor")
    @PermitAll
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<Response> tfaValidation(TfaParam param){
        MessageResult success = new MessageResult(true,"Token Valid");
        MessageResult invalid = new MessageResult(false,"Token Invalid!");
        return sessionFactory.withSession((session -> userRepository.validTfa(param.userId,param.code)
                .onItem().ifNotNull().transform(user->{
                    AccessToken token = jwtService.generateUserToken(user);
                    // Create AuthResult
                    AuthResult result = new AuthResult();
                    user.statusLogin = "tfa"; // Set TFA status
                    user.tfaCode = AppKeyService.generateToken(6); // Generate TFA Code
                    result.user = user;
                    result.token = token;

                    // Set authentication result in the service
                    authenticationService.getInstance().setAuthResult(result);
                    return Response.ok(result).build();
                }).onItem().ifNull().continueWith(Response.ok(invalid).status(500).build())));
    }
    @POST
    @Path("/check")
    @PermitAll
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    public Uni<Response> checkEmail(@FormParam("email") String email){
        MessageResult success = new MessageResult(true,"Email Pengguna Sudah Ada");
        MessageResult notFound = new MessageResult(false,"Email Pengguna Belum Terdaftar");
        return sessionFactory.withSession((session -> userRepository
                .findByEmail(email).onItem().ifNotNull().transform(user->Response.ok(success).build())
                .onItem().ifNull().continueWith(Response.ok(notFound).build())));
    }
}