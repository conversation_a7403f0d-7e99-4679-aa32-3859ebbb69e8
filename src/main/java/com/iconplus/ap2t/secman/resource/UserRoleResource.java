package com.iconplus.ap2t.secman.resource;

import com.iconplus.ap2t.common.response.MessageResult;
import com.iconplus.ap2t.data.entity.secman.UserRole;
import com.iconplus.ap2t.data.repository.secman.UserRoleRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.security.RolesAllowed;
import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.jboss.resteasy.reactive.RestQuery;

import java.util.List;
import java.util.UUID;

@Path("/api/user/role")
@RequestScoped
@Tag(name = "<PERSON>an <PERSON>",description = "pengaturan peran pengguna")
public class UserRoleResource {

    @Inject
    UserRoleRepository userRoleService;

    @GET
    @Path("/list")
    @WithSession
    public Uni<List<UserRole>> getUsersByRoleId(@RestQuery Long roleId) {
        return userRoleService.findByRoleId(roleId);
    }

    @GET
    @Path("/{roleid}")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    @WithSession
    public Uni<Response> getMenuById(
        @QueryParam("roleId") String roleIdQueryParam) {
        try {
            UUID roleId = roleIdQueryParam != null ? UUID.fromString(roleIdQueryParam) : null;

            return userRoleService.findById(roleId)
                .onItem().transform(item -> Response.ok(item).build())
                .onFailure().recoverWithItem(Response.status(Response.Status.NOT_FOUND).entity("Data Not Found").build());
        } catch (Exception ex) {
            MessageResult result = new MessageResult();
            result.status = false;
            result.message = ex.getMessage();
            return Uni.createFrom().item(Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(result).build());
        }
    }

}
