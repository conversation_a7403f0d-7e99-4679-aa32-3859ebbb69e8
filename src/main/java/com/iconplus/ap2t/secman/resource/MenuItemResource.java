package com.iconplus.ap2t.secman.resource;

import com.iconplus.ap2t.common.param.auth.MenuItemParam;
import com.iconplus.ap2t.common.response.MessageResult;
import com.iconplus.ap2t.data.entity.secman.MenuItem;
import com.iconplus.ap2t.data.repository.secman.MenuItemRepository;
import com.iconplus.ap2t.data.repository.secman.MenuRepository;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.security.RolesAllowed;
import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.jwt.Claims;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.UUID;

@Path("/api/menu_items")
@Produces(MediaType.APPLICATION_JSON)
@RequestScoped
@Tag(name = "Menu Item", description = "pengaturan menu item")
public class MenuItemResource {
    Logger LOGGER = LoggerFactory.getLogger(MenuItemResource.class);
    @Inject
    JsonWebToken jwt;

    @Inject
    MenuItemRepository menuItemRepository;

    @Inject
    MenuRepository menuRepository;

    @GET
    @Path("/")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> getAllMenuItems(@QueryParam("id") Long menuId) {
        if (menuId != null && menuId > 0) {
            // Jika menuId valid, ambil menu items berdasarkan menuId
            return menuItemRepository.getByMenu(menuId)
                    .onItem().ifNotNull().transform(items -> {
                        if (items.isEmpty()) {
                            // Jika tidak ada data ditemukan
                            MessageResult result = new MessageResult(false,
                                    "No menu items found for the specified menuId.");
                            return Response.status(Response.Status.NOT_FOUND).entity(result).build();
                        }
                        return Response.ok(items).build();
                    })
                    .onItem().ifNull().continueWith(() -> {
                        // Jika terjadi kesalahan atau menuId tidak valid
                        MessageResult result = new MessageResult(false, "Invalid menuId or no menu items found.");
                        return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
                    });
        }

        // Jika menuId tidak diberikan atau 0, ambil semua menu items
        return menuItemRepository.getAll()
                .onItem().ifNotNull().transform(items -> {
                    if (items.isEmpty()) {
                        // Jika tidak ada menu items
                        MessageResult result = new MessageResult(false, "No menu items available.");
                        return Response.status(Response.Status.NOT_FOUND).entity(result).build();
                    }
                    return Response.ok(items).build();
                })
                .onItem().ifNull().continueWith(() -> {
                    // Jika terjadi kesalahan dalam pengambilan data
                    MessageResult result = new MessageResult(false, "Error retrieving menu items.");
                    return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(result).build();
                });
    }

    @POST
    @Path("/store")
    @RolesAllowed({ "User", "Admin" })
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<Response> saveItemMenu(@Valid MenuItemParam menuItemParam) {
        String userUuid = jwt.getClaim(Claims.upn); // Ambil klaim JWT
        // Mencoba untuk menyimpan item menu berdasarkan menuId
        return menuRepository.findById(menuItemParam.menuId)
                .onItem().ifNotNull().transformToUni(menu -> {
                    // Membuat objek MenuItem dan mengisi nilai dari MenuItemParam
                    MenuItem menuItem = new MenuItem();
                    menuItem.title = menuItemParam.title;
                    menuItem.tooltip = menuItemParam.tooltip;
                    menuItem.description = menuItemParam.description;
                    menuItem.parentId = menuItemParam.parentId;
                    menuItem.url = menuItemParam.url;
                    menuItem.target = menuItemParam.target;
                    menuItem.iconClass = menuItemParam.iconClass;
                    menuItem.color = menuItemParam.color;
                    menuItem.menuOrder = menuItemParam.menuOrder;
                    menuItem.parameters = menuItemParam.parameters;
                    menuItem.enabled = menuItemParam.enabled;
                    menuItem.menuId = menu.id;
                    menuItem.createdBy = UUID.fromString(userUuid);
                    menuItem.createdAt = Instant.now();

                    // Pesan untuk sukses dan error
                    MessageResult success = new MessageResult(true, "Menu Item Berhasil di tambahkan");
                    MessageResult errorMenuId = new MessageResult(true, "Menu ID tidak ditemukan");
                    MessageResult errorMsg = new MessageResult(false, "Terjadi Kesalahan Server");

                    return Panache.withTransaction(() -> MenuItem.persist(menuItem)
                            .onItem().transform(item -> Response.ok(success).build())
                            .onFailure().recoverWithItem(() -> Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                                    .entity(errorMsg).build()));
                })
                .onItem().ifNull().continueWith(() -> {
                    // Jika menuId tidak ditemukan, respons 404
                    return Response.status(Response.Status.NOT_FOUND)
                            .entity("Menu dengan menuId " + menuItemParam.menuId + " tidak ditemukan")
                            .build();
                });
    }

    @POST
    @Path("/update/{id}")
    @RolesAllowed({ "User", "Admin" })
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<Response> updateItemMenu(@PathParam("id") Long id,
            @Valid MenuItemParam menuItemParam) {
        LOGGER.info("Update Item Menu");
        String userUuid = jwt.getClaim(Claims.upn);

        // Validasi apakah menuId valid
        return menuRepository.findById(menuItemParam.menuId)
                .onItem().ifNotNull().transformToUni(menu -> {
                    // Menu ID valid, lanjutkan ke update menu item
                    return menuItemRepository.findById(id)
                            .onItem().ifNotNull().transformToUni(menuItem -> {
                                // Mengupdate properti menu item dengan data dari menuItemParam
                                menuItem.title = menuItemParam.title;
                                menuItem.tooltip = menuItemParam.tooltip;
                                menuItem.description = menuItemParam.description;
                                menuItem.parentId = menuItemParam.parentId;
                                menuItem.url = menuItemParam.url;
                                menuItem.target = menuItemParam.target;
                                menuItem.iconClass = menuItemParam.iconClass;
                                menuItem.color = menuItemParam.color;
                                menuItem.menuOrder = menuItemParam.menuOrder;
                                menuItem.parameters = menuItemParam.parameters;
                                menuItem.enabled = menuItemParam.enabled;
                                menuItem.menuId = menuItemParam.menuId;
                                menuItem.updatedBy = UUID.fromString(userUuid);
                                menuItem.updatedAt = Instant.now();

                                // Pesan untuk sukses dan error
                                MessageResult success = new MessageResult(true, "Menu Item Berhasil di Update");
                                MessageResult errorMsg = new MessageResult(false, "Terjadi Kesalahan Server");

                                // Melakukan transaksi dan menyimpan perubahan menu item
                                return Panache.withTransaction(() -> menuItem.persist()
                                        .onItem().transform(item -> Response.ok(success).build())
                                        .onFailure()
                                        .recoverWithItem(() -> Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                                                .entity(errorMsg).build()));
                            })
                            .onItem().ifNull().continueWith(() -> {
                                // Jika menu item dengan ID tidak ditemukan, respons 404
                                return Response.status(Response.Status.NOT_FOUND)
                                        .entity("Menu Item dengan ID " + id + " tidak ditemukan")
                                        .build();
                            });
                })
                .onItem().ifNull().continueWith(() -> {
                    // Jika menuId tidak ditemukan, respons 404
                    return Response.status(Response.Status.NOT_FOUND)
                            .entity("Menu dengan ID " + menuItemParam.menuId + " tidak ditemukan")
                            .build();
                });
    }

    @GET
    @Path("/{id}")
    @RolesAllowed({ "User", "Admin" })
    public Uni<Response> getMenuItemById(@PathParam("id") Long id) {
        MessageResult failed = new MessageResult(false, "Data tidak ada");
        return menuItemRepository.findById(id)
                .onItem().ifNotNull().transform(menu -> Response.ok(menu).build())
                .onItem().ifNull().continueWith(Response.ok(failed).status(404).build());
    }

    @GET
    @Path("bymenu/{id}")
    @RolesAllowed({ "User", "Admin" })
    public Uni<Response> getMenuItemByMenuId(@PathParam("id") Long id) {
        MessageResult failed = new MessageResult(false, "Menu ID tidak ditemukan");
    
        return menuItemRepository.findByMenuId(id)
                .onItem().ifNotNull().transform(menuItems -> Response.ok(menuItems).build())
                .onItem().ifNull().continueWith(() -> Response.status(404).entity(failed).build());
    }
    

    @DELETE
    @Path("/{id}")
    @RolesAllowed({ "User", "Admin" })
    public Uni<Response> deleteAuthor(@PathParam("id") Long id) {
        MessageResult success = new MessageResult(true, "Hapus Menu Item Sukses!");
        MessageResult notFound = new MessageResult(false, "Menu Item tidak ditemukan!");

        return Panache.withTransaction(() -> menuItemRepository.findById(id)
                .onItem().ifNotNull().transformToUni(menuItem -> menuItemRepository.deleteById(id)
                        .replaceWith(Response.ok(success).build()))
                .onItem().ifNull()
                .continueWith(() -> Response.status(Response.Status.NOT_FOUND).entity(notFound).build()));
    }

    @POST
    @Path("/search")
    @RolesAllowed({ "User", "Admin" })
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    public Uni<Response> searchMenuItemByTitle(@FormParam("Title") String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            MessageResult error = new MessageResult(false, "Keyword tidak boleh kosong");
            return Uni.createFrom().item(Response.status(Response.Status.BAD_REQUEST).entity(error).build());
        }
        MessageResult failed = new MessageResult(false, "Menu Item dengan nama tersebut tidak ditemukan");

        String title = "%" + keyword + "%";
        return menuItemRepository.searchTitle(title).onItem().transform(items -> Response.ok(items).build())
                .onItem().ifNull().continueWith(Response.status(Response.Status.NOT_FOUND).entity(failed).build());
    }

}
