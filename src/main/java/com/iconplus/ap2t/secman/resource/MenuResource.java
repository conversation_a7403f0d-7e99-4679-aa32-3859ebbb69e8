package com.iconplus.ap2t.secman.resource;

import com.iconplus.ap2t.common.param.auth.MenuParam;
import com.iconplus.ap2t.common.response.MessageResult;
import com.iconplus.ap2t.data.entity.secman.Menu;
import com.iconplus.ap2t.data.repository.secman.MenuRepository;
import com.iconplus.ap2t.data.repository.secman.ModuleRepository;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.security.RolesAllowed;
import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.jwt.Claims;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@Path("/api/menu")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequestScoped
@Tag(name = "Menu Aplikasi",description = "pengaturan menu aplikasi")
public class MenuResource {
    Logger LOGGER = LoggerFactory.getLogger(MenuResource.class);
    @Inject
    MenuRepository menuRepository;

    @Inject
    ModuleRepository moduleRepository;

    @Inject
    JsonWebToken jwt;

    @GET
    @Path("/")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    @WithSession
    public Uni<List<Menu>> getAllMenus() {
        return menuRepository.listAll();
    }

    @GET
    @Path("/{id}")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    @WithSession
    public Uni<Response> getMenuById(@PathParam("id") Long id) {
         MessageResult failed = new MessageResult(false,"Data Tidak di Temukan");
        return menuRepository.findById(id)
                .onItem().ifNotNull().transform(menu -> Response.ok(menu).build())
                .onItem().ifNull().continueWith(Response.ok(failed).status(404).build());
    }

   @POST
   @Path("/store")
   @RolesAllowed({ "User", "Admin" })
   @Produces(MediaType.APPLICATION_JSON)
   @Consumes(MediaType.APPLICATION_JSON)
   public Uni<Response> saveMenu(@Valid MenuParam menuParam) {
         MessageResult failed = new MessageResult(false,"Module id Tidak di Temukan");
       String userUuid = jwt.getClaim(Claims.upn);
        return moduleRepository.findById(menuParam.moduleId)
                .onItem().ifNotNull().transformToUni(module->{
            Menu menu = new Menu();
            menu.name = menuParam.name;
            menu.description = menuParam.description;
            menu.enabled = menuParam.enabled;
            menu.moduleId = module.id;
            menu.createdBy = UUID.fromString(userUuid);
            menu.createdAt = Instant.now();

            return Panache.withTransaction(() -> Menu.persist(menu)
               .onItem().transform(item -> Response.ok(item).build()));
        }).onItem().ifNull().continueWith(Response.ok(failed).status(404).build());
    }
    @POST
    @Path("/update/{id}")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<Response> updateMenu(@PathParam("id") Long id,
                                    @Valid MenuParam menuParam) {
        MessageResult failed = new MessageResult(false,"Id Module Tidak di Temukan");
        MessageResult success = new MessageResult(true,"Update Menu Sukses");
        String userUuid = jwt.getClaim(Claims.upn);
        return menuRepository.findById(id)
                .onItem().ifNotNull().transformToUni(menu->{
                    menu.name = menuParam.name;
                    menu.description = menuParam.description;
                    menu.enabled = menuParam.enabled;
                    menu.moduleId = menuParam.moduleId;
                    menu.updatedAt = Instant.now();
                    menu.updatedBy = UUID.fromString(userUuid);
                    return Panache.withTransaction(() -> menu.persist()
                            .onItem().transform(item -> {
                                success.data = item;
                                return Response.ok(success).build();
                            }));
                }).onItem().ifNull().continueWith(Response.ok(failed).status(404).build());
    }
    @DELETE
    @Path("/{id}")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> deleteMenu(@PathParam("id") Long id) {
        MessageResult success = new MessageResult(true, "Hapus Menu Sukses!");
        MessageResult failed = new MessageResult(false, "Data Menu Tidak Ada");
        return Panache.withTransaction(() -> menuRepository.findById(id)
                .onItem().ifNotNull().transformToUni(item -> {
                    return Menu.deleteById(id)
                            .onItem().transform(baru -> Response.ok(success).build());
                }).onItem()
                .ifNull().continueWith(Response.ok(failed).status(404).build()));
    }

    @POST
    @Path("/search")
    @RolesAllowed({"User","Admin"})
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    public Uni<Response> searchMenuByName(@FormParam("Name") String keyword) {
            if (keyword == null || keyword.trim().isEmpty()) {
            MessageResult error = new MessageResult(false, "Keyword tidak boleh kosong");
            return Uni.createFrom().item(Response.status(Response.Status.BAD_REQUEST).entity(error).build());
        }
        MessageResult failed = new MessageResult(false, "Menu dengan nama tersebut tidak ditemukan");
        String name = "%"+keyword+"%";
        return menuRepository.searchName(name).onItem().transform(items -> Response.ok(items).build())
         .onItem().ifNull().continueWith(Response.status(Response.Status.NOT_FOUND).entity(failed).build());
    }

}