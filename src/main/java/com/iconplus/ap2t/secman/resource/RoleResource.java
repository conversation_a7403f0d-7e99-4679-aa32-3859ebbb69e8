package com.iconplus.ap2t.secman.resource;

import com.iconplus.ap2t.common.param.auth.RoleParam;
import com.iconplus.ap2t.common.response.MessageResult;
import com.iconplus.ap2t.data.entity.secman.Role;
import com.iconplus.ap2t.data.repository.secman.RoleRepository;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.security.RolesAllowed;
import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.hibernate.reactive.mutiny.Mutiny;


@Path("/api/role")
@RequestScoped
@Tag(name = "<PERSON><PERSON> aplik<PERSON>",description = "API pengaturan peran pengguna")
public class RoleResource {
    @Inject
    Mutiny.SessionFactory sf;
    @Inject
    RoleRepository roleRepository;

    @GET
    @Path("/all")
    @RolesAllowed({"User","Admin"})
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> getAllRoles() {
        return sf.withSession(s-> Role.listAll()
                .onItem().transform(items->Response.ok(items).build()));
    }

    @GET
    @Path("/{id}")
    @RolesAllowed({"User", "Admin"})
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> getRoleById(@PathParam("id") Long id) {
        return Role.findById(id)
            .onItem().ifNotNull().transform(role -> Response.ok(role).build())
            .onItem().ifNull().continueWith(Response.status(Response.Status.NOT_FOUND)::build);
    }


    @POST
    @Path("/create")
    @RolesAllowed({"User", "Admin"})
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
   public Uni<Response> saveRole(RoleParam param){
    Role role = new Role();
    role.name = param.name;
    role.description = param.description;
    role.display_name = param.displayName;
    role.parent_id = param.parentId;
    
    return Panache.withTransaction(()->Role.persist(role).onItem().transform(item->Response.ok(item).build()));
   }

   
    @DELETE
    @Path("/{id}")
    @RolesAllowed({"User", "Admin"})
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> deleteRole(@PathParam("id") Long id){
        return Panache.withTransaction(()->roleRepository.findById(id)
            .onItem().ifNotNull().transformToUni(item->{
            return Role.deleteById(id)
                .onItem().transform(baru->Response.ok().status(200).build());
        }).onItem()
        .ifNull().continueWith(Response.ok().status(404).build()));
    }

 @POST
@Path("/search")
@RolesAllowed({"User", "Admin"})
@Consumes(MediaType.APPLICATION_FORM_URLENCODED)
public Uni<Response> searchRoleByName(@FormParam("keyword") String keyword) {
    if (keyword == null || keyword.trim().isEmpty()) {
        // Jika keyword kosong, kembalikan respons gagal
        return Uni.createFrom().item(
            Response.status(Response.Status.BAD_REQUEST)
                    .entity(new MessageResult(false, "Keyword tidak boleh kosong!"))
                    .build()
        );
    }

    String namePattern = "%" + keyword + "%"; // Pastikan keyword tidak mengandung spasi berlebih
    return roleRepository.searchName(namePattern)
            .onItem().transform(items -> {
                if (items == null || items.isEmpty()) {
                    return Response.status(Response.Status.NOT_FOUND)
                            .entity(new MessageResult(false, "Tidak ada hasil untuk pencarian keyword: " + keyword))
                            .build();
                }
                return Response.ok(items).build();
            })
            .onFailure().recoverWithItem(throwable -> 
                Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                        .entity(new MessageResult(false, "Terjadi kesalahan saat mencari data: " + throwable.getMessage()))
                        .build()
            );
}


}