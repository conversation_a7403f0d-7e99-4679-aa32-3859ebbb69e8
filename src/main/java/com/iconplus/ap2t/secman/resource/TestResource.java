package com.iconplus.ap2t.secman.resource;

import com.iconplus.ap2t.common.response.MessageResult;
import com.iconplus.ap2t.secman.service.MailServerService;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Path("/api/test")
@ApplicationScoped
@Tag(name = "Test",description = "pengujian email")
public class TestResource {
    Logger LOG = LoggerFactory.getLogger(TestResource.class);
    @Inject
    MailServerService mailServerService;
    @PostConstruct
    public void postConstruct(){
        System.setProperty("mail.smtp.ssl.trust", "*");
    }
    @Path("/mail")
    @POST
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> sendTest(@FormParam("email")String email,
                                  @FormParam("subject")String subject,
                                  @FormParam("body")String body){
        LOG.info("Sending Test Email to : {}",email);
        MessageResult suksesResponse = new MessageResult(true,"Kirim email sukses");
        MessageResult gagal = new MessageResult(false,"Kirim email Gagal");
        return mailServerService.testEmail(email,subject,body)
                .onItem().transform(success->success ? Response.ok(suksesResponse).build() : Response.ok(gagal).build());
    }
}
