package com.iconplus.ap2t.secman.resource;

import com.iconplus.ap2t.common.param.auth.JabatanParam;
import com.iconplus.ap2t.common.response.MessageResult;
import com.iconplus.ap2t.data.entity.secman.Jabatan;
import com.iconplus.ap2t.data.repository.secman.JabatanRepository;
import com.iconplus.ap2t.data.repository.secman.UserLevelRepository;
import com.iconplus.ap2t.secman.service.DataImporter;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.security.PermitAll;
import jakarta.annotation.security.RolesAllowed;
import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@Path("/api/jabatan")
@RequestScoped
@Tag(name = "Jabatan", description = "pengaturan data jabatan")
public class JabatanResource {
    final Logger LOG = LoggerFactory.getLogger(JabatanResource.class);
    @Inject
    JabatanRepository jabatanRepository;

    @Inject
    DataImporter dataImporter;

    @Inject
    UserLevelRepository userLevelRepository;

    @PostConstruct
    public void PostConstruct() {

    }

    @GET
    @Path("/")
    @PermitAll
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> listAll(@QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("100") int size) {

        return jabatanRepository.getPerPage(page, size).onItem().transform(items -> Response.ok(items).build());
    }
    @GET
    @Path("/{id}")
    @PermitAll
    @WithSession
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> getDetail(@PathParam("id") Long id) {
        return jabatanRepository.findById(id)
            .onItem().ifNotNull().transform(jabatan -> 
                Response.ok(jabatan).build()
            )
            .onItem().ifNull().continueWith(() -> 
                Response.status(Response.Status.NOT_FOUND)
                        .entity(new MessageResult(false, "Jabatan tidak ditemukan!"))
                        .build()
            );
    }
    

    @GET
    @Path("/level/{level}")
    @PermitAll
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<List<Jabatan>> getByLevel(@PathParam("level") String level,
                                         @QueryParam("page") @DefaultValue("0") Integer page,
                                         @QueryParam("size") @DefaultValue("20") Integer size) {
        LOG.info("Get Jabatan Level : {}, page {}", level, page);
        return jabatanRepository.getByLevel(level, page, size);
    }

    @POST
    @Path("/")
    @RolesAllowed({ "User", "Admin" })
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> save(JabatanParam jabatanParam) {
        return userLevelRepository.findById(jabatanParam.userLevel).onItem().ifNotNull().transformToUni(userlever -> {
            Jabatan jabatan = new Jabatan();
            jabatan.kode = jabatanParam.kode;
            jabatan.name = jabatanParam.name;
            jabatan.description = jabatanParam.description;
            jabatan.layerUnit = jabatanParam.layerUnit;
            jabatan.userLevel = userlever;
            jabatan.status = jabatanParam.status;
            return jabatanRepository.persist(jabatan)
                    .onItem().transform(saved -> Response.ok(saved).status(Response.Status.CREATED).build());
        }).onItem().ifNull().continueWith(Response.status(Response.Status.NOT_FOUND).build());

    }

    @DELETE
    @Path("/{id}")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> deleteJabatan(@PathParam("id") Long id) {
        MessageResult success = new MessageResult(true, "Hapus Jabatan Sukses!");
        MessageResult failed = new MessageResult(false, "Data Tidak Ada");
        MessageResult conflict = new MessageResult(false, "Jabatan tidak dapat dihapus karena memiliki data terkait!");
        return Panache.withTransaction(() -> jabatanRepository.findById(id)
                .onItem().ifNotNull().transformToUni(item -> {
                    item.setUserLevel(null);
                    return item.persist()
                            .onItem()
                            .transformToUni(updateJabatan -> updateJabatan.delete().onItem()
                                    .transform(deletedUser -> Response.ok(success).build()))
                            .onFailure().recoverWithItem(Response.ok(conflict)
                                    .status(Response.Status.CONFLICT).build());
                }).onItem()
                .ifNull().continueWith(Response.ok(failed).status(404).build()));
    }

    @POST
    @Path("/import")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.TEXT_PLAIN)
    @RolesAllowed({ "User", "Admin" })
    @WithSession
    public Uni<Response> importData() {
        return dataImporter.loadExcel()
                .onItem()
                .transformToUni(ignored -> Uni.createFrom().item(Response.ok("Import Data Jabatan Berhasil").build()))
                .onFailure().recoverWithItem(throwable -> Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                        .entity("Gagal Dalam Import Data Jabatan" + throwable.getMessage()).build());
    }

    @POST
    @Path("/search")
    @RolesAllowed({ "User", "Admin" })
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> searchJabatanByName(@FormParam("name") String keyword) {
        String name = "%" + keyword + "%";
        return jabatanRepository.searchName(name).onItem().transform(items -> Response.ok(items).build());
    }

}
