package com.iconplus.ap2t.secman.resource;

import com.iconplus.ap2t.common.param.auth.ModuleParam;
import com.iconplus.ap2t.common.response.MessageResult;
import com.iconplus.ap2t.data.entity.secman.Module;
import com.iconplus.ap2t.data.repository.secman.ModuleRepository;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.security.RolesAllowed;
import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import java.time.Instant;

@Path("/api/module")
@Produces(MediaType.APPLICATION_JSON)
@RequestScoped
@Tag(name = "Modul Aplik<PERSON>",description = "Pengaturan modul aplikasi")
public class ModuleResource {

    @Inject
    ModuleRepository moduleRepository;

    @GET
    @RolesAllowed({"User", "Admin"})
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> getAllModules() {
        MessageResult failed = new MessageResult(false, "Data Module Resource Tidak ada");

        return moduleRepository.listAll()
            .onItem().transform(modules -> {
                if (modules == null || modules.isEmpty()) {
                    return Response.status(404).entity(failed).build();
                } else {
                    return Response.ok(modules).build();
                }
            });
    }

    @GET
    @Path("/{id}")
    @RolesAllowed({"User", "Admin"})
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> getModuleById(@PathParam("id") Long id) {
        MessageResult failed = new MessageResult(false, "Data Module Resource Tidak ada");

        return moduleRepository.findById(id)
            .onItem().transform(module -> {
                if (module != null) {
                    return Response.ok(module).build();
                } else {
                    return Response.status(404).entity(failed).build();
                }
            });
    }

    @POST
    @Path("/store")
    @RolesAllowed({"User", "Admin"})
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<Response> saveModule(ModuleParam param) {
        // Validate input parameter
        if (param == null || param.name == null || param.name.isEmpty()) {
            return Uni.createFrom().item(Response.status(Response.Status.BAD_REQUEST)
                    .entity(new MessageResult(false, "Nama module tidak boleh kosong")).build());
        }
    
        // Create a new Module from the parameters
        Module module = new Module();
        module.name = param.name;
        module.description = param.description;
        module.hostname = param.hostname;
        module.urlPath = param.urlPath;
        module.IpService = param.IpService;
        module.port = param.port; // Ensure the correct type is used (String expected)
        module.enabled = param.enabled;
    
        // Prepare success and error message responses
        MessageResult successMessage = new MessageResult(true, "Module Berhasil ditambahkan");
        MessageResult errorMessage = new MessageResult(false, "Terjadi Kesalahan Server");
    
        // Use Panache transaction to persist the module and return appropriate response
        return Panache.withTransaction(() -> {
            return module.persist()
                    .onItem().transform(item -> item != null ? 
                        Response.status(Response.Status.CREATED).entity(successMessage).build() : 
                        Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(errorMessage).build());
        });
    }
    
    @PUT
    @Path("/{id}")
    @RolesAllowed({"User","Admin"})
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> updateModule(@PathParam("id") Long id,
                    ModuleParam param){
        MessageResult success = new MessageResult(true, "Update Module Resource Sukses!");
        MessageResult notFound = new MessageResult(false, "ID Module tidak ditemukan!");
        return moduleRepository.findById(id).onItem().ifNotNull().transformToUni(item->{
            item.name = param.name;
            item.description = param.description;
            item.hostname = param.hostname;
            item.IpService=param.IpService;
            item.port = param.port;
            item.urlPath = param.urlPath;
            item.updatedAt = Instant.now();

            return moduleRepository.persist(item)
                    .onItem().transform(module->Response.ok(module).build());
        }).onItem().ifNull().continueWith(Response.ok(notFound).build());
    }
    @DELETE
    @Path("/{id}")
    @RolesAllowed({"User", "Admin"})
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> deleteModule(@PathParam("id") Long id) {
    MessageResult success = new MessageResult(true, "Hapus Module Resource Sukses!");
    MessageResult notFound = new MessageResult(false, "Module Resource tidak ditemukan!");
        return Panache.withTransaction(() -> moduleRepository.findById(id)
        .onItem().ifNotNull().transformToUni(menuItem -> moduleRepository.deleteById(id)
        .replaceWith(Response.ok(success).build()))
    .onItem().ifNull().continueWith(() -> Response.status(Response.Status.NOT_FOUND).entity(notFound).build()));
    }

    @POST
    @Path("/search")
    @RolesAllowed({"User", "Admin"})
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    public Uni<Response> searchModuleByName(@FormParam("keyword") String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            // Jika keyword kosong, kembalikan respons gagal
            return Uni.createFrom().item(
                Response.status(Response.Status.BAD_REQUEST)
                        .entity(new MessageResult(false, "Keyword tidak boleh kosong!"))
                        .build()
            );
        }
    
        String namePattern = "%" + keyword+ "%"; // Pastikan keyword dalam huruf kecil
        return moduleRepository.searchName(namePattern)
                .onItem().transform(items -> {
                    if (items == null || items.isEmpty()) {
                        return Response.status(Response.Status.NOT_FOUND)
                                .entity(new MessageResult(false, "Tidak ada hasil untuk pencarian keyword: " + keyword))
                                .build();
                    }
                    return Response.ok(items).build();
                })
                .onFailure().recoverWithItem(throwable -> 
                    Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                            .entity(new MessageResult(false, "Terjadi kesalahan saat mencari data: " + throwable.getMessage()))
                            .build()
                );
    }
    
}

