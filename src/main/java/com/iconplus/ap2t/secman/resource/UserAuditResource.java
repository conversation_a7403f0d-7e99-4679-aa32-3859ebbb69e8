package com.iconplus.ap2t.secman.resource;

import com.iconplus.ap2t.common.param.auth.UserAuditParam;
import com.iconplus.ap2t.common.response.MessageResult;
import com.iconplus.ap2t.data.entity.secman.UserAudit;
import com.iconplus.ap2t.data.repository.secman.ModuleRepository;
import com.iconplus.ap2t.data.repository.secman.UserAuditRepository;
import com.iconplus.ap2t.data.repository.secman.UserRepository;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.security.RolesAllowed;
import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.jwt.Claims;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import java.time.Instant;
import java.util.UUID;

@Path("/api/user_audit")
@RequestScoped
@Tag(name = "Audit pengguna",description = "pencatatan aktivitas pengguna")
public class UserAuditResource {
    @Inject
    JsonWebToken jwt;

    @Inject
    UserAuditRepository userAuditRepository;

    @Inject
    UserRepository userRepository;

    @Inject
    ModuleRepository moduleRepository;

    @GET
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    @WithSession
    public Uni<Response> listAll(@QueryParam("userId") UUID userId,
                                        @QueryParam("page") @DefaultValue("0") Integer page,
                                        @QueryParam("size") @DefaultValue("20") Integer size) {
        MessageResult notFound = new MessageResult(false,"Data Belum Ada");
        if(userId==null){
             String strId = jwt.getClaim(Claims.upn);
             userId = UUID.fromString(strId);
            return userAuditRepository.findByUserId(userId,page,size)
                    .onItem().transform(items->Response.ok(items).build())
                    .onItem().ifNull().continueWith(Response.ok(notFound).status(404).build());
        } else {
            return userAuditRepository.findByUserId(userId,page,size)
                    .onItem().transform(items->Response.ok(items).build())
                    .onItem().ifNull().continueWith(Response.ok(notFound).status(404).build());
        }


    }

    @GET
    @Path("/{id}")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    @WithSession
    public Uni<Response> getUserAuditById(@PathParam("id") Long id) {
        MessageResult failed = new MessageResult(false, "Data Tidak di Temukan");
        return userAuditRepository.findById(id)
                .onItem().ifNotNull().transform(userAudit -> Response.ok(userAudit).build())
                .onItem().ifNull().continueWith(Response.ok(failed).status(404).build());
    }

    @GET
    @Path("activity/{userid}")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    @WithSession
    public Uni<Response> getUserAuditByUserId(@PathParam("userid") UUID userid,
                                              @QueryParam("page") @DefaultValue("0") Integer page,
                                              @QueryParam("size") @DefaultValue("20") Integer size) {
        MessageResult failed = new MessageResult(false, "Data Tidak di Temukan");
        return userAuditRepository.findByUserId(userid,page,size)
                .onItem().ifNotNull().transform(userAudit -> Response.ok(userAudit).build())
                .onItem().ifNull().continueWith(Response.ok(failed).status(404).build());
    }


    @POST
    @Path("/store")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Uni<Response> saveAudit(UserAuditParam param) {
        MessageResult failedUser = new MessageResult(false, "User_id Tidak di Temukan");
        MessageResult failedModule = new MessageResult(false, "Module_id Tidak di Temukan");
        MessageResult success = new MessageResult(true, "Audit berhasil disimpan");
        String userUuid = jwt.getClaim(Claims.upn);
        UUID userId = UUID.fromString(userUuid);
        return userRepository.findById(userId)
                .onItem().ifNotNull().transformToUni(user ->
                    moduleRepository.findById(param.moduleId)
                            .onItem().ifNotNull().transformToUni(module -> {
                                UserAudit audit = new UserAudit();
//                                audit.userId = user.id;
                                audit.activityName = param.activityName;
                                audit.ipAddress = param.ipAddress;
                                audit.moduleId = param.moduleId;
                                audit.urlPath = param.urlPath;
                                audit.userAgent = param.userAgent;
                                audit.userId = UUID.fromString(userUuid);
                                audit.activityTime = Instant.now();
                                return Panache.withTransaction(() -> UserAudit.persist(audit))
                                    .onItem().transform(item -> Response.ok(success).build());
                            })
                            .onItem().ifNull().continueWith(Response.status(404).entity(failedModule).build())
                )
                .onItem().ifNull().continueWith(Response.status(404).entity(failedUser).build());
    }


    @DELETE
    @Path("/{id}")
    @RolesAllowed({ "User", "Admin" })
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> deleteMenu(@PathParam("id") Long id) {
        MessageResult success = new MessageResult(true,"Hapus User Audit Sukses!");
        MessageResult failed = new MessageResult(false,"Data Tidak di Temukan");
        return Panache.withTransaction(() -> userAuditRepository.findById(id)
                .onItem().ifNotNull().transformToUni(item -> {
                    return UserAudit.deleteById(id)
                            .onItem().transform(baru -> Response.ok(success).build());
                }).onItem()
                .ifNull().continueWith(Response.ok(failed).status(404).build()));
    }

    @POST
    @Path("/search")
    @RolesAllowed({"User","Admin"})
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    public Uni<Response> searchJabatanByName(@FormParam("ip_address") String keyword){
        String ipAddress = "%"+keyword+"%";
        return userAuditRepository.searchName(ipAddress).onItem().transform(items->Response.ok(items).build());
    }
}
