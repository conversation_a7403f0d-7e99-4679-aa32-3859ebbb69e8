package com.iconplus.ap2t.secman.resource;

import com.iconplus.ap2t.common.param.auth.RoleModuleParam;
import com.iconplus.ap2t.common.response.MessageResult;
import com.iconplus.ap2t.data.entity.secman.RoleModule;
import com.iconplus.ap2t.data.repository.secman.RoleModuleRepository;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.security.RolesAllowed;
import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.jwt.Claims;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import java.time.Instant;
import java.util.UUID;

@Path("/api/module/role")
@RequestScoped
@Tag(name = "Role Module",description = "pengaturan peran per modul")
public class RoleModuleResource {
    @Inject
    JsonWebToken jwtService;
    @Inject
    RoleModuleRepository roleModuleRepository;

    @GET
    @Path("/{id}")
    @RolesAllowed({"User","Admin"})
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> getRoleModuleById(@PathParam("id") Long id) {
        MessageResult failed = new MessageResult(false, "Data Role Module Resource Tidak ada");

        return roleModuleRepository.findById(id)
                .onItem().ifNotNull().transform(roleModule -> Response.ok(roleModule).build())
                .onItem().ifNull().continueWith(() -> Response.status(Response.Status.NOT_FOUND).entity(failed).build());
    }
    @POST
    @Path("/store")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> postNewRoleModule(RoleModuleParam param){
        MessageResult success = new MessageResult(true, "Simpan Role Module Sukses!");
        RoleModule roleModule = new RoleModule();
        roleModule.moduleId = param.moduleId;
        roleModule.roleId = param.roleId;
        roleModule.granted = param.granted;
        String strUUID = jwtService.claim(Claims.upn).toString();
        UUID userUUID = UUID.fromString(strUUID);
        if(param.granted.equals("Y")) {
            roleModule.grantedAt = Instant.now();
            roleModule.grantedBy = userUUID;
        } else {
            roleModule.revokedAt = Instant.now();
            roleModule.revokedBy = userUUID;
        }

        return roleModuleRepository.persist(roleModule).onItem().transform(roleModule1 -> {
            return Response.ok(success).build();
        });
    }
    @POST
    @Path("/update/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> postNewRoleModule(@PathParam("id") Long id,
                                           RoleModuleParam param){
        MessageResult success = new MessageResult(true, "Simpan Role Module Sukses!");
        RoleModule roleModule = new RoleModule();
        roleModule.moduleId = param.moduleId;
        roleModule.roleId = param.roleId;
        roleModule.granted = param.granted;
        String strUUID = jwtService.claim(Claims.upn).toString();
        UUID userUUID = UUID.fromString(strUUID);
        if(param.granted.equals("Y")) {
            roleModule.grantedAt = Instant.now();
            roleModule.grantedBy = userUUID;
        } else {
            roleModule.revokedAt = Instant.now();
            roleModule.revokedBy = userUUID;
        }

        return roleModuleRepository.update(id,roleModule).onItem().transform(roleModule1 -> {
            return Response.ok(success).build();
        });
    }
    @DELETE
    @Path("/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> deleteRoleModule(@PathParam("id") Long id){
        MessageResult success = new MessageResult(true, "Hapus Role Module Sukses!");
        MessageResult failed = new MessageResult(false, "Hapus Role Module Gagal!");
        return roleModuleRepository.deleteById(id).onItem().transform(value->{
            if(value){
                return Response.ok(success).build();
            }
            return Response.ok(failed).build();
        });
    }
}
