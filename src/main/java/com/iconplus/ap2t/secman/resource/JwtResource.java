package com.iconplus.ap2t.secman.resource;

import com.iconplus.ap2t.common.param.auth.AccessToken;
import com.iconplus.ap2t.common.response.MessageResult;
import com.iconplus.ap2t.data.repository.secman.UserRepository;
import com.iconplus.ap2t.secman.service.Ap2tJwtService;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.security.PermitAll;
import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.jwt.Claims;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;

@Path("/jwt/")
@RequestScoped
@Tag(name = "Token",description = "Pengaturan Token Web")
public class JwtResource {
    static Logger LOG = LoggerFactory.getLogger(JwtResource.class);
    @Inject
    Ap2tJwtService service;
    @Inject
    UserRepository userRepository;
    @GET
    @PermitAll
    @Produces(MediaType.APPLICATION_JSON)
    public Response getJwt(){
        AccessToken jwt = service.generateJwt();
        return Response.ok(jwt).build();
    }
    @POST
    @Path("/check")
    @PermitAll
    @Consumes(MediaType.TEXT_PLAIN)
    @Produces(MediaType.APPLICATION_JSON)
    public Uni<Response> checkToken(String token){
        LOG.info("Cek Token : {}",token);
        JsonWebToken jwt = service.parseToken(token);
        Instant expired = Instant.ofEpochMilli(jwt.getExpirationTime());
        Instant sekarang = Instant.now();
        Date expired1 = new Date(jwt.getExpirationTime());
        MessageResult failed = new MessageResult(false,"Token Not Valid!");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("d-MM-Y HH:mm:ss");
        LOG.info("token expired : {}",simpleDateFormat.format(expired1));
        if(service.isTokenValid(token) && expired.isAfter(sekarang)){
            LOG.info("Token Valid ");
            String email = jwt.getClaim(Claims.email);
            MessageResult succeed = new MessageResult(true,"Token Valid!");
            return userRepository
                    .findByEmail(email).onItem().ifNotNull().transform(user->{
                        succeed.data = user;
                        return Response.ok(succeed).build();
                    })
                    .onItem().ifNull().continueWith(Response.ok(failed).status(403).build());

        }

        return Uni.createFrom().item(Response.ok(failed).status(403).build());
    }
}
