package com.iconplus.ap2t.secman.dto;

import com.iconplus.ap2t.data.entity.secman.User;

public class User2User {
    public static com.iconplus.ap2t.common.param.secman.User fromUserEntity(User user){
        com.iconplus.ap2t.common.param.secman.User user2 = new com.iconplus.ap2t.common.param.secman.User();
        user2.name = user.name;
        user2.email = user.email;
        user2.activationCode = user.activationCode;
        user2.avatar = user.avatar;

        return user2;
    }
}
