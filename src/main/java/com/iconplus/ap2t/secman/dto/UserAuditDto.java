package com.iconplus.ap2t.secman.dto;

import java.time.Instant;
import java.util.UUID;

public class UserAuditDto {
    private UUID id;
    private UUID userId;
    private Instant loginDate;
    private Instant logoutDate;
    private String ipAddress;
    private Long moduleId;

    public UserAuditDto(UUID id, UUID userId, Instant loginDate, Instant logoutDate, String ipAddress, Long moduleId) {
        this.id = id;
        this.userId = userId;
        this.loginDate = loginDate;
        this.logoutDate = logoutDate;
        this.ipAddress = ipAddress;
        this.moduleId = moduleId;
    }

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getUserId() {
        return userId;
    }

    public void setUserId(UUID userId) {
        this.userId = userId;
    }


    public Instant getLoginDate() {
        return loginDate;
    }

    public void setLoginDate(Instant loginDate) {
        this.loginDate = loginDate;
    }

    public Instant getLogoutDate() {
        return logoutDate;
    }

    public void setLogoutDate(Instant logoutDate) {
        this.logoutDate = logoutDate;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public Long getModuleId() {
        return moduleId;
    }

    public void setModuleId(Long moduleId) {
        this.moduleId = moduleId;
    }
}