package com.iconplus.ap2t.secman.service;

import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.UUID;

@ApplicationScoped
public class UploadService {
    final Logger LOGGER = LoggerFactory.getLogger(UploadService.class);
    @ConfigProperty(name = "upload.path")
    String basePath;
    public Uni<String> uploadFile(InputStream inputStream){
        try {
            if(inputStream==null) return null;
            String currentDirectory = System.getProperty("user.dir");
            // String uploadDir = currentDirectory+ File.separator+"uploads";
            String fileName = UUID.randomUUID().toString();
            String filePath = basePath+File.separator+fileName+".bin";

            File output = new File(filePath);
            FileOutputStream outputStream = new FileOutputStream(output);
            outputStream.write(inputStream.readAllBytes());
            outputStream.close();
            String mimeType = Files.probeContentType(output.toPath());
            LOGGER.info("Mime Type : {}",mimeType);
            String[] extensions = mimeType.split("/");
            String newPath = basePath+File.separator+fileName+".jpg";
            File newFile = new File(newPath);
            output.renameTo(newFile);
            return Uni.createFrom().item(newFile.getName());
        } catch (IOException ex){
            return Uni.createFrom().nullItem();
        }

    }
}
