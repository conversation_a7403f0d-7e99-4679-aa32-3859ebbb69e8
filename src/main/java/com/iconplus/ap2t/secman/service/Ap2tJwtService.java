package com.iconplus.ap2t.secman.service;

import com.iconplus.ap2t.common.param.auth.AccessToken;
import com.iconplus.ap2t.data.entity.secman.User;
import io.smallrye.jwt.auth.principal.JWTParser;
import io.smallrye.jwt.auth.principal.ParseException;
import io.smallrye.jwt.build.Jwt;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.apache.commons.lang3.time.DateUtils;
import org.eclipse.microprofile.jwt.Claims;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

@ApplicationScoped
public class Ap2tJwtService {
    final static Logger LOG = LoggerFactory.getLogger(Ap2tJwtService.class);
    @Inject
    JWTParser parser;

    public AccessToken generateJwt(){
        Set<String> roles = new HashSet<>(
                Arrays.asList("admin","writer")
        );
        Date instant = DateUtils.addDays(new Date(),3);
        Long duration = instant.getTime();
        String token= Jwt.issuer("iconpln-ap2t-web")
                .subject("application-token")
                .groups(roles)
                .expiresAt(duration)
                .sign();
        return new AccessToken(token,duration);
    }
    public AccessToken generateUserToken(User user){

        Set<String> roles = new HashSet<>();
        if(user.jenisPp.equals("PUSAT")){
            roles.add("Admin");
        } else {
            roles.add("User");
        }
        LOG.info("user : {}",user.toString());
        LOG.info("roles : {}",roles);
        ZoneId zone = ZoneId.of("Asia/Jakarta");
        Instant now = Instant.now();
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(now, zone);

        // Add one minute
        ZonedDateTime expiredDateZone = zonedDateTime.plus(3, ChronoUnit.DAYS);

        // Convert back to instant if needed
        Instant expiredDate = expiredDateZone.toInstant();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss z");
        LOG.info("Token Expired at : {}",expiredDateZone.format(formatter));
        Long expiredAt = expiredDate.toEpochMilli();
        String token= Jwt.issuer("iconpln-ap2t-web")
                .groups(roles)
                .upn(user.id.toString())
                .claim(Claims.email,user.email)
                .claim(Claims.groups,"User")
                .claim(Claims.full_name,user.name)
                .claim(Claims.upn,user.id)
                .expiresAt(expiredAt)
                .sign();
        return new AccessToken(token,expiredAt);
    }
    public Boolean isTokenValid(String token){
        try {
            JsonWebToken jwt = parser.parse(token);
            if(jwt!=null){
                Instant jatime = Instant.ofEpochMilli(jwt.getExpirationTime());
                ZoneId zone = ZoneId.of("Asia/Jakarta");
                ZonedDateTime expiredAt = ZonedDateTime.ofInstant(jatime, zone);
                ZonedDateTime now = ZonedDateTime.now(zone);

                return expiredAt.isAfter(now);
            }
            return false;
//            return jwt != null && !jwt.getExpirationTime();
        } catch (ParseException e) {
            return false;
        }
    }
    public JsonWebToken parseToken(String token){
        try {
            JsonWebToken jwt = parser.parse(token);

            return jwt;
//            return jwt != null && !jwt.getExpirationTime();
        } catch (ParseException e) {
            return null;
        }
    }
    public String generateToken(Integer charLength) {
        return String.valueOf(charLength < 1 ? 0 : new Random()
                .nextInt((9 * (int) Math.pow(10, charLength - 1)) - 1)
                + (int) Math.pow(10, charLength - 1));
    }
}
