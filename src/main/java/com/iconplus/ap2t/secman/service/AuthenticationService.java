package com.iconplus.ap2t.secman.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iconplus.ap2t.common.param.auth.AccessToken;
import com.iconplus.ap2t.data.entity.secman.User;
import com.iconplus.ap2t.data.repository.secman.UserRepository;
import com.iconplus.ap2t.secman.result.AuthResult;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.core.NewCookie;
import org.eclipse.microprofile.jwt.Claims;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class AuthenticationService {
    static Logger LOG = LoggerFactory.getLogger(AuthenticationService.class);
    @Inject
    JsonWebToken jwt;
    @Inject
    UserRepository userRepository;
    private User currentUser;
    private AuthResult authResult;
    private Boolean isAuthenticated = false;
    private AuthenticationService instance;
    public AuthenticationService(){
        if(instance==null) {
            instance = this;
        }
    }
    public AuthenticationService getInstance(){
        return instance;
    }
    public Uni<Boolean> init(){
        if(jwt!=null){
            String email = jwt.getClaim(Claims.email);

            return userRepository.findByEmail(email).onItem().transform(user->{
                currentUser = user;
                isAuthenticated = true;
                return true;
            });
        }
        return Uni.createFrom().item(false);
    }
    public NewCookie saveUserToCookie(AuthResult result){
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            String userInfo = objectMapper.writeValueAsString(result);
            NewCookie userCookie = new NewCookie("user_token",userInfo,"/","","JWT Cookie",86400,false,true);
            return userCookie;
        } catch (JsonProcessingException ex){
            return null;
        }

    }
    public Boolean IsAuthenticated(){
        return isAuthenticated;
    }
    public void setResult(AuthResult result){
        this.authResult=result;
    }
    public AuthResult getUserInfo(String cookies){
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            AuthResult result = objectMapper.readValue(cookies,AuthResult.class);
            return result;

        } catch (JsonProcessingException ex){
            return null;
        }
    }
    public AuthResult getAuthResult() {
        return authResult;
    }

    public void setAuthResult(AuthResult authResult) {
        this.authResult = authResult;
    }
    public User getCurrentUser() {
        return currentUser;
    }
    public AccessToken getToken(){
        return authResult.token;
    }
    public NewCookie cookieLogout(){
        NewCookie userCookie = new NewCookie("user_token","","/","","JWT Cookie",0,false,true);
        return userCookie;
    }
}
