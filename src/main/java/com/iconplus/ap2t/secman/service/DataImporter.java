package com.iconplus.ap2t.secman.service;

import com.iconplus.ap2t.data.repository.secman.*;
import com.iconplus.ap2t.data.entity.secman.*;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Singleton
public class DataImporter {
    private static final Logger LOGGER = LoggerFactory.getLogger(DataImporter.class.getName());
    @Inject
    UserLevelRepository userLevelRepository;

    @Inject
    JabatanRepository jabatanRepository;

    private static final LocalDate EXCEL_EPOCH = LocalDate.of(1899, 12, 30);
    public static Date convertNumericDate(double numericDate) {
        // Convert numeric date to LocalDate
        LocalDate localDate = EXCEL_EPOCH.plusDays((long) numericDate);
        // Convert LocalDate to Date
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }
    @WithTransaction
    public Uni<Void> loadExcel(){
        LOGGER.info("Loading Excel ...");
        return Uni.createFrom().item(()->{
            List<Jabatan> jabatans = new ArrayList<>();
            try {

                String currentDirectory = System.getProperty("user.dir");
                String filePath = currentDirectory+ File.separator+"data"+File.separator+"jabatan.xlsx";
                LOGGER.info(String.format("File Path : %s",filePath));
                Workbook workbook = WorkbookFactory.create(new File(filePath));
                Sheet sheet = workbook.getSheetAt(0);
                int rowCount = sheet.getLastRowNum();
                LOGGER.info(String.format("Jumlah rows : %d",rowCount));

                StringBuilder stringBuilder = new StringBuilder();
                int cellNum = sheet.getRow(0).getLastCellNum();
                LOGGER.info(String.format("Jumlah kolom : %d",cellNum));
                for(int i=1;i<rowCount;i++){
                    Row row = sheet.getRow(i);
                    Jabatan jabatan = new Jabatan();
                    jabatan.kode = row.getCell(0).getStringCellValue();
//                    LOGGER.info("kode : {}",jabatan.kode);
                    jabatan.name = row.getCell(1).getStringCellValue();
                    jabatan.level = row.getCell(2).getStringCellValue();
                    jabatan.layerUnit = row.getCell(3).getStringCellValue();

                    jabatan.status = row.getCell(4).getStringCellValue();
                    Date dateCreated = convertNumericDate(row.getCell(6).getNumericCellValue());
                    jabatan.createdAt = dateCreated.toInstant();
                    jabatan.updatedAt = dateCreated.toInstant();
                    jabatans.add(jabatan);
                }
            } catch (Exception ex){
                throw new RuntimeException("Error reading Excel file", ex);
            }
            return jabatans;
        }).onItem().transformToUni(jabatans->jabatanRepository.persist(jabatans));

    }
}
