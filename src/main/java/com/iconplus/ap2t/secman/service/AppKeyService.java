package com.iconplus.ap2t.secman.service;

import jakarta.enterprise.context.ApplicationScoped;

import java.util.Random;
import java.util.UUID;

@ApplicationScoped
public class AppKeyService {
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
//    private static final String NUMBERS = "0123456789";
//    private static final Random SECURE_RANDOM = new Random();
    public static String generateRandomString(int length) {
        Random SECURE_RANDOM = new Random();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = SECURE_RANDOM.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(index));
        }
        return sb.toString();
    }
    public static String generateToken(int length) {
        String NUMBERS = "0123456789";
        Random SECURE_RANDOM = new Random();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = SECURE_RANDOM.nextInt(NUMBERS.length());
            sb.append(NUMBERS.charAt(index));
        }
        return sb.toString();
    }
    public static String generateKey(){
        return UUID.randomUUID().toString().toUpperCase();
    }
    public static String generateSecretKey(){
        return generateRandomString(128);
    }
}
