package com.iconplus.ap2t.secman.service;

import com.iconplus.ap2t.data.entity.secman.User;
import io.quarkus.mailer.Mail;
import io.quarkus.mailer.reactive.ReactiveMailer;
import io.quarkus.qute.Template;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.hibernate.reactive.mutiny.Mutiny;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ApplicationScoped
public class MailServerService {
    Logger LOG = LoggerFactory.getLogger(MailServerService.class);
    @Inject
    ReactiveMailer mailer;
    @Inject
    Template register_notification;
    @Inject
    Template tfa_notification;
    @Inject
    Template approval_status;
    @Inject
    Mutiny.SessionFactory sessionFactory;
    public Uni<Void> sendRegisterNotification(User user){
        String htmlContent = register_notification.data("user",user).render();
        return mailer.send(
                Mail.withHtml(user.email,"Pendaftaran Pengguna Baru - New AP2T",htmlContent)
                        .setFrom("<EMAIL>")
        );
    }
    public Uni<Void> sendApprovalMail(User user){
        String htmlContent = approval_status.data("status",user.statusApproval).render();
        return mailer.send(
                Mail.withHtml(user.email,"Persetujuan Pendaftaran Pengguna - New AP2T",htmlContent)
                        .setFrom("<EMAIL>")
        );
    }
    public Uni<Void> sendTFANotification(User user){
        String htmlContent = tfa_notification.data("user", user).render();
        // Asynchronously send email using mailer
        return mailer.send(Mail.withHtml(user.email, "Two Factor Authentication - New AP2T Login", htmlContent)
                .setFrom("<EMAIL>")).replaceWithVoid();
    }
    public Uni<Boolean> testEmail(String email,String subject,String body){

        return mailer.send(Mail.withText(email,subject,body).setFrom("<EMAIL>"))
                .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                .onItem().transform(sent->{
                    LOG.info("Email Sent");
                    return Uni.createFrom().item(true);
                }).onFailure().invoke((e)->{
                    LOG.error("Error : {}",e.getMessage());
                }).replaceWith(false);
    }
}
