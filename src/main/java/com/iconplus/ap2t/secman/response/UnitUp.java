package com.iconplus.ap2t.secman.response;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

@RegisterForReflection
public class UnitUp {
    @JsonProperty("id")
    private Integer id;
    @JsonProperty("unit_up")
    private String unitUp;
    @JsonProperty("satuan")
    private String satuan;
    @JsonProperty("nama")
    private String nama;
    @JsonProperty("alamat")
    private String alamat;
    @JsonProperty("telepon")
    private String telepon;
    @JsonProperty("faximile")
    private String faximile;
    @JsonProperty("manager")
    private String manager;
    @JsonProperty("kota")
    private String kota;
    @JsonProperty("unit_ap")
    private String unitAp;
    @JsonProperty("nama_singkat")
    private String namaSingkat;

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("unit_up")
    public String getUnitUp() {
        return unitUp;
    }

    @JsonProperty("unit_up")
    public void setUnitUp(String unitUp) {
        this.unitUp = unitUp;
    }

    @JsonProperty("satuan")
    public String getSatuan() {
        return satuan;
    }

    @JsonProperty("satuan")
    public void setSatuan(String satuan) {
        this.satuan = satuan;
    }

    @JsonProperty("nama")
    public String getNama() {
        return nama;
    }

    @JsonProperty("nama")
    public void setNama(String nama) {
        this.nama = nama;
    }

    @JsonProperty("alamat")
    public String getAlamat() {
        return alamat;
    }

    @JsonProperty("alamat")
    public void setAlamat(String alamat) {
        this.alamat = alamat;
    }

    @JsonProperty("telepon")
    public String getTelepon() {
        return telepon;
    }

    @JsonProperty("telepon")
    public void setTelepon(String telepon) {
        this.telepon = telepon;
    }

    @JsonProperty("faximile")
    public String getFaximile() {
        return faximile;
    }

    @JsonProperty("faximile")
    public void setFaximile(String faximile) {
        this.faximile = faximile;
    }

    @JsonProperty("manager")
    public String getManager() {
        return manager;
    }

    @JsonProperty("manager")
    public void setManager(String manager) {
        this.manager = manager;
    }

    @JsonProperty("kota")
    public String getKota() {
        return kota;
    }

    @JsonProperty("kota")
    public void setKota(String kota) {
        this.kota = kota;
    }

    @JsonProperty("unit_ap")
    public String getUnitAp() {
        return unitAp;
    }

    @JsonProperty("unit_ap")
    public void setUnitAp(String unitAp) {
        this.unitAp = unitAp;
    }

    @JsonProperty("nama_singkat")
    public String getNamaSingkat() {
        return namaSingkat;
    }

    @JsonProperty("nama_singkat")
    public void setNamaSingkat(String namaSingkat) {
        this.namaSingkat = namaSingkat;
    }

}