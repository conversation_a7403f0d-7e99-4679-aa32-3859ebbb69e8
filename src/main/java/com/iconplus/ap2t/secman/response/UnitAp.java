package com.iconplus.ap2t.secman.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

@RegisterForReflection
public class UnitAp {
    private int id;
    @JsonProperty("unit_ap")
    private String unitap;
    private String satuan;
    private String nama;
    private String alamat;
    private String telepon;
    private String faximile;
    private String manager;
    private String kota;
    @JsonProperty("unit_upi")
    private String unitupi;

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUnitap() {
        return unitap;
    }

    public void setUnitap(String unitap) {
        this.unitap = unitap;
    }

    public String getSatuan() {
        return satuan;
    }

    public void setSatuan(String satuan) {
        this.satuan = satuan;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public String getAlamat() {
        return alamat;
    }

    public void setAlamat(String alamat) {
        this.alamat = alamat;
    }

    public String getTelepon() {
        return telepon;
    }

    public void setTelepon(String telepon) {
        this.telepon = telepon;
    }

    public String getFaximile() {
        return faximile;
    }

    public void setFaximile(String faximile) {
        this.faximile = faximile;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public String getKota() {
        return kota;
    }

    public void setKota(String kota) {
        this.kota = kota;
    }

    public String getUnitupi() {
        return unitupi;
    }

    public void setUnitupi(String unitupi) {
        this.unitupi = unitupi;
    }
}
