package com.iconplus.ap2t.secman.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

import java.io.Serializable;
import java.util.List;

@RegisterForReflection
public class AuthenticationResponse implements Serializable {

    @JsonProperty("dataUser")
    private List<Data> data;

    @JsonProperty("dataRole")
    private List<Role> roles;

    public Data getData() {
        return !data.isEmpty() ? data.getFirst() : new Data();
    }

    public void setData(List<Data> data) {
        this.data = data;
    }

    public List<Role> getRoles() {
        return roles;
    }

    @RegisterForReflection
    public static class Data {
        private String tglmasaaktif;
        private String kode_jabatan;
        private String id_user;
        private String unitap_user;
        private String keterangan_jabatan;
        private String alamat;
        private String notelp;
        private String unit;
        private String kode_departemen;
        private String nip;
        private String nama;
        private String tgllahir;
        private String keterangan_status_pegawai;
        private String nama_unit_user;
        private String leveluser;
        private String unitupi_user;
        private String status_user;
        private String kode_status_pegawai;
        private String unitup_user;
        private String email;
        private String keterangan_departemen;

        public String getTglmasaaktif() {
            return tglmasaaktif;
        }

        public void setTglmasaaktif(String tglmasaaktif) {
            this.tglmasaaktif = tglmasaaktif;
        }

        public String getKode_jabatan() {
            return kode_jabatan;
        }

        public void setKode_jabatan(String kode_jabatan) {
            this.kode_jabatan = kode_jabatan;
        }

        public String getId_user() {
            return id_user;
        }

        public void setId_user(String id_user) {
            this.id_user = id_user;
        }

        public String getUnitap_user() {
            return unitap_user;
        }

        public void setUnitap_user(String unitap_user) {
            this.unitap_user = unitap_user;
        }

        public String getKeterangan_jabatan() {
            return keterangan_jabatan;
        }

        public void setKeterangan_jabatan(String keterangan_jabatan) {
            this.keterangan_jabatan = keterangan_jabatan;
        }

        public String getAlamat() {
            return alamat;
        }

        public void setAlamat(String alamat) {
            this.alamat = alamat;
        }

        public String getNotelp() {
            return notelp;
        }

        public void setNotelp(String notelp) {
            this.notelp = notelp;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public String getKode_departemen() {
            return kode_departemen;
        }

        public void setKode_departemen(String kode_departemen) {
            this.kode_departemen = kode_departemen;
        }

        public String getNip() {
            return nip;
        }

        public void setNip(String nip) {
            this.nip = nip;
        }

        public String getNama() {
            return nama;
        }

        public void setNama(String nama) {
            this.nama = nama;
        }

        public String getTgllahir() {
            return tgllahir;
        }

        public void setTgllahir(String tgllahir) {
            this.tgllahir = tgllahir;
        }

        public String getKeterangan_status_pegawai() {
            return keterangan_status_pegawai;
        }

        public void setKeterangan_status_pegawai(String keterangan_status_pegawai) {
            this.keterangan_status_pegawai = keterangan_status_pegawai;
        }

        public String getNama_unit_user() {
            return nama_unit_user;
        }

        public void setNama_unit_user(String nama_unit_user) {
            this.nama_unit_user = nama_unit_user;
        }

        public String getLeveluser() {
            return leveluser;
        }

        public void setLeveluser(String leveluser) {
            this.leveluser = leveluser;
        }

        public String getUnitupi_user() {
            return unitupi_user;
        }

        public void setUnitupi_user(String unitupi_user) {
            this.unitupi_user = unitupi_user;
        }

        public String getStatus_user() {
            return status_user;
        }

        public void setStatus_user(String status_user) {
            this.status_user = status_user;
        }

        public String getKode_status_pegawai() {
            return kode_status_pegawai;
        }

        public void setKode_status_pegawai(String kode_status_pegawai) {
            this.kode_status_pegawai = kode_status_pegawai;
        }

        public String getUnitup_user() {
            return unitup_user;
        }

        public void setUnitup_user(String unitup_user) {
            this.unitup_user = unitup_user;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getKeterangan_departemen() {
            return keterangan_departemen;
        }

        public void setKeterangan_departemen(String keterangan_departemen) {
            this.keterangan_departemen = keterangan_departemen;
        }
    }

    public static class Role {
        private String id_group;

        public String getId_group() {
            return id_group;
        }

        public void setId_group(String id_group) {
            this.id_group = id_group;
        }
    }
}
