package com.iconplus.ap2t.secman.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

@RegisterForReflection
public class UnitUpi {
    @JsonProperty("id")
    private Integer id;
    @JsonProperty("kode")
    private String kode;
    @JsonProperty("satuan")
    private String satuan;
    @JsonProperty("nama")
    private String nama;
    @JsonProperty("alamat")
    private String alamat;
    @JsonProperty("telepon")
    private String telepon;
    @JsonProperty("faximile")
    private String faximile;
    @JsonProperty("manager")
    private String manager;
    @JsonProperty("kota")
    private String kota;
    @JsonProperty("unitupierp")
    private String unitupierp;
    
    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("kode")
    public String getKode() {
        return kode;
    }

    @JsonProperty("kode")
    public void setKode(String kode) {
        this.kode = kode;
    }

    @JsonProperty("satuan")
    public String getSatuan() {
        return satuan;
    }

    @JsonProperty("satuan")
    public void setSatuan(String satuan) {
        this.satuan = satuan;
    }

    @JsonProperty("nama")
    public String getNama() {
        return nama;
    }

    @JsonProperty("nama")
    public void setNama(String nama) {
        this.nama = nama;
    }

    @JsonProperty("alamat")
    public String getAlamat() {
        return alamat;
    }

    @JsonProperty("alamat")
    public void setAlamat(String alamat) {
        this.alamat = alamat;
    }

    @JsonProperty("telepon")
    public String getTelepon() {
        return telepon;
    }

    @JsonProperty("telepon")
    public void setTelepon(String telepon) {
        this.telepon = telepon;
    }

    @JsonProperty("faximile")
    public String getFaximile() {
        return faximile;
    }

    @JsonProperty("faximile")
    public void setFaximile(String faximile) {
        this.faximile = faximile;
    }

    @JsonProperty("manager")
    public String getManager() {
        return manager;
    }

    @JsonProperty("manager")
    public void setManager(String manager) {
        this.manager = manager;
    }

    @JsonProperty("kota")
    public String getKota() {
        return kota;
    }

    @JsonProperty("kota")
    public void setKota(String kota) {
        this.kota = kota;
    }

    @JsonProperty("unitupierp")
    public String getUnitupierp() {
        return unitupierp;
    }

    @JsonProperty("unitupierp")
    public void setUnitupierp(String unitupierp) {
        this.unitupierp = unitupierp;
    }


}
