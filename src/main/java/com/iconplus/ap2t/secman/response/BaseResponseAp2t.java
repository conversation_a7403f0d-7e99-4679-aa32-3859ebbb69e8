package com.iconplus.ap2t.secman.response;

import io.quarkus.runtime.annotations.RegisterForReflection;

@RegisterForReflection
public class BaseResponseAp2t<T> {
    public Integer status;
    public Boolean success;
    public String message;
    public T data;

    public BaseResponseAp2t() {
    }

    public BaseResponseAp2t(Integer status, String message) {
        this.success = false;
        this.status = status;
        this.message = message;
    }
}
