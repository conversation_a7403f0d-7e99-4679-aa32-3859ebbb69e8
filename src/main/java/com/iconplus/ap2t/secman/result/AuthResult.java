package com.iconplus.ap2t.secman.result;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.iconplus.ap2t.common.param.auth.AccessToken;
import com.iconplus.ap2t.data.entity.secman.User;
import io.quarkus.runtime.annotations.RegisterForReflection;

@RegisterForReflection
public class AuthResult {
    @JsonProperty("user")
    public User user;
    @JsonProperty("token")
    public AccessToken token;
    public AuthResult(){}

    public AuthResult(User user, AccessToken accessToken) {
        this.user = user;
        this.token = accessToken;
    }
}
