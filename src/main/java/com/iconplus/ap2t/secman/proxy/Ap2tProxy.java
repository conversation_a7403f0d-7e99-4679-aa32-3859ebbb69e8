package com.iconplus.ap2t.secman.proxy;

import com.iconplus.ap2t.secman.response.AuthenticationResponse;
import com.iconplus.ap2t.secman.response.BaseResponseAp2t;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.annotation.ClientHeaderParam;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

@RegisterRestClient(configKey = "ap2t.proxy")
@ClientHeaderParam(name = "X-API-KEY", value = "${ap2t.proxy/mp-rest/key}")
@Produces(MediaType.APPLICATION_JSON)
public interface Ap2tProxy {

    @GET
    @Path("/validasi/user")
    Uni<BaseResponseAp2t<AuthenticationResponse>> validasiUser(
        @QueryParam("username") String username,
        @QueryParam("password") String password
    );
}
