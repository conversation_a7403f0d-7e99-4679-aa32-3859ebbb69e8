package com.iconplus.ap2t.secman.proxy;

import com.iconplus.ap2t.secman.response.UnitAp;
import com.iconplus.ap2t.secman.response.UnitUp;
import com.iconplus.ap2t.secman.response.UnitUpi;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import java.util.List;

@RegisterRestClient(configKey = "master.proxy")
@Path("/api")
@Produces(MediaType.APPLICATION_JSON)
public interface MasterProxy {
    @GET
    @Path("/ap")
    List<UnitAp> getListAP();
    @GET
    @Path("/ap/upi/{kode}")
    List<UnitAp> getAPbyUPI(@PathParam("kode") String kode);
    @GET
    @Path("/up")
    List<UnitUp> getListUP();
    @GET
    @Path("/up/ap/{kode}")
    List<UnitUp> getUpByAp(@PathParam("kode") String kode);
    @GET
    @Path("/upi")
    List<UnitUpi> getListUpi();
}
