package com.iconplus.ap2t.secman.token;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.iconplus.ap2t.common.param.secman.User;
import io.quarkus.runtime.annotations.RegisterForReflection;

@RegisterForReflection
public class TokenResult{

	@JsonProperty("data")
	private User user;

	@JsonProperty("message")
	private String message;

	@JsonProperty("status")
	private boolean status;

	public User getData(){
		return user;
	}

	public String getMessage(){
		return message;
	}

	public boolean isStatus(){
		return status;
	}
}