quarkus.smallrye-openapi.info-title=Billing ModuleAPI
%dev.quarkus.smallrye-openapi.info-title=Billing Module API (development)
%test.quarkus.smallrye-openapi.info-title=Billing Module API (test)
quarkus.smallrye-openapi.info-version=1.0.1
quarkus.smallrye-openapi.info-description=Billing Module Service
quarkus.smallrye-openapi.info-terms-of-service=This is private application exclusively for PLN internal application only
quarkus.smallrye-openapi.info-contact-email=<EMAIL>
quarkus.smallrye-openapi.info-contact-name=Billing Engine API Support
quarkus.smallrye-openapi.info-contact-url=http://iconpln.co.id/contact
quarkus.smallrye-openapi.info-license-name=Apache 2.0
quarkus.smallrye-openapi.info-license-url=https://www.apache.org/licenses/LICENSE-2.0.html
quarkus.swagger-ui.always-include=true
#quarkus.http.http2=true
quarkus.swagger-ui.path=/api-doc
quarkus.http.port=${HOST_PORT:8080}
quarkus.http.host=${HOST_NAME:localhost}
quarkus.http.static-resources.index-page=index.html
quarkus.http.static-resources.caching-enabled=true
quarkus.http.cors=true
%prod.quarkus.http.cors.origins=/.*/
%dev.quarkus.http.cors.origins=/.*/
quarkus.http.cors.headers=accept, authorization, content-type, x-requested-with
quarkus.http.cors.methods=GET,POST, OPTIONS,PUT,DELETE
quarkus.datasource.db-kind = postgresql
#%prod.quarkus.datasource.username = dev_ap2t
#%prod.quarkus.datasource.password = d3v_4p2t2024
# %dev.quarkus.datasource.username = dev_ap2t
# %dev.quarkus.datasource.password = d3v_4p2t2024
#%prod.quarkus.datasource.jdbc.url = ***************************************
quarkus.datasource.username = ${QUARKUS_DATASOURCE_USERNAME:postgres}
quarkus.datasource.password = ${QUARKUS_DATASOURCE_PASSWORD:postgres}
quarkus.datasource.jdbc.url = ${QUARKUS_DATASOURCE_JDBC_URL:*************************************}
quarkus.datasource.jdbc=false
quarkus.datasource.reactive=true
quarkus.datasource.reactive.url=${DB_REACTIVE_URL:postgresql://localhost:5432/ap2t}
#%prod.quarkus.datasource.reactive.url=vertx-reactive:postgresql://***********:5432/db55
%dev.quarkus.datasource.reactive.max-size=20
%prod.quarkus.datasource.reactive.max-size=20
quarkus.hibernate-orm.log.sql=false
quarkus.thread-pool.core-threads=8
quarkus.thread-pool.max-threads=16
quarkus.thread-pool.queue-size=128
quarkus.http.enable-compression=true
# drop and create the database at startup (use `update` to only update the schema)
#%prod.quarkus.hibernate-orm.database.generation = none
#%dev.quarkus.hibernate-orm.database.generation = update
#%prod.quarkus.hibernate-orm.database.generation = none
#%dev.quarkus.hibernate-orm.database.generation = update
quarkus.hibernate-orm.validate-in-dev-mode=false
quarkus.hibernate-orm.database.generation.create-schemas=false
quarkus.index-dependency.spring-data-jpa.group-id=org.springframework.data
quarkus.index-dependency.spring-data-jpa.artifact-id=spring-data-jpa
quarkus.log.sentry=false
quarkus.log.sentry.dsn=http://6e77ef478cdda66a7f94c2ab9fff0ba3@10.1.50.165:9000/4
quarkus.log.sentry.traces-sample-rate=1.0
#rest client / microprofile
crm.proxy/mp-rest/url=${CRM_API}
crm.proxy/mp-rest/readTimeout=${READ_TIMEOUT}
crm.proxy/mp-rest/connectTimeout=${CONNECT_TIMEOUT}
master.proxy/mp-rest/url=${MASTER_API}
master.proxy/mp-rest/readTimeout=${READ_TIMEOUT}
master.proxy/mp-rest/connectTimeout=${CONNECT_TIMEOUT}
engine.proxy/mp-rest/url=${ENGINE_API}
engine.proxy/mp-rest/readTimeout=${READ_TIMEOUT}
engine.proxy/mp-rest/connectTimeout=${CONNECT_TIMEOUT}
workflow.proxy/mp-rest/url=${WORKFLOW_API}
workflow.proxy/mp-rest/readTimeout=${READ_TIMEOUT}
workflow.proxy/mp-rest/connectTimeout=${CONNECT_TIMEOUT}
acmt.proxy/mp-rest/url=${ACMT_BACA_ULANG_API}
acmt.proxy/mp-rest/readTimeout=${READ_TIMEOUT}
acmt.proxy/mp-rest/connectTimeout=${CONNECT_TIMEOUT}
quarkus.log.category."org.hibernate.reactive".level=DEBUG
quarkus.log.category."io.smallrye.mutiny".level=DEBUG
# HTTP server max connections
quarkus.http.limits.max-connections=20000
# Max requests per connection (useful for HTTP/1.1 keep-alive)
#quarkus.http.limits.max-request-size=10M
# Increase the number of IO threads (defaults to core count / 2)
quarkus.http.io-threads=16
# Increase worker threads (used for blocking tasks like database operations)
#quarkus.http.worker-threads=2000
#quarkus.vertx.worker-pool-size=100
quarkus.vertx.event-loops-pool-size=200
quarkus.quartz.thread-count=10
quarkus.quartz.batch-trigger-acquisition-max-count=100
quarkus.quartz.select-with-lock-sql=true
quarkus.quartz.thread-priority=5
# Database connection pool size
#quarkus.datasource.jdbc.min-size=10
#quarkus.datasource.jdbc.max-size=200
# Idle connection timeoutq
#quarkus.datasource.jdbc.idle-timeout=1000
# Connection acquisition timeout
#quarkus.datasource.jdbc.acquire-timeout=6000
# Oracle Datasource
#quarkus.datasource.oracle.db-kind=oracle
#quarkus.datasource.oracle.username=your_username
#quarkus.datasource.oracle.password=your_password
#quarkus.datasource.oracle.jdbc.url=**********************************************
quarkus.micrometer.export.json.enabled=true
quarkus.micrometer.export.json.path=metrics/json
quarkus.micrometer.export.prometheus.path=metrics/prometheus
quarkus.grpc.server.port=9001
quarkus.grpc.server.host=0.0.0.0
