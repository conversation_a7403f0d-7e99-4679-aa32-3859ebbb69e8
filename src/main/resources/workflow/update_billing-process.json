{"name": "update_billing_process", "taskReferenceName": "update_billing_process_ref_1", "description": null, "inputParameters": {"http_request": {"uri": "http://***********:8002/api/save-billing", "method": "POST", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "body": {"idpel": "${workflow.input.idpel}", "thblrek": "${workflow.input.thblrek}", "nopel": "${inisialisasi_ref_1.output.response.body.data.nopel}", "nama": "${inisialisasi_ref_1.output.response.body.data.nama}", "norek": "${inisialisasi_ref_1.output.response.body.data.norek}", "unitup": "${inisialisasi_ref_1.output.response.body.data.unitup}", "unitupi": "${inisialisasi_ref_1.output.response.body.data.unitupi}", "unitap": "${inisialisasi_ref_1.output.response.body.data.unitap}", "frt": "${inisialisasi_ref_1.output.response.body.data.frt}", "fjn": "${inisialisasi_ref_1.output.response.body.data.fjn}", "kdppj": "${inisialisasi_ref_1.output.response.body.data.kdppj}", "pemda": "${inisialisasi_ref_1.output.response.body.data.pemda}", "tarif": "${inisialisasi_ref_1.output.response.body.data.tarif}", "kogol": "${inisialisasi_ref_1.output.response.body.data.kogol}", "kdinkaso": "${inisialisasi_ref_1.output.response.body.data.kdinkaso}", "tgljttempo": "${inisialisasi_ref_1.output.response.body.data.tgljttempo}", "kwhlwbp": "${bb_perhitungan_kwh_blok_1_ref_1.output.result}}", "kwhwbp": "${bb_perhitungan_kwh_blok_2_ref_1.output.result}", "rplwbp": "${bb_per<PERSON>ungan_rupiah_blok_1_ref_1.output.result}", "rpwbp": "${bb_per<PERSON><PERSON>an_rupiah_blok_2_ref_1.output.result}", "rpblok3": "${bb_per<PERSON>ungan_rupiah_blok_3_ref_1.output.result}", "rpkvarh": "${workflow.input.rpkvarh}", "rpbeban": "${bb_per<PERSON>ungan_rupiah_beban_ref_1.output.result}", "rpptl": "${bb_perhitungan_ptl_ref_1.output.result}", "rptb": "${workflow.input.rptb}", "rpppn": "0", "rpbpju": "${bb_perhitungan_ppj_ref_1.output.result}", "rpbptrafo": "${workflow.input.rpbptrafo}", "rpsewatrafo": "${workflow.input.rpsewatrafo}", "rpsewakap": "${workflow.input.rpsewakap}", "rpangsa": "${inisialisasi_ref_1.output.response.body.data.rpangsa}", "rpangsb": "${workflow.input.rpangsb}", "rpangsc": "${workflow.input.rpangsc}", "rpmat": "${workflow.input.rpmat}", "rppln": "${workflow.input.rppln}", "rptag": "${bb_perhitungan_tagihan_ref_1.output.result}", "rpproduksi": "${workflow.input.rpproduksi}", "rpsubsidi": "${workflow.input.rpsubsidi}", "rptdllama": "${workflow.input.rptdllama}", "rptdlbaru": "${workflow.input.rptdlbaru}", "rpselisih": "${workflow.input.rpselisih}", "rpreduksi": "${workflow.input.rpreduksi}", "rpbk1": "${bb_per<PERSON>ungan_rupiah_blok_1_ref_1.output.result}", "rpbk2": "${bb_per<PERSON><PERSON>an_rupiah_blok_2_ref_1.output.result}", "rpbk3": "${bb_per<PERSON>ungan_rupiah_blok_3_ref_1.output.result}", "rprekBruto": "${workflow.input.rprekBruto}", "rprekNetto": "${workflow.input.rprekNetto}", "rptagBruto": "${workflow.input.rptagBruto}", "rptagNetto": "${workflow.input.rptagNetto}", "rpjbst": "${workflow.input.rpjbst}", "rpkompensasi": "${workflow.input.rpkompensasi}", "rpdiskonPremium": "${workflow.input.rpdiskonPremium}", "rpdiskon": "${workflow.input.rpdiskon}", "rpinvoice": "${workflow.input.rpinvoice}", "rpptlplus": "${workflow.input.rpptlplus}", "rpptlminus": "${workflow.input.rpptlminus}", "rpppjptl": "${workflow.input.rpppjptl}", "rpppjangsa": "${workflow.input.rpppjangsa}", "rpppjangsb": "${workflow.input.rpppjangsb}", "rpppjangsc": "${workflow.input.rpppjangsc}", "rpppnR3": "${workflow.input.rpppnR3}", "rpppnBptrafo": "${workflow.input.rpppnBptrafo}", "rpppnSewatrafo": "${workflow.input.rpppnSewatrafo}", "rpppnOpspararel": "${workflow.input.rpppnOpspararel}", "rpppnSewakap": "${workflow.input.rpppnSewakap}", "rpppnLain": "${workflow.input.rpppnLain}", "rptagMat": "${workflow.input.rptagMat}", "rpppnRec": "${workflow.input.rpppnRec}", "rprec": "${workflow.input.rprec}", "rpppnUap": "${workflow.input.rpppnUap}", "rpblok4": "${workflow.input.rpblok4}", "rpopsel": "${workflow.input.rpopsel}", "rpsewatrafoDil": "${workflow.input.rpsewatrafoDil}", "slalwbp": "${inisialisasi_ref_1.output.response.body.data.slalwbp}", "sahlwbp": "${inisialisasi_ref_1.output.response.body.data.sahlwbp}", "blok4": "${inisialisasi_ref_1.output.response.body.data.blok4}", "kdpt": "${inisialisasi_ref_1.output.response.body.datakdpt}", "kdpt2": "${inisialisasi_ref_1.output.response.body.data.kdpt2}", "daya": "${inisialisasi_ref_1.output.response.body.data.daya}", "faknpremium": "${workflow.input.faknpremium}", "dayar312": "${workflow.input.dayar312}", "subkogol": "${workflow.input.subkogol}", "kdklp": "${workflow.input.kdklp}", "kdind": "${workflow.input.kdind}", "kdam": "${workflow.input.kdam}", "kdkvamaks": "${workflow.input.kdkvamaks}", "maxDemand": "${workflow.input.maxDemand}", "kdbpt": "${workflow.input.kdbpt}", "dayabpt": "${workflow.input.dayabpt}", "kddayabpt": "${workflow.input.kddayabpt}", "jnsmut": "${inisialisasi_ref_1.output.response.body.data.jnsmut}", "thblmut": "${workflow.input.thblmut}", "kdmut": "${workflow.input.kdmut}", "tglnyala": "${inisialisasi_ref_1.output.response.body.data.tglnyala}", "tglrubah": "${inisialisasi_ref_1.output.response.body.data.tglrubah}", "kdpembmeter": "${workflow.input.kdpembmeter}", "fakm": "${inisialisasi_ref_1.output.response.body.data.fakm}", "fakmkvarh": "${inisialisasi_ref_1.output.response.body.data.fakmkvarh}", "fakmkvam": "${workflow.input.fakmkvam}", "tglbacalalu": "${workflow.input.tglbacalalu}", "tglbacaakhir": "${workflow.input.tglbacaakhir}", "slawbp": "${inisialisasi_ref_1.output.response.body.data.slawbp}", "sahwbp": "${inisialisasi_ref_1.output.response.body.data.sahwbp}", "slakvarh": "${inisialisasi_ref_1.output.response.body.data.slakvarh}", "sahkvarh": "${inisialisasi_ref_1.output.response.body.data.sahkvarh}", "sahkvamaks": "${inisialisasi_ref_1.output.response.body.data.sahkvamaks}", "dayamaks": "${inisialisasi_ref_1.output.response.body.data.dayamaks}", "sahkvamaxWbp": "${workflow.input.sahkvamaxWbp}", "dayamaxWbp": "${workflow.input.dayamaxWbp}", "rasioDaya": "${workflow.input.rasioDaya}", "blok3": "${workflow.input.blok3}", "pemkwh": "${inisialisasi_ref_1.output.response.body.data.pemkwh}", "pemkvarh": "${inisialisasi_ref_1.output.response.body.data.pemkvarh}", "kelbkvarh": "${workflow.input.kelbkvarh}", "jamnyala": "${inisialisasi_ref_1.output.response.body.data.jamnyala}", "kdangsa": "${inisialisasi_ref_1.output.response.body.data.kdangsa}", "lamaangsa": "${inisialisasi_ref_1.output.response.body.data.lamaangsa}", "thblangs1a": "${inisialisasi_ref_1.output.response.body.data.thblangs1a}", "angskea": "${inisialisasi_ref_1.output.response.body.data.angskea}", "kdangsb": "${inisialisasi_ref_1.output.response.body.data.kdangsb}", "lamaangsb": "${inisialisasi_ref_1.output.response.body.data.lamaangsb}", "thblangs1b": "${inisialisasi_ref_1.output.response.body.data.thblangs1b}", "angskeb": "${workflow.input.angskeb}", "kdangsc": "${workflow.input.kdangsc}", "lamaangsc": "${workflow.input.lamaangsc}", "thblangs1c": "${workflow.input.thblangs1c}", "angskec": "${workflow.input.angskec}", "flagdiskon": "${workflow.input.flagdiskon}", "prosendiskon": "${inisialisasi_ref_1.output.response.body.data.prosendiskon}", "kddiskon": "${inisialisasi_ref_1.output.response.body.data.kddiskon}", "jnsdiskon": "${workflow.input.jnsdiskon}", "kdinvoice": "${inisialisasi_ref_1.output.response.body.data.kdinvoice}", "statusemin": "${inisialisasi_ref_1.output.response.body.data.status_emin}", "fraksibptrafo": "${workflow.input.fraksibptrafo}", "trfbptrafo": "${workflow.input.trfbptrafo}", "trfsewakap": "${workflow.input.trfsewakap}", "dayajbst": "${workflow.input.dayajbst}", "kdbedajbst": "${workflow.input.kdbedajbst}", "jumlahPadam": "${workflow.input.jumlahPadam}", "hitungby": "${workflow.input.hitungby}", "kdproses": "${inisialisasi_ref_1.output.response.body.data.kdproses}", "kdprosesklp": "${inisialisasi_ref_1.output.response.body.data.kdprosesklp}", "dlpd": "${inisialisasi_ref_1.output.response.body.data.dlpd", "prosenppj": "${inisialisasi_ref_1.output.response.body.data.prosen_ppj}", "dlpdLm": "${workflow.input.dlpdLm}", "dlpdFkm": "${workflow.input.dlpdFkm}", "dlpdKvarh": "${workflow.input.dlpdKvarh}", "dlpd3bln": "${workflow.input.dlpd3bln}", "dlpdJnsmutasi": "${workflow.input.dlpdJnsmutasi}", "dlpdTglbaca": "${workflow.input.dlpdTglbaca}", "alasanKoreksi": "${workflow.input.alasanKoreksi}", "flagsewakap": "${workflow.input.flagsewakap}", "faradkap": "${workflow.input.faradkap}", "kddk": "${inisialisasi_ref_1.output.response.body.data.kddk}", "kdbacameter": "${inisialisasi_ref_1.output.response.body.data.kdbacameter}", "kdrekg": "${workflow.input.kdrekg}", "copyrek": "${workflow.input.copyrek}", "kdmeterai": "${workflow.input.kdmeterai}", "jnsmutAde": "${inisialisasi_ref_1.output.response.body.data.jnsmutAde}", "flagppjangsa": "${workflow.input.flagppjangsa}", "flagppjangsb": "${workflow.input.flagppjangsb}", "flagppjangsc": "${workflow.input.flagppjangsc}", "fraksibeban": "${workflow.input.fraksibeban}", "haribeban": "${workflow.input.haribeban}", "haribebanBulan": "${workflow.input.haribebanBulan}", "fraksikwh": "${workflow.input.fraksikwh}", "fraksiemin": "${workflow.input.fraksiemin}", "fraksihemat": "${workflow.input.fraksihemat}", "fraksidmp": "${workflow.input.fraksidmp}", "haripakai": "${workflow.input.haripakai}", "haripakaiBulan": "${workflow.input.haripakaiBulan}", "jamnyala350Batas": "${workflow.input.jamnyala350Batas}", "kwhlwbpReal": "${workflow.input.kwhlwbpReal}", "kwhwbpReal": "${workflow.input.kwhwbpReal}", "kwhblok3Real": "${workflow.input.kwhblok3Real}", "pemkwhReal": "${workflow.input.pemkwhReal}", "kvarhpakaiReal": "${workflow.input.kvarhpakaiReal}", "kvarhlebih": "${workflow.input.kvarhlebih}", "cosphiReal": "${workflow.input.cosphiReal}", "jamnyalaReal": "${workflow.input.jamnyalaReal}", "kwhlwbpRegReal": "${workflow.input.kwhlwbpRegReal}", "kwhwbpRegReal": "${workflow.input.kwhwbpRegReal}", "pemkwhRegReal": "${workflow.input.pemkwhRegReal}", "jamnyalaRegReal": "${workflow.input.jamnyalaRegReal}", "kwhlwbpPremiumReal": "${workflow.input.kwhlwbpPremiumReal}", "kwhwbpPremiumReal": "${workflow.input.kwhwbpPremiumReal}", "pemkwhPremiumReal": "${workflow.input.pemkwhPremiumReal}", "kwhlwbpBtobReal": "${workflow.input.kwhlwbpBtobReal}", "kwhwbpBtobReal": "${workflow.input.kwhwbpBtobReal}", "pemkwhBtobReal": "${workflow.input.pemkwhBtobReal}", "kwhlwbpReg": "${workflow.input.kwhlwbpReg}", "kwhwbpReg": "${workflow.input.kwhwbpReg}", "kwhblok3Reg": "${workflow.input.kwhblok3Reg}", "pemkwhReg": "${workflow.input.pemkwhReg}", "kwhminEminReg": "${workflow.input.kwhminEminReg}", "eminReg": "${workflow.input.eminReg}", "rprekmin": "${workflow.input.rprekmin}", "jnsbatasEminReg": "${workflow.input.jnsbatasEminReg}", "kwhlwbpPremium": "${workflow.input.kwhlwbpPremium}", "kwhwbpPremium": "${workflow.input.kwhwbpPremium}", "pemkwhPremium": "${workflow.input.pemkwhPremium}", "kwhminEminPremium": "${workflow.input.kwhminEminPremium}", "eminPremium": "${workflow.input.eminPremium}", "jnsbatasEminPremium": "${workflow.input.jnsbatasEminPremium}", "kwhlwbpBtob": "${workflow.input.kwhlwbpBtob}", "kwhwbpBtob": "${workflow.input.kwhwbpBtob}", "pemkwhBtob": "${workflow.input.pemkwhBtob}", "kwhminEminBtob": "${workflow.input.kwhminEminBtob}", "eminBtob": "${workflow.input.eminBtob}", "jnsbatasEminBtob": "${workflow.input.jnsbatasEminBtob}", "statussubsidi": "${workflow.input.statussubsidi}", "rpsub": "${workflow.input.rpsub}", "rpnonsub": "${workflow.input.rpnonsub}", "kwhsub": "${workflow.input.kwhsub}", "kwhnonsub": "${workflow.input.kwhnonsub}", "btskwhsubsidi": "${workflow.input.btskwhsubsidi}", "rpblok1Sub": "${workflow.input.rpblok1Sub}", "rpblok2Sub": "${workflow.input.rpblok2Sub}", "rpblok3Sub": "${workflow.input.rpblok3Sub}", "rpblok1Nonsub": "${workflow.input.rpblok1Nonsub}", "rpblok2Nonsub": "${workflow.input.rpblok2Nonsub}", "rpblok3Nonsub": "${workflow.input.rpblok3Nonsub}", "kwhblok1Sub": "${workflow.input.kwhblok1Sub}", "kwhblok2Sub": "${workflow.input.kwhblok2Sub}", "kwhblok3Sub": "${workflow.input.kwhblok3Sub}", "kwhblok1Nonsub": "${workflow.input.kwhblok1Nonsub}", "kwhblok2Nonsub": "${workflow.input.kwhblok2Nonsub}", "kwhblok3Nonsub": "${workflow.input.kwhblok3Nonsub}", "jnsbatas101a": "${workflow.input.jnsbatas101a}", "btsblok101a": "${workflow.input.btsblok101a}", "bpakai101a": "${workflow.input.bpakai101a}", "kwhwbpBatas": "${workflow.input.kwhwbpBatas}", "dayamaxwbpBatas": "${workflow.input.dayamaxwbpBatas}", "dayamaxSuplai": "${workflow.input.dayamaxSuplai}", "dayamaxwbpSuplai": "${workflow.input.dayamaxwbpSuplai}", "kdjnsmeter": "${workflow.input.kdjnsmeter}", "suplaiDayamax": "${workflow.input.suplaiDayamax}", "suplaiDayamaxwbp": "${workflow.input.suplaiDayamaxwbp}", "dayamaxReal": "${workflow.input.dayamaxReal}", "dayamaxwbpReal": "${workflow.input.dayamaxwbpReal}", "cosphiDmp": "${workflow.input.cosphiDmp}", "kwhpakaimaks": "${workflow.input.kwhpakaimaks}", "kwhpakainormal": "${workflow.input.kwhpakainormal}", "syaratpakai1": "${workflow.input.syaratpakai1}", "syaratpakai2": "${workflow.input.syaratpakai2}", "statuspakai": "${workflow.input.statuspakai}", "statusdmp": "${workflow.input.statusdmp}", "kwhwbp1Reg": "${workflow.input.kwhwbp1Reg}", "kwhwbp2Reg": "${workflow.input.kwhwbp2Reg}", "dayawbpDis": "${workflow.input.dayawbpDis}", "kwhwbpDasarIns": "${workflow.input.kwhwbpDasarIns}", "kwhwbpTambahIns": "${workflow.input.kwhwbpTambahIns}", "trfwbp1Reg": "${workflow.input.trfwbp1Reg}", "trfwbp2Reg": "${workflow.input.trfwbp2Reg}", "trfdayawbpDis": "${workflow.input.trfdayawbpDis}", "trfwbpIns": "${workflow.input.trfwbpIns}", "rpwbp1Reg": "${workflow.input.rpwbp1Reg}", "rpwbp2Reg": "${workflow.input.rpwbp2Reg}", "rpdayawbpDis": "${workflow.input.rpdayawbpDis}", "rpinsDasar": "${workflow.input.rpinsDasar}", "rpinsTambah": "${workflow.input.rpinsTambah}", "rpinsDmp": "${workflow.input.rpinsDmp}", "rpinsMax": "${workflow.input.rpinsMax}", "rplwbpReg": "${workflow.input.rplwbpReg}", "rpwbpReg": "${workflow.input.rpwbpReg}", "rpblok3Reg": "${workflow.input.rpblok3Reg}", "rpkwhReg": "${workflow.input.rpkwhReg}", "rplwbpPremium": "${workflow.input.rplwbpPremium}", "rpwbpPremium": "${workflow.input.rpwbpPremium}", "rpkwhPremium": "${workflow.input.rpkwhPremium}", "rplwbpBtob": "${workflow.input.rplwbpBtob}", "rpwbpBtob": "${workflow.input.rpwbpBtob}", "rpkwhBtob": "${workflow.input.rpkwhBtob}", "trfbeban": "${inisialisasi_ref_1.output.response.body.data.biaya_beban}", "trfkvarh": "${inisialisasi_ref_1.output.response.body.data.biaya_kvarh}", "trflwbpReg": "${workflow.input.trflwbpReg}", "trfwbpReg": "${workflow.input.trfwbpReg}", "trfblok3": "${inisialisasi_ref_1.output.response.body.data.biaya_pakai3}", "trflwbpPremium": "${workflow.input.trflwbpPremium}", "trfwbpPremium": "${workflow.input.trfwbpPremium}", "trflwbpBtob": "${workflow.input.trflwbpBtob}", "trfwbpBtob": "${workflow.input.trfwbpBtob}", "faktorkTdl": "${workflow.input.faktorkTdl}", "faktorpTdl": "${workflow.input.faktorpTdl}", "trflwbpTdl": "${workflow.input.trflwbpTdl}", "trfwbpTdl": "${workflow.input.trfwbpTdl}", "lwbp3": "${workflow.input.lwbp3}", "wbp3": "${workflow.input.wbp3}", "blok33": "${workflow.input.blok33}", "kwhpakai3": "${bb_perhitungan_kwh_blok_3_ref_1.output.result}", "rplwbp3": "${workflow.input.rplwbp3}", "rpwbp3": "${workflow.input.rpwbp3}", "rpblok33": "${workflow.input.rpblok33}", "rpkwh3": "${bb_per<PERSON>ungan_rupiah_blok_3_ref_1.output.result}", "trflwbpDasar": "${workflow.input.trflwbpDasar}", "trfwbpDasar": "${workflow.input.trfwbpDasar}", "trfkvarhDasaridpel": "${workflow.input.trfkvarhDasaridpel}"}}}, "type": "HTTP", "dynamicTaskNameParam": null, "caseValueParam": null, "caseExpression": null, "scriptExpression": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksParam": null, "dynamicForkTasksInputParamName": null, "startDelay": 0, "subWorkflowParam": null, "sink": null, "optional": false, "taskDefinition": null, "rateLimited": null, "asyncComplete": false, "loopCondition": null, "retryCount": null, "evaluatorType": null, "expression": ""}