.flowchart-container{position:relative;overflow:hidden}.flowchart-links-layer,.flowchart-operators-layer,.flowchart-temporary-link-layer{position:absolute;top:0;left:0;width:100%;height:100%}.flowchart-operators-layer,.flowchart-temporary-link-layer{pointer-events:none}.flowchart-temporary-link-layer{display:none}.flowchart-link,.flowchart-operator{cursor:default}.flowchart-operator-connector{position:relative;padding-top:5px;padding-bottom:5px}.flowchart-operator-connector-label{font-size:small}.flowchart-operator-inputs .flowchart-operator-connector-label{margin-left:14px}.flowchart-operator-outputs .flowchart-operator-connector-label{text-align:right;margin-right:5px}.flowchart-operator-connector-arrow{width:0;height:0;border-top:10px solid transparent;border-bottom:10px solid transparent;border-left:10px solid #ccc;position:absolute;top:0}.flowchart-operator-connector-small-arrow{width:0;height:0;border-top:5px solid transparent;border-bottom:5px solid transparent;border-left:5px solid transparent;position:absolute;top:5px;pointer-events:none}.flowchart-operator-connector:hover .flowchart-operator-connector-arrow{border-left:10px solid #999}.flowchart-operator-inputs .flowchart-operator-connector-arrow{left:-1px}.flowchart-operator-outputs .flowchart-operator-connector-arrow{right:-10px}.flowchart-operator-inputs .flowchart-operator-connector-small-arrow{left:-1px}.flowchart-operator-outputs .flowchart-operator-connector-small-arrow{right:-7px}.unselectable{-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;user-select:none}.flowchart-operator{position:absolute;width:140px;border:1px solid #ccc;background:#fafafa;pointer-events:auto}.flowchart-operator.hover{border-color:#999}.flowchart-operator.selected{border-color:#555}.flowchart-operator .flowchart-operator-title{width:100%;padding:5px;font-weight:bold;box-sizing:border-box;border-bottom:1px solid #ddd;background:#f0f0f0;height:auto;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;cursor:move}.flowchart-operator .flowchart-operator-inputs-outputs{display:table;width:100%;margin-top:5px;margin-bottom:5px}.flowchart-operator .flowchart-operator-inputs,.flowchart-default-operator .flowchart-operator-outputs{display:table-cell;width:50%}.flowchart-vertical .flowchart-operator-inputs,.flowchart-vertical .flowchart-operator-outputs{position:relative;text-align:center;display:table;width:100%}.flowchart-vertical .flowchart-operator-connector-set{display:table-cell}.flowchart-vertical .flowchart-operator-connector{position:relative}.flowchart-vertical .flowchart-operator-connector-label{position:relative;text-align:center;width:100%}.flowchart-vertical .flowchart-operator-inputs .flowchart-operator-connector-label{margin-left:auto}.flowchart-vertical .flowchart-operator-connector-arrow{border-left:10px solid transparent;border-right:10px solid transparent;border-top:10px solid #ccc;left:calc(50% - 10px)}.flowchart-vertical .flowchart-operator-connector:hover .flowchart-operator-connector-arrow{border-left-color:transparent;border-top-color:#999}.flowchart-vertical .flowchart-operator-connector-small-arrow{border-right:5px solid transparent;top:2px;left:calc(50% - 5px)}.flowchart-vertical .flowchart-operator-connector-arrow{top:0}.flowchart-vertical .flowchart-operator-outputs .flowchart-operator-connector-arrow{bottom:-20px;top:auto}.flowchart-vertical .flowchart-operator-outputs .flowchart-operator-connector-small-arrow{left:calc(50% - 5px);bottom:-12px;top:auto}.flowchart-vertical .flowchart-link rect{display:none}.flowchart-operator-body{padding:5px;cursor:move}
