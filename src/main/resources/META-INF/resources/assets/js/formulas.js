var operations = [];
var conditions = [];
var subroutines = [];
var inputoutputs = [];
var inputs = [];
var outputs = [];
var objects = [];
var isRtl = $('html').attr('data-textdirection') === 'rtl';
var formulas = [];
function loadVariable(target){
    $.ajax({
        url:"/api/components/input",
        type:"GET",
        cache:false,
        beforeSend:function(){
            $('.spinner-border').show();
        }
    }).done(function(json){
        const components = json;
        $('.spinner-border').hide();
        $.each(components,function(i,item){
            var html = `<option value="${item.variable_name}">${item.description}</option>`;
            $(target).append(html);
        });
    });
}
function loadVarOutput(target,value){
    $.ajax({
        url:"/api/components/output",
        type:"GET",
        cache:false,
        beforeSend:function(){
            $('.spinner-border').show();
        }
    }).done(function(json){
        const components = json;
        $('.spinner-border').hide();
        $.each(components,function(i,item){
            if(item.variable_name===value){
            var html = `<option value="${item.variable_name}" selected>${item.description}</option>`;
            } else {
            var html = `<option value="${item.variable_name}">${item.description}</option>`;
            }

            $(target).append(html);
        });
    });
}
function loadFormula(name,target){
    var URL = `/api/formula/refs`;
    if(name!==''){
        URL = `/api/formula/other/${name}`;
    }
    $.ajax({
        url:URL,
        type:"GET",
        cache:false,
        beforeSend:function(){
            formulas=[];
            $(target).children().remove();
        }
    }).done(function(json){
        const formuls = json;
        $.each(formuls,function(i,item){
            formulas.push(item);
            var html = `<option value="${item.output_variables}">${item.name}</option>`;
            $(target).append(html);
        });
    });
}
var diagram;
function formToJSON(form) {
    const formData = new FormData(form);
    const jsonObject = {};

    formData.forEach((value, key) => {
        // Check if key is already present
        if (jsonObject[key]) {
            // If it's an array, add the value to the array
            if (Array.isArray(jsonObject[key])) {
                jsonObject[key].push(value);
            } else {
                // If it's not an array, convert it to an array and add the new value
                jsonObject[key] = [jsonObject[key], value];
            }
        } else {
            jsonObject[key] = value;
        }
    });

    return jsonObject;
}
function drawDiagram(){
    
    if(diagram){
        diagram.clean();
    }
    var content = $('#flowchart').val();
    diagram = flowchart.parse(content);
    diagram.drawSVG('diagram',{
        'x': 0,
        'y': 0,
        'line-width': 3,
        'line-length': 50,
        'text-margin': 10,
        'font-size': 14,
        'font-color': 'black',
        'line-color': 'black',
        'element-color': 'black',
        'fill': 'white',
        'yes-text': 'yes',
        'no-text': 'no',
        'arrow-end': 'block',
        'scale': 1,
        // style symbol types
        'symbols': {
        'start': {
            'font-color': 'red',
            'element-color': 'green',
            'fill': 'yellow'
        },
        'end':{
            'class': 'end-element'
        }
        },
        // even flowstate support ;-)
        'flowstate' : {
        'past' : { 'fill' : '#CCCCCC', 'font-size' : 12},
        'current' : {'fill' : 'yellow', 'font-color' : 'red', 'font-weight' : 'bold'},
        'future' : { 'fill' : '#FFFF99'},
        'request' : { 'fill' : 'blue'},
        'invalid': {'fill' : '#444444'},
        'approved' : { 'fill' : '#58C4A3', 'font-size' : 12, 'yes-text' : 'APPROVED', 'no-text' : 'n/a' },
        'rejected' : { 'fill' : '#C45879', 'font-size' : 12, 'yes-text' : 'n/a', 'no-text' : 'REJECTED' }
        }
    });
}
function refreshFlowchart(){
    drawDiagram();
    saveFlowchartAsImage();
}
$('#flowchart').on('blur',function(){
    refreshFlowchart();
});
$('#apiUrl').on('change',function(){
    var url = $(this).val();
    $('#api_url').val(url);
})
$('#apiMethod').on('change',function(){
    var method = $(this).val();
    $('#api_method').val(method);
})
function generateFlowOrder(){
    var options = $('#objects').children();
    var orders = [];
    $.each(options,function(i,item){
        var value = $(item).val();
        orders.push(value);
    });
    return orders.join("->");
}
function addObject(){
    var name = $('#objname').val();
    var object = $('#select-object').val();
    var express = $('#objexpress').val();
    switch(object){
        case 'condition':
            conditions.push(name);
            break;
        case 'operation':
            operations.push(name);
            break;    
        case 'subroutine':
            subroutines.push(name);
            break;    
        case 'input':
            inputs.push(name);
            break;
        case 'inputoutputs':
            inputoutputs.push(name);
            break;        
        case 'outputs':
            outputs.push(name);
            break;    
        default:
    }
    objects.push(name);
    var command = `${name}=>${object}: ${express}`;
    var context=$('#flowchart').val();
    var lines = context.split('\n');
    var index = lines.indexOf('');
    var last = lines.length-1;
    console.log(index);
    lines.splice(index,0,command);
    lines.pop();
    var option = `<option value="${name}">${name}</option>`;
    $('#objects').children().last().before(option);
    var forder = generateFlowOrder();
    lines.push(forder);
    $('#flowchart').val(lines.join("\n"));
    refreshFlowchart();
}
function clearFlow(){
    var content = `st=>start: Mulai
    e=>end: Selesai
    
    st->e`;
    $('#flowchart').val(content);
    drawDiagram();
}
function saveFlowchartAsImage() {
    var svg = document.querySelector('#diagram svg');
    // var form = document.getElementById('upload-image');
    var imagebase64 = document.getElementById('imagebase64');
    var canvas = document.getElementById('canvas');
    var ctx = canvas.getContext('2d');
    
    var svgString = new XMLSerializer().serializeToString(svg);
    var svgBlob = new Blob([svgString], {type: 'image/svg+xml;charset=utf-8'});
    var url = URL.createObjectURL(svgBlob);

    var img = new Image();
    img.onload = function() {
        canvas.width = svg.clientWidth;
        canvas.height = svg.clientHeight;
        ctx.drawImage(img, 0, 0);
        
        var imgURL = canvas.toDataURL('image/png');
        // console.log(base64String);
        var value64 = imgURL.split(",");
        
        imagebase64.value = value64[1];
        // Clean up
        URL.revokeObjectURL(url);
          
    };
    
    // Set the src attribute of the Image to the Blob URL
    img.src = url;
    img.setAttribute('id','image-render');
    img.style.display = 'none';
    
}
function saveFormula(){
    const type = $('#formula_type').val();
    if(type==='INLINE'){
        const jcontent = $('#j_script').val();
        $('#expressions').val(jcontent);
    } else {
        $('#expressions').val('');
    }
    
    $('#form_formula').submit();
}
function addOutputFormula(){
    var content = $('#input-params').val();
    var expression = $('#objexpress').val();
    var name = $('#select-formula').val();
    content+=name+'\n';
    $('#input-params').val(content);
    var variable = `${expression} $.${name}`;
    $('#objexpress').val(variable);
}
function addInputParam(){
    var content = $('#input-params').val();
    var expression = $('#objexpress').val();
    var name = $('#select-variable').val();
    content+=name+'\n';
    $('#input-params').val(content);
    var variable = `${expression} $.${name}`;
    $('#objexpress').val(variable);
}

$('#form_formula').submit(function(e){
    e.preventDefault();
    var inputParam = $('#input-params').val();//input-params
    const type = $('#formula_type').val();
    //mengambil nilai text di output params
    if(type==='HTTP'){
        const outputText = $('#output-params').val();
        const outputs = outputText.split('\n');
        const outVars = outputs.join(',');
        $('#output_variable').val(outVars);
    } else {
        const text = $('#select_output_variable').val();
        $('#output_variable').val(text);
    }
    const outVar = $('#output_variable').val();
    console.log(outVar);
    if(inputParam.length==0){
        toastr['warning']('Anda Belum memasukkan input parameter','Peringatan!');
        return;
    }
    var fc = $('#flowchart').val().split("\n");
    if(fc.length==4){
        toastr['warning']('Flowchart belum lengkap','Peringatan!');
        return;
    }
    var inputParams = inputParam.split("\n");
    var inputs = inputParams.join(",");
    $('#input_variables').val(inputs);
    $('#api_method').val($('#apiMethod').val());
    var action = $('#form_action').val();
    var valid = checkScript();
    if(!valid){
        return;
    }
    $(this).ajaxSubmit({success:function(json,statusText,xhr,form){
           console.log(json);
      var jsonContent = JSON.stringify(json.task_def);
       if(action=='new'){
          $('#json_content').val('['+jsonContent+']');
       } else {
          $('#json_content').val(jsonContent);
       }

       if(json.status){
          toastr['success'](json.message,'Selamat!',{
                  timeOut: 1000,
                  fadeOut: 1000,
                  onHidden: function() {
                    $('#form_upload').submit();
                    
                  }
          });
       } else {
          toastr['error'](json.message,'Maaf!');
       }
    }});
 });
 function saveJson(){
    $('#form_json').submit();
 }
$('#form_json').submit(function(e){
    e.preventDefault();
    var jsonData = $('#json_content').val();
    var json = JSON.parse(jsonData);
    var URL = $(this).attr('action');
    console.log(json);
    $.ajax({
      url: URL,
      type:'POST',
      dataType:'json',
      data: JSON.stringify(json),
      cache: false,
      headers : {'Content-Type':'application/json',
      'Accept' : 'application/json'
      },
      complete: function(xhr, textStatus) {
             console.log(xhr.status);

      },
      error: function(e){
//          console.error('error');
        console.log(e);
        var response = e.responseJSON;
        var details = response.details;
        toastr['error'](details,'Maaf!');
      }
    }).done(function(json){
        if(json.status){
        toastr['success'](json.message,'Selamat!',{
                          timeOut: 1000,
                          fadeOut: 1000,
                          onHidden: function() {
        //                      window.location="/formula";
                          }});
        } else {
            toastr['error'](json.message,'Maaf!');
        }

    });

 });
 $('#formula_type').on('change',function(){
    const type = $(this).val();
    if(type==='HTTP'){
        $('#select_output_variable').hide();
        $('.http').show();
    } else {
        $('#select_output_variable').show();
        $('.http').hide();
    }
 });
 function checkFormulaType(inputes){
    if($('#formula_type').length){
        $('#input-parameters').children().remove();
        $.each(inputes,function(i,item){
            if(item!==''){
                var label = item.replaceAll("_"," ").toUpperCase();
                var html = `<div class="form-group row mb-1">
                <label for="param_input_${item}" class="col-form-label col-md-2">${label}</label>
                <div class="col-md-4">
                    <input type="text" class="form-control" name="${item}" id="param_input_${item}" placeholder="">
                </div>
            </div>`;
                $('#input-parameters').append(html);
            }
            
        });
    }
    if($('#var_container').length){
        $.each(inputes,function(i,item){
            const label = item.replaceAll("_"," ").toUpperCase();
            var html = `<div class="form-group mb-1">
                        <label for="${item}" class="col-form-label">${label}</label>
                        <input type="text" name="${item}" id="${item}" class="form-control">
                 </div>`;
            $('#var_container').append(html);
        });
    }
 }
$('#form_api_test').submit(function(e){
    e.preventDefault();
    var URL = $('#apiUrl').val();
    var method = $('#apiMethod').val();
    var formData = $(this).serialize();
    console.log(formData);
    if(method==='GET'){
        URL +="?"+formData;
        console.log(URL);
        $.ajax({
            url:URL,
            type:method,
            cache:false,
            success:function(json,e){
                console.log(json);
            },
            error:function(e){
                console.log(e.message);
            }
        }).done(function(json){
            if(json!==undefined){
                const content = JSON.stringify(json,null,4);
                const map = new Map(Object.entries(json));
                console.log(map);
                $('#api_result').val(content);
            } else {
                $('#api_result').val('not found');
            }
            
        });
    } else {
        var formData = formToJSON(this);
        console.log(formData);
        $.ajax({
            url:URL,
            type:method,
            dataType:'json',
            data:JSON.stringify(formData),
            headers:{'Accept':'application/json',
                'Content-Type':'application/json'},
            cache:false,
            complete: function(xhr, textStatus) {
                console.log(xhr.status);
                if(xhr.status=='404'){
                    $('#api_result').val('DATA NOT FOUND');
                }
            },
            error:function(e){
                console.log(e.message);
            }
        }).done(function(json){
            if(json!==undefined){
                // console.log(json);
                const map = new Map(Object.entries(json.data));
                const outputs = Array.from(map.entries());
                console.log(outputs);
                var params = [];
                $.each(outputs,function(i,item){
                    params.push(item[0]);
                });
                const text = params.join('\n');
                console.log(text);
                $('#output-params').val(text);
                $('#api_result').val(JSON.stringify(json,null,4));
            } else {
                $('#api_result').val('not found');
            }
        });
    }
}); 
$('#form_test').submit(function(e){
    e.preventDefault();
    var varcontent = $(this).serialize();
//    console.log(content);
    var dataForm = {content: varcontent};
//    console.log(dataForm);
    var varOutput = $('#output_variable').val();
    var testType = $('#formula_type').val();
    if(testType=='HTTP'){
        var URL = $('#api_url').val();
        var METHOD = $('#api_method').val();
        $.ajax({
            url:URL,
            type:METHOD,
            data:varcontent,
            complete: function(xhr, textStatus) {
                console.log(xhr.status);
                if(xhr.status=='404'){
                    $('#'+varOutput).val('DATA NOT FOUND');
                }
            },
            error: function(e){
                console.error('error');
            }
        }).done(function(json){
            console.log(json);
            var cnt = JSON.stringify(json);
            $('#'+varOutput).val(cnt);
        });
    } else {
        $.ajax({
            url:"/api/formula/run",
            type:'POST',
            data:JSON.stringify(dataForm),
            headers : {'Content-Type':'application/json',
            'Accept':'application/json'},
        }).done(function(json){
            console.log(json);
            $('#'+varOutput).val(json[varOutput]);
        });

    }

});
$('#gen_js').click(function(){
    var fcr = $('#flowchart').val().trim();
    var output = $('#output_variable').val();
    var lines = fcr.split("\n");
    var commands = [];
    var ix = 100;
    for(i=0;i<lines.length;i++){
        if(lines[i].trim()===''){
            ix = i;
        }
        if(i>ix){
            commands.push(lines[i]);
        }
    }
    // console.log(ix);
    // console.log(commands);
    var code = '';
    // var lastcode = '';
    for(i=0;i<commands.length;i++){
        var flows = commands[i].split("->");
        console.log(flows);
        for(j=0;j<flows.length;j++){
            if(flows[j]==='st'){
                code+='function getValue()\{';
            } else if(flows[j]==='e'){
                code+='\n\t \}\n';
            } else {
                var index = lines.findIndex(line => line.includes(flows[j]));
                var baris = lines[index].split(':');
                // console.log(index);
                var yespos = flows[j].indexOf('yes');
                var nopos = flows[j].indexOf('no');
                var linecode = baris[1];
                // console.log(`yes ${yespos} no ${nopos}`);
                var names = baris[0].split("=>");
                // console.log('names');
                // console.log(names);
                // console.log(linecode);
                if(names[1]==='operation'){
                    code +=`\n\tvar ${linecode.trim()};`;
                    
                }
                if(names[1]==='condition'){
                    code +=`\n\tif(${linecode})\{`;
                    
                }
                // if(yespos>0){
                //     code +=`\{\n var ${linecode}; \}\n`;
                // }
                if(nopos>0){
                    code +=`\t else \{`;
                }
                
            }
        }
    }
    code+=` return ${output};\n}\ngetValue();`;
    $('#j_script').val(code);
    
});

function checkScript(){
    var code=$('#j_script').val();
    try {
        new Function(code);
        return true;
        // 
        
    } catch (e) {
        toastr['error']('Syntax error : '+e.message,'Skrip Salah!');
        return false;
    }
}
$('#btn_check').click(function(){
    var value = checkScript();
    if(value){
        toastr['success']('Skrip JS Valid','Selamat');
    }
});
if($('#table-formula').length){
    $('#table-formula').DataTable();
}
$('#thumbnail-preview').on('shown.bs.modal',function(e){
    var imageUrl = $(e.relatedTarget).attr('data-url');
    $('#url_preview').attr('href',imageUrl);
    // $.ajax({
    //     url:imageUrl,
    //     type:'GET',
    //     cache:false,
    //     beforeSend:function(){
    //         $('#thumbnail_preview').hide();
    //     }
    // }).done(function(stream){
    // $('#spinner').hide();
    // $('#thumbnail_preview').show();
    // var bodyStream = `data:image/png;base64,${stream}`;
    $('#thumbnail_preview').attr('src',imageUrl);
    // });
});
$('#modalFlowObject').on('shown.bs.modal',function(e){
    $('.spinner-border').hide();
    $('#select-formula').select2({dropdownParent: $("#modalFlowObject")});
    $('#select-variable').select2({dropdownParent: $("#modalFlowObject")});
    loadFormula('','#select-formula');
});
$(document).delegate('.delete','click',function(e){
    e.preventDefault();
    var actionUrl = $(this).attr('data-url');
    var answ = confirm('Ingin menghapus data ini?');
    if(answ){
        $.ajax({
                url: actionUrl,
                type:"GET",
                cache:false,
            }).done(function(json){
                    if(json.status){
                        toastr['success'](json.message,'Selamat!',{
                                timeOut: 1000,
                                fadeOut: 1000,
                                onHidden: function() {
                                    window.location="/formula";
                                }
                        });
                     } else {
                        toastr['error'](json.message,'Maaf!');
                     }
            });
    }
});
$('#input-params').on('blur',function(){
    const content = $(this).val();
    const lines = content.split('\n');
    checkFormulaType(lines);
    const params = lines.join(",");
    $('#input_variables').val(params);
});
$('#form_upload').submit(function(e){
    e.preventDefault();
    var formData = formToJSON(this);
    var URL = $(this).attr('action');
    $.ajax({
        url:URL,
        type:'POST',
        dataType:'json',
        data:JSON.stringify(formData),
        headers:{'Accept':'application/json',
            'Content-Type':'application/json'},
        cache:false,
        complete: function(xhr, textStatus) {
            console.log(xhr.status);
            
        },
        error:function(e){
            console.log(e.message);
            toastr['error']('Upload File Gagal','Maaf');
        }
    }).done(function(json){
        if(json.status){
            toastr['success'](json.message,'Selamat');
            window.location = "/formula";
        } else {
            toastr['error']('Upload File Gagal','Maaf');
        }
    });
});

$(document).ready(function(){
    $('.form-select').select2();
    $('#select_output_variable').on('change',function(){
        var text = $(this).val();
        if(text!==''){
            $('#btn_save').prop('disabled',false);
            $('#gen_js').prop('disabled',false);   
        }
     });
    if($('#form_formula').length){
        $.contextMenu({
            selector: '#chart',
            trigger: 'left',
            callback: function (key, options) {
            //   var r = 'clicked ' + key;
            //   window.console &&
            //     toastr['success']('', r, {
            //       rtl: isRtl
            //     });
            var cnt =0;
            var name = '';
            switch(key){
                case 'condition':
                    cnt = conditions.length + 1;
                    name = 'cond'+cnt;
                    break;
                case 'input':
                    cnt = inputs.length + 1;
                    name = 'inp'+cnt;
                    break;
                case 'inputoutput':
                    cnt = inputoutputs.length + 1;
                    name = 'inout'+cnt;
                    break;
                case 'operation':
                    cnt = operations.length + 1;
                    name = 'op'+cnt;
                    break;
                case 'subroutine':
                    cnt = subroutines.length + 1;
                    name = 'sub'+cnt;
                    break;                
            }
            $('#objname').val(name);
            $('#select-object').val(key);
            $('#modalFlowObject').modal('show');
            },
            items: {
              'condition': { name: 'Condition' },
              'input': { name: 'Input' },
              'inputoutput': { name: 'Input Output' },
              'operation': { name: 'Operation' },
              'output': { name: 'Output' },
              'subroutine': { name: 'Sub Routine' }
            }
          });
        const inputvar = $('#input_variables').val();
        if(inputvar.length>0){
            var inputes = inputvar.split(",");
            checkFormulaType(inputes);
            var content = '';
            $.each(inputes,function(i,item){
                content+=item+'\n';
            });
            $('#input-params').val(content);
        }
        loadVariable('#select-variable');
        
        if($('#flowchart').length){
            var lines = $('#flowchart').val().trim().split("\n");
            var commands = [];
            var ix = 100;
            for(i=0;i<lines.length;i++){
                if(lines[i].trim()===''){
                    ix = i;
                }
                if(i>ix){
                    commands.push(lines[i]);
                }
            }
            var count = lines.length-1;
            var objects = lines[count].split("->");
            $('#objects').children().remove();
            $.each(objects,function(i,item){
                var html = `<option value="${item}">${item}</option>`;
                $('#objects').append(html);
            });
            drawDiagram();
            saveFlowchartAsImage();    
        }
    }
    if($('#output_variable').length){
        const ours = $('#output_variable').val().split(",");
        const text = ours.join('\n');
        $('#output-params').val(text);
    }
    if($('#formula_type').length){
        const type = $('#formula_type').val();
        if(type==='HTTP'){
            $('.http').show();
        } else {
            $('.http').hide();
        }
    }
});
