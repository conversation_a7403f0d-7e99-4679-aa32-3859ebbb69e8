function hitungBeban(){
    var rpBeban = 0.0;
    var faktorMax = $.daya_max / 1000;
    var faktorDaya = 0.5 * $.daya / 1000;
    var fraksiBeban = 1.0;
    if($.met_kwh==='G'){
        if(faktorMax>faktorDaya){
            rpBeban = $.biaya_beban * $.faktor_fjn * faktorMax * fraksiBeban;
        } else {
            rpBeban = $.biaya_beban * $.faktor_fjn * faktorDaya;
        }
    } else {
        rpBeban = $.biaya_beban * $.daya * $.faktor_fjn / 1000;
    }
    return rpBeban;
}
hitungBeban();