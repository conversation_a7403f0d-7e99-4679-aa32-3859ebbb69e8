/********* CONTEXT MENU *********/
.context-menu-list {
  margin: 0;
  padding: 0.5rem 0;
  border-radius: 0.357rem;
  border: 1px solid rgba(34, 41, 47, 0.05);
  box-shadow: 0 5px 25px rgba(34, 41, 47, 0.1);
  min-width: 10rem; }

.context-menu-list .context-menu-item {
  padding: 0.65rem 1.28rem;
  color: #6e6b7b; }

.context-menu-list .context-menu-item.context-menu-submenu:after {
  border-color: transparent transparent transparent #6e6b7b; }

.context-menu-list .context-menu-item.context-menu-hover, .context-menu-list .context-menu-item:hover, .context-menu-list .context-menu-item:focus {
  background-color: rgba(115, 103, 240, 0.12) !important;
  color: #7367f0; }

.context-menu-list .context-menu-item.context-menu-hover.context-menu-submenu:after, .context-menu-list .context-menu-item:hover.context-menu-submenu:after, .context-menu-list .context-menu-item:focus.context-menu-submenu:after {
  border-color: transparent transparent transparent #7367f0 !important; }

.context-menu-list .context-menu-item:focus {
  outline: 0; }

.dark-layout .context-menu-list {
  background-color: #161d31;
  border-color: #3b4253; }

.dark-layout .context-menu-list .context-menu-item {
  background-color: #161d31; }

.dark-layout .context-menu-list .context-menu-item span {
  color: #b4b7bd; }

.dark-layout .context-menu-list .context-menu-item.context-menu-hover > span {
  color: #7367f0; }

.dark-layout .context-menu-list .context-menu-item.context-menu-submenu:after {
  border-color: transparent transparent transparent #b4b7bd; }

[data-textdirection='rtl'] .context-menu-list {
  z-index: 1031 !important; }

[data-textdirection='rtl'] .context-menu-list .context-menu-item.context-menu-submenu:after {
  transform: rotate(180deg);
  top: 1.2rem;
  right: 1rem;
  left: auto;
  border-color: transparent #6e6b7b transparent transparent; }

[data-textdirection='rtl'] .context-menu-list .context-menu-item.context-menu-hover.context-menu-submenu:after {
  border-color: transparent #7367f0 transparent transparent !important; }

[data-textdirection='rtl'] .context-menu-list .context-menu-item > .context-menu-list {
  left: 100%;
  margin-left: 0; }

[data-textdirection='rtl'] .dark-layout .context-menu-list .context-menu-item.context-menu-submenu:after {
  border-color: transparent #b4b7bd transparent transparent; }
