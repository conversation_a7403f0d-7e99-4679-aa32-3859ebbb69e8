$((function(){"use strict";var e=$(".credit-card-mask"),a=$(".phone-number-mask"),n=$(".date-mask"),t=$(".time-mask"),l=$(".numeral-mask"),s=$(".block-mask"),m=$(".delimiter-mask"),r=$(".custom-delimiter-mask"),i=$(".prefix-mask");e.length&&e.each((function(){new Cleave($(this),{creditCard:!0})})),a.length&&new Cleave(a,{phone:!0,phoneRegionCode:"US"}),n.length&&new Cleave(n,{date:!0,delimiter:"-",datePattern:["Y","m","d"]}),t.length&&new Cleave(t,{time:!0,timePattern:["h","m","s"]}),l.length&&new Cleave(l,{numeral:!0,numeralThousandsGroupStyle:"thousand"}),s.length&&new Cleave(s,{blocks:[4,3,3],uppercase:!0}),m.length&&new Cleave(m,{delimiter:"·",blocks:[3,3,3],uppercase:!0}),r.length&&new Cleave(r,{delimiters:[".",".","-"],blocks:[3,3,3,2],uppercase:!0}),i.length&&new Cleave(i,{prefix:"+63",blocks:[3,3,3,4],uppercase:!0})}));