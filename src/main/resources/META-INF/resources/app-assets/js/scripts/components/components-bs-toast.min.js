!function(t,e,o){"use strict";var r=e.querySelector(".basic-toast"),a=e.querySelector(".toast-basic-toggler"),s=new bootstrap.Toast(r);a.addEventListener("click",(function(){s.show()}));[].slice.call(e.querySelectorAll(".toast")).map((function(t){return new bootstrap.Toast(t)}));var c=e.querySelector(".toast-autohide"),n=e.querySelector(".toast-autohide-toggler"),i=new bootstrap.Toast(c,{autohide:!1});n.addEventListener("click",(function(){i.show()}));var u=e.querySelector(".toast-stacked"),l=e.querySelector(".toast-stacked-toggler"),d=new bootstrap.Toast(u);l.addEventListener("click",(function(){d.show()}))}(window,document,jQuery);