$((function(){var e=$(".phone-number-mask"),t=new bootstrap.Modal(document.getElementById("twoFactorAuthModal")),o=new bootstrap.Modal(document.getElementById("twoFactorAuthAppsModal")),n=new bootstrap.Modal(document.getElementById("twoFactorAuthSmsModal"));document.getElementById("nextStepAuth").onclick=function(){"apps-auth"===document.querySelector("input[name=twoFactorAuthRadio]:checked").value?(t.hide(),o.show()):(t.hide(),n.show())},e.length&&e.each((function(){new Cleave($(this),{phone:!0,phoneRegionCode:"US"})}))}));